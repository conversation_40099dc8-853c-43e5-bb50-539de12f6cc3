# PLC 工業控制系統





## 📋 專案概述

PLC 工業控制系統是一個基於 Vue 3 + TypeScript 的現代化工業監控平台，專為高雄火車站商業大樓電力管理系統設計。系統提供即時監控、數據分析、警報管理、用戶權限控制等完整功能。

## 🚀 快速開始

### 系統要求
- **Node.js**: >= 18.0.0 (建議使用 LTS 版本)
- **npm**: >= 8.0.0 (或使用 pnpm >= 7.0.0)
- **瀏覽器**: Chrome >= 88, Firefox >= 85, Safari >= 14

### 安裝與啟動

```bash
# 進入專案目錄
cd vue-pure-admin-main

# 安裝依賴
npm install

# 啟動開發服務器
npm run dev
```

服務器將在 `http://localhost:8855` 啟動

### 登入系統
- **網址**: `http://localhost:8855`
- **帳號**: <EMAIL>
- **密碼**: 111111

## 📚 完整文檔

**所有詳細的技術文檔、API規格、部署指南、開發規範都已整合到：**

👉 **[vue-pure-admin-main/plc_System.MD](./vue-pure-admin-main/plc_System.MD)**

該文檔包含：
- 📊 完整的系統架構說明
- 🔧 API 整合狀態和規範
- 📋 功能模組完整對比
- 🚀 部署與測試指南
- 📝 開發規範與約束
- 📖 詳細的專案說明
- 🔒 後端相容性約束
- 📋 實施檢查清單
- 📊 前端轉移清單

## ✨ 主要功能

### 🚨 警報系統
- 即時警報監控、歷史記錄、可靠性分析

### 💾 資料庫管理
- 即時資料監控、歷史查詢、自訂報表

### 🖥️ GUI 監控系統
- 圖形化編輯器、監控畫面顯示

### 📢 通知系統
- 群組管理、訊息管理、通知設定

### 📅 排程系統
- 行事曆功能、工作排程

### ⚙️ 系統管理
- 帳單管理、BTU管理、CCTV管理、水費管理

### 🏷️ 標籤管理
- 通道管理、設備管理、群組管理、區域管理

### 👥 使用者管理
- 使用者列表、角色管理

## 🎯 專案狀態

- ✅ **功能完成度**: 100% (25/25 模組)
- ✅ **API整合**: 95% 完成
- ✅ **模擬數據消除**: 100%
- ✅ **系統穩定性**: 良好
- 🟢 **狀態**: Ready for Production

## 🛠️ 技術棧

- **前端**: Vue 3 + TypeScript + Element Plus
- **狀態管理**: Pinia
- **構建工具**: Vite
- **後端**: ASP.NET Core
- **即時通訊**: SignalR
- **認證**: JWT Bearer Token

## 📞 支援

如需技術支援或有任何問題，請參考完整文檔或聯繫開發團隊。

---

**最後更新**: 2025-01-27  
**版本**: 2.0  
**狀態**: 🟢 Production Ready
