# PLC 功能套用 - 清理和設置腳本
# 此腳本將清理 vue-pure-admin-main 的示例內容並設置 PLC 目錄結構

Write-Host "開始執行 PLC 功能套用腳本..." -ForegroundColor Green

# 檢查是否在正確的目錄
if (-not (Test-Path "src/main.ts")) {
    Write-Host "錯誤：請在 vue-pure-admin-main 根目錄執行此腳本" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 確認在正確的專案目錄" -ForegroundColor Green

# 第一階段：清理示例內容
Write-Host "`n🗑️ 第一階段：清理示例內容..." -ForegroundColor Yellow

# 1.1 刪除示例頁面目錄
Write-Host "刪除示例頁面目錄..." -ForegroundColor Cyan
$viewsToDelete = @(
    "src/views/able",
    "src/views/about", 
    "src/views/account-settings",
    "src/views/chatai",
    "src/views/codemirror",
    "src/views/components",
    "src/views/editor",
    "src/views/empty",
    "src/views/flow-chart",
    "src/views/ganttastic",
    "src/views/guide",
    "src/views/list",
    "src/views/markdown",
    "src/views/menuoverflow",
    "src/views/monitor",
    "src/views/nested",
    "src/views/permission",
    "src/views/result",
    "src/views/schema-form",
    "src/views/system",
    "src/views/table",
    "src/views/tabs",
    "src/views/vue-flow"
)

foreach ($dir in $viewsToDelete) {
    if (Test-Path $dir) {
        Remove-Item -Recurse -Force $dir
        Write-Host "  ❌ 已刪除: $dir" -ForegroundColor Gray
    }
}

# 1.2 刪除示例路由文件
Write-Host "刪除示例路由文件..." -ForegroundColor Cyan
$routesToDelete = @(
    "src/router/modules/able.ts",
    "src/router/modules/about.ts",
    "src/router/modules/board.ts",
    "src/router/modules/chatai.ts",
    "src/router/modules/codemirror.ts",
    "src/router/modules/components.ts",
    "src/router/modules/editor.ts",
    "src/router/modules/flowchart.ts",
    "src/router/modules/form.ts",
    "src/router/modules/formdesign.ts",
    "src/router/modules/ganttastic.ts",
    "src/router/modules/guide.ts",
    "src/router/modules/list.ts",
    "src/router/modules/markdown.ts",
    "src/router/modules/menuoverflow.ts",
    "src/router/modules/mind.ts",
    "src/router/modules/nested.ts",
    "src/router/modules/ppt.ts",
    "src/router/modules/result.ts",
    "src/router/modules/table.ts",
    "src/router/modules/vueflow.ts"
)

foreach ($file in $routesToDelete) {
    if (Test-Path $file) {
        Remove-Item -Force $file
        Write-Host "  ❌ 已刪除: $file" -ForegroundColor Gray
    }
}

# 1.3 刪除示例 API 文件
Write-Host "刪除示例 API 文件..." -ForegroundColor Cyan
$apisToDelete = @(
    "src/api/list.ts",
    "src/api/mock.ts", 
    "src/api/routes.ts",
    "src/api/system.ts",
    "src/api/user.ts"
)

foreach ($file in $apisToDelete) {
    if (Test-Path $file) {
        Remove-Item -Force $file
        Write-Host "  ❌ 已刪除: $file" -ForegroundColor Gray
    }
}

# 第二階段：創建 PLC 目錄結構
Write-Host "`n📦 第二階段：創建 PLC 目錄結構..." -ForegroundColor Yellow

# 2.1 創建 PLC 頁面目錄
Write-Host "創建 PLC 頁面目錄..." -ForegroundColor Cyan
$plcViewDirs = @(
    "src/views/plc/alarm",
    "src/views/plc/database", 
    "src/views/plc/gui",
    "src/views/plc/notify",
    "src/views/plc/schedule",
    "src/views/plc/system",
    "src/views/plc/tags",
    "src/views/plc/user",
    "src/views/plc/auth",
    "src/views/plc/dashboard"
)

foreach ($dir in $plcViewDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  ✅ 已創建: $dir" -ForegroundColor Green
    }
}

# 2.2 創建 PLC API 目錄和文件
Write-Host "創建 PLC API 文件..." -ForegroundColor Cyan
if (-not (Test-Path "src/api/plc")) {
    New-Item -ItemType Directory -Path "src/api/plc" -Force | Out-Null
}

$plcApiFiles = @(
    "src/api/plc/alarm.ts",
    "src/api/plc/database.ts",
    "src/api/plc/gui.ts", 
    "src/api/plc/notify.ts",
    "src/api/plc/schedule.ts",
    "src/api/plc/system.ts",
    "src/api/plc/tags.ts",
    "src/api/plc/user.ts",
    "src/api/plc/auth.ts"
)

foreach ($file in $plcApiFiles) {
    if (-not (Test-Path $file)) {
        New-Item -ItemType File -Path $file -Force | Out-Null
        Write-Host "  ✅ 已創建: $file" -ForegroundColor Green
    }
}

# 2.3 創建 PLC Store 模組文件
Write-Host "創建 PLC Store 模組..." -ForegroundColor Cyan
$plcStoreFiles = @(
    "src/store/modules/plc-alarm.ts",
    "src/store/modules/plc-database.ts",
    "src/store/modules/plc-gui.ts",
    "src/store/modules/plc-notify.ts", 
    "src/store/modules/plc-schedule.ts",
    "src/store/modules/plc-system.ts",
    "src/store/modules/plc-tags.ts",
    "src/store/modules/plc-auth.ts"
)

foreach ($file in $plcStoreFiles) {
    if (-not (Test-Path $file)) {
        New-Item -ItemType File -Path $file -Force | Out-Null
        Write-Host "  ✅ 已創建: $file" -ForegroundColor Green
    }
}

# 2.4 創建 PLC 路由模組文件
Write-Host "創建 PLC 路由模組..." -ForegroundColor Cyan
$plcRouteFiles = @(
    "src/router/modules/plc-alarm.ts",
    "src/router/modules/plc-database.ts",
    "src/router/modules/plc-gui.ts",
    "src/router/modules/plc-notify.ts",
    "src/router/modules/plc-schedule.ts", 
    "src/router/modules/plc-system.ts",
    "src/router/modules/plc-tags.ts",
    "src/router/modules/plc-user.ts"
)

foreach ($file in $plcRouteFiles) {
    if (-not (Test-Path $file)) {
        New-Item -ItemType File -Path $file -Force | Out-Null
        Write-Host "  ✅ 已創建: $file" -ForegroundColor Green
    }
}

# 2.5 創建 PLC 組件目錄
Write-Host "創建 PLC 組件目錄..." -ForegroundColor Cyan
$plcComponentDirs = @(
    "src/components/PlcAlarm",
    "src/components/PlcDatabase",
    "src/components/PlcGui",
    "src/components/PlcNotify",
    "src/components/PlcSchedule",
    "src/components/PlcSystem", 
    "src/components/PlcTags",
    "src/components/PlcUser",
    "src/components/PlcCommon"
)

foreach ($dir in $plcComponentDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  ✅ 已創建: $dir" -ForegroundColor Green
    }
}

# 2.6 創建 PLC 工具目錄
Write-Host "創建 PLC 工具目錄..." -ForegroundColor Cyan
$plcUtilDirs = @(
    "src/utils/plc",
    "src/composables/plc"
)

foreach ($dir in $plcUtilDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  ✅ 已創建: $dir" -ForegroundColor Green
    }
}

# 創建基礎工具文件
$plcUtilFiles = @(
    "src/utils/plc/dataService.ts",
    "src/utils/plc/signalr.ts",
    "src/utils/plc/permission.ts",
    "src/composables/plc/usePermission.ts",
    "src/composables/plc/useTagInfo.ts",
    "src/composables/plc/useSignalR.ts"
)

foreach ($file in $plcUtilFiles) {
    if (-not (Test-Path $file)) {
        New-Item -ItemType File -Path $file -Force | Out-Null
        Write-Host "  ✅ 已創建: $file" -ForegroundColor Green
    }
}

# 第三階段：創建基礎模板文件
Write-Host "`n🔧 第三階段：創建基礎模板文件..." -ForegroundColor Yellow

# 創建基礎 API 模板
Write-Host "創建基礎 API 模板..." -ForegroundColor Cyan

# 創建 DataService 基礎模板
$dataServiceTemplate = @"
import { http } from '@/utils/http'

/**
 * PLC DataService - 統一的 API 請求服務
 */
export class DataService {
  private baseURL: string

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL
  }

  // GET 請求
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await http.get<T>(`$`{this.baseURL}$`{url}`, { params })
    return response.data
  }

  // POST 請求
  async post<T>(url: string, data?: any): Promise<T> {
    const response = await http.post<T>(`$`{this.baseURL}$`{url}`, data)
    return response.data
  }

  // PUT 請求
  async put<T>(url: string, data?: any): Promise<T> {
    const response = await http.put<T>(`$`{this.baseURL}$`{url}`, data)
    return response.data
  }

  // DELETE 請求
  async delete<T>(url: string): Promise<T> {
    const response = await http.delete<T>(`$`{this.baseURL}$`{url}`)
    return response.data
  }
}

export const dataService = new DataService()
"@

Set-Content -Path "src/utils/plc/dataService.ts" -Value $dataServiceTemplate -Encoding UTF8
Write-Host "  ✅ 已創建: DataService 模板" -ForegroundColor Green

Write-Host "`nPLC 功能套用腳本執行完成！" -ForegroundColor Green
Write-Host "`n接下來的步驟：" -ForegroundColor Yellow
Write-Host "1. 根據 'Vue-Pure-Admin-架構保留-PLC功能套用計劃.md' 進行核心功能改造" -ForegroundColor White
Write-Host "2. 參考 'PLC-Frontend-轉移更新清單.md' 實現各功能模組" -ForegroundColor White
Write-Host "3. 測試和優化各功能模組" -ForegroundColor White
Write-Host "`n祝您開發順利！" -ForegroundColor Green
