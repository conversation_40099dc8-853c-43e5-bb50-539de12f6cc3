<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警報設定條件顯示測試</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            color: #e6a23c;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn-primary { background: #409eff; color: white; }
        .btn-success { background: #67c23a; color: white; }
        .btn-warning { background: #e6a23c; color: white; }
        .btn-danger { background: #f56c6c; color: white; }
        .btn-info { background: #909399; color: white; }
        
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        .test-case {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid #409eff;
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #409eff;
        }
        .expected {
            background: #f0f9ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 3px solid #409eff;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #e6a23c;
            border-radius: 8px;
            margin-top: 15px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .step {
            display: flex;
            align-items: flex-start;
            margin: 12px 0;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #e6a23c;
        }
        .step-number {
            background: #e6a23c;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .credentials {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚠️ 警報設定完整功能測試</h1>
            <p>測試警報狀態選擇對設定欄位顯示的影響，包含新增的例外設定功能</p>
        </div>

        <div class="test-section">
            <h3>🎯 測試目標</h3>
            <div class="test-case">
                <h4>條件顯示邏輯</h4>
                <div class="expected">
                    <strong>✅ 停用 (0)：</strong> 不顯示任何警報設定欄位<br>
                    <strong>✅ 一般警報 (1)：</strong> 顯示所有警報設定欄位（包含例外設定）<br>
                    <strong>✅ 重要警報 (2)：</strong> 顯示所有警報設定欄位（包含例外設定）<br>
                    <strong>🆕 例外設定：</strong> 包含時間範圍、例外動作和說明
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 測試步驟</h3>
            <div class="step">
                <div class="step-number">1</div>
                <div>
                    <strong>登入系統</strong><br>
                    使用測試帳號登入 PLC 系統
                </div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div>
                    <strong>進入標籤管理</strong><br>
                    導航到標籤管理頁面
                </div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div>
                    <strong>打開編輯對話框</strong><br>
                    點擊任一標籤的"編輯"按鈕
                </div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div>
                    <strong>切換到警報設定</strong><br>
                    點擊"警報設定"標籤頁
                </div>
            </div>
            <div class="step">
                <div class="step-number">5</div>
                <div>
                    <strong>測試條件顯示</strong><br>
                    依序選擇不同的警報狀態，觀察設定欄位的顯示變化
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔑 登入資訊</h3>
            <div class="credentials">
                <strong>帳號：</strong> <EMAIL><br>
                <strong>密碼：</strong> 111111
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 快速操作</h3>
            <div class="button-group">
                <a href="http://localhost:8849/#/plc-login" target="_blank" class="btn btn-primary">
                    🌐 打開登入頁面
                </a>
                <a href="http://localhost:8849/#/plc-tags/tag" target="_blank" class="btn btn-success">
                    📊 直接打開標籤管理
                </a>
                <button class="btn btn-warning" onclick="loadTagPage()">
                    📱 在下方預覽中載入
                </button>
                <button class="btn btn-info" onclick="checkLoginStatus()">
                    🔍 檢查登入狀態
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 測試狀態</h3>
            <div id="status" class="status info">
                等待測試...
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 測試檢查清單</h3>
            <div class="test-case">
                <h4>停用狀態 (0)</h4>
                <div class="expected">
                    <input type="checkbox" id="check1"> 選擇"停用"後，警報設定欄位完全隱藏<br>
                    <input type="checkbox" id="check2"> 不顯示播放語音、通知群組等設定<br>
                    <input type="checkbox" id="check3"> 不顯示類比警報設定（HH, HI, LO, LL）<br>
                    <input type="checkbox" id="check4"> 不顯示數位警報設定<br>
                    <input type="checkbox" id="check5"> 不顯示例外設定
                </div>
            </div>
            <div class="test-case">
                <h4>一般警報狀態 (1)</h4>
                <div class="expected">
                    <input type="checkbox" id="check6"> 選擇"一般警報"後，所有警報設定欄位顯示<br>
                    <input type="checkbox" id="check7"> 顯示播放語音、通知群組、SOP等通用設定<br>
                    <input type="checkbox" id="check8"> 顯示類比警報設定（如果是類比標籤）<br>
                    <input type="checkbox" id="check9"> 顯示數位警報設定（如果是數位標籤）<br>
                    <input type="checkbox" id="check10"> 顯示例外設定區塊
                </div>
            </div>
            <div class="test-case">
                <h4>重要警報狀態 (2)</h4>
                <div class="expected">
                    <input type="checkbox" id="check11"> 選擇"重要警報"後，所有警報設定欄位顯示<br>
                    <input type="checkbox" id="check12"> 功能與"一般警報"相同，都顯示完整設定<br>
                    <input type="checkbox" id="check13"> 包含例外設定功能
                </div>
            </div>
            <div class="test-case">
                <h4>例外設定功能 (新增)</h4>
                <div class="expected">
                    <input type="checkbox" id="check14"> 例外設定有啟用/停用開關<br>
                    <input type="checkbox" id="check15"> 啟用後顯示開始時間和結束時間選擇器<br>
                    <input type="checkbox" id="check16"> 顯示例外動作選項（停止警報、延遲警報、降級警報）<br>
                    <input type="checkbox" id="check17"> 顯示例外說明輸入框<br>
                    <input type="checkbox" id="check18"> 停用後隱藏所有例外設定欄位
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🖥️ 系統預覽</h3>
            <div class="iframe-container">
                <iframe id="systemFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        const systemFrame = document.getElementById('systemFrame');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function checkLoginStatus() {
            const accessToken = localStorage.getItem('access_token');
            if (accessToken) {
                updateStatus(`✅ 已登入 - Token: ${accessToken.substring(0, 20)}...`, 'success');
            } else {
                updateStatus('❌ 未登入 - 請先登入系統', 'error');
            }
        }

        function loadTagPage() {
            systemFrame.src = 'http://localhost:8849/#/plc-tags/tag';
            updateStatus('📊 正在載入標籤管理頁面...', 'info');
        }

        // 監聽 iframe 載入事件
        systemFrame.onload = function() {
            updateStatus('✅ 頁面載入完成，可以開始測試警報設定', 'success');
        };

        // 初始化
        checkLoginStatus();
        updateStatus('🚀 警報設定測試工具已準備就緒', 'info');
    </script>
</body>
</html>
