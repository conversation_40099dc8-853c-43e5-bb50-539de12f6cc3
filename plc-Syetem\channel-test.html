<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 通道管理 API 測試</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #27ae60; }
        .status-error { background-color: #e74c3c; }
        .status-warning { background-color: #f39c12; }
        .status-info { background-color: #3498db; }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .iframe-container {
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        
        .console-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .console-output .timestamp {
            color: #95a5a6;
        }
        
        .console-output .success {
            color: #2ecc71;
        }
        
        .console-output .error {
            color: #e74c3c;
        }
        
        .console-output .info {
            color: #3498db;
        }
        
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 通道管理 API 測試</h1>
            <p>測試通道管理相關的 API 端點和數據載入功能</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>📋 功能對比與修復</h3>
                <p><span class="status-indicator status-success"></span><strong>已修復 plcDataService 導入問題</strong></p>
                <p><span class="status-indicator status-success"></span><strong>已修正 pagination 變數引用錯誤</strong></p>
                <p><span class="status-indicator status-success"></span><strong>已更新 API 端點為正確路徑</strong></p>
                <p><span class="status-indicator status-success"></span><strong>已修正數據解析邏輯</strong></p>
                <p><span class="status-indicator status-success"></span><strong>已實現動態選項載入</strong></p>
                <p><span class="status-indicator status-success"></span><strong>已匹配舊系統編輯功能</strong></p>

                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 4px;">
                    <strong>✅ 完全匹配舊系統功能：</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li><strong>狀態選擇</strong>：啟用/停用 radio buttons ✅</li>
                        <li><strong>Channel名稱</strong>：文本輸入框 ✅</li>
                        <li><strong>Channel Driver</strong>：動態載入選項 (TCP, OBIX, Desigo CC, 虛擬點) ✅</li>
                        <li><strong>資料獲取方式</strong>：動態載入選項 (OCO, OPC) ✅</li>
                        <li><strong>說明</strong>：文本輸入框 ✅</li>
                        <li><strong>操作按鈕</strong>：確認/取消 ✅</li>
                    </ul>
                </div>

                <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px;">
                    <strong>❌ 已移除的多餘功能：</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li><strong>進階設定</strong>：整個區塊已移除 ❌</li>
                        <li><strong>IP位址</strong>：文本輸入已移除 ❌</li>
                        <li><strong>連接埠</strong>：數字輸入已移除 ❌</li>
                        <li><strong>OBIX URL</strong>：文本輸入已移除 ❌</li>
                        <li><strong>掃描間隔</strong>：數字輸入已移除 ❌</li>
                        <li><strong>連線逾時</strong>：數字輸入已移除 ❌</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🧪 測試步驟</h3>
                <ol>
                    <li><strong>檢查控制台</strong> - 確認 "plcDataService is not defined" 錯誤已消失</li>
                    <li><strong>導航到通道管理</strong> - 點擊下方按鈕或直接訪問頁面</li>
                    <li><strong>觀察數據載入</strong> - 檢查是否成功載入通道列表</li>
                    <li><strong>驗證 API 響應</strong> - 在網路標籤中檢查 API 調用狀態</li>
                    <li><strong>測試功能</strong> - 嘗試新增、編輯通道功能</li>
                </ol>
            </div>
            
            <div class="action-buttons">
                <a href="http://localhost:8849/#/plc-tags/channel" class="btn btn-primary" target="_blank">
                    🔗 開啟通道管理頁面
                </a>
                <a href="http://localhost:8849/#/plc-test/api" class="btn btn-secondary" target="_blank">
                    🧪 開啟 API 測試頁面
                </a>
                <button class="btn btn-success" onclick="testChannelAPI()">
                    ⚡ 測試通道 API
                </button>
            </div>
            
            <div class="console-output" id="console">
                <div class="timestamp">[等待測試...]</div>
            </div>
            
            <div class="iframe-container">
                <iframe src="http://localhost:8849/#/plc-tags/channel" title="通道管理系統預覽"></iframe>
            </div>
        </div>
    </div>
    
    <script>
        function log(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> <span class="${type}">${message}</span>`;
            console.appendChild(logEntry);
            console.scrollTop = console.scrollHeight;
        }
        
        async function testChannelAPI() {
            log('🚀 開始測試通道 API...', 'info');
            
            try {
                // 檢查登入狀態
                const token = localStorage.getItem('plc-token');
                if (!token) {
                    log('❌ 未檢測到登入狀態，請先登入系統', 'error');
                    return;
                }
                
                log('✅ 檢測到登入狀態', 'success');
                
                // 測試通道 API
                const response = await fetch('/api/Tag/GetTagChannelListByCustomer', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`📡 API 響應狀態: ${response.status} ${response.statusText}`, 
                    response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📊 成功獲取數據: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.Detail && data.Detail.TagChannelList) {
                        log(`🎯 通道數量: ${data.Detail.TagChannelList.length}`, 'success');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ API 錯誤: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`💥 測試失敗: ${error.message}`, 'error');
            }
        }
        
        // 自動重新載入 iframe 以確保顯示最新內容
        setTimeout(() => {
            const iframe = document.querySelector('iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }, 2000);
        
        // 檢查登入狀態
        function checkLoginStatus() {
            const token = localStorage.getItem('plc-token');
            if (!token) {
                log('⚠️ 未檢測到登入狀態，請先登入系統', 'warning');
            } else {
                log('✅ 已檢測到登入狀態', 'success');
            }
        }
        
        // 頁面載入時檢查登入狀態
        window.addEventListener('load', checkLoginStatus);
    </script>
</body>
</html>
