// 調試編輯對話框問題的腳本
// 在瀏覽器控制台中運行此腳本

console.log('=== PLC 標籤編輯對話框調試工具 ===');

// 1. 檢查對話框狀態
function checkDialogStatus() {
  console.log('\n1. 檢查對話框元素:');
  
  const dialogs = document.querySelectorAll('.el-dialog');
  const overlays = document.querySelectorAll('.el-overlay');
  const dialogWrappers = document.querySelectorAll('.el-overlay-dialog');
  
  console.log(`找到 ${dialogs.length} 個對話框元素`);
  console.log(`找到 ${overlays.length} 個遮罩元素`);
  console.log(`找到 ${dialogWrappers.length} 個對話框包裝器`);
  
  dialogs.forEach((dialog, index) => {
    const style = window.getComputedStyle(dialog);
    console.log(`對話框 ${index}:`, {
      display: style.display,
      visibility: style.visibility,
      opacity: style.opacity,
      zIndex: style.zIndex
    });
  });
  
  return { dialogs, overlays, dialogWrappers };
}

// 2. 檢查 Vue 響應式狀態
function checkVueState() {
  console.log('\n2. 檢查 Vue 響應式狀態:');
  
  // 嘗試找到 Vue 應用實例
  const app = document.querySelector('#app').__vue_app__;
  if (app) {
    console.log('找到 Vue 應用實例');
    
    // 查找包含 showTagDialog 的組件
    const tagComponent = document.querySelector('[data-v-tag]');
    if (tagComponent && tagComponent.__vueParentComponent) {
      const component = tagComponent.__vueParentComponent;
      console.log('找到標籤組件:', component);
    }
  }
}

// 3. 手動觸發對話框顯示
function forceShowDialog() {
  console.log('\n3. 嘗試手動顯示對話框:');
  
  // 方法1: 直接設置樣式
  const dialogs = document.querySelectorAll('.el-dialog');
  dialogs.forEach((dialog, index) => {
    console.log(`嘗試顯示對話框 ${index}`);
    dialog.style.display = 'block';
    dialog.style.visibility = 'visible';
    dialog.style.opacity = '1';
    dialog.style.zIndex = '9999';
    
    const parent = dialog.parentElement;
    if (parent) {
      parent.style.display = 'block';
      parent.style.visibility = 'visible';
      parent.style.opacity = '1';
    }
  });
  
  // 方法2: 觸發 Element Plus 的顯示機制
  const overlays = document.querySelectorAll('.el-overlay');
  overlays.forEach(overlay => {
    overlay.style.display = 'block';
    overlay.style.visibility = 'visible';
    overlay.style.opacity = '1';
  });
}

// 4. 檢查編輯按鈕事件
function checkEditButtons() {
  console.log('\n4. 檢查編輯按鈕:');
  
  const editButtons = document.querySelectorAll('button:contains("編輯")');
  console.log(`找到 ${editButtons.length} 個編輯按鈕`);
  
  // 為編輯按鈕添加調試事件
  editButtons.forEach((button, index) => {
    button.addEventListener('click', function(e) {
      console.log(`編輯按鈕 ${index} 被點擊`);
      console.log('事件對象:', e);
      
      // 延遲檢查對話框狀態
      setTimeout(() => {
        checkDialogStatus();
      }, 100);
    });
  });
}

// 5. 修復警報狀態選項
function fixAlarmStatusOptions() {
  console.log('\n5. 修復警報狀態選項:');
  
  // 查找警報狀態選擇器
  const alarmSelects = document.querySelectorAll('el-select[v-model*="alarmStatus"]');
  console.log(`找到 ${alarmSelects.length} 個警報狀態選擇器`);
  
  // 這裡需要通過 Vue 組件來修改選項
  console.log('警報狀態應該包含三個選項:');
  console.log('- 停用 (value: 0)');
  console.log('- 一般警報 (value: 1)');
  console.log('- 重要警報 (value: 2)');
}

// 6. 主要調試函數
function debugTagDialog() {
  console.log('開始調試標籤編輯對話框...\n');
  
  const results = checkDialogStatus();
  checkVueState();
  checkEditButtons();
  fixAlarmStatusOptions();
  
  console.log('\n=== 調試完成 ===');
  console.log('如果對話框仍然不顯示，請運行: forceShowDialog()');
  
  return results;
}

// 7. 快速修復函數
function quickFix() {
  console.log('執行快速修復...');
  
  // 強制顯示所有隱藏的對話框
  forceShowDialog();
  
  // 檢查結果
  setTimeout(() => {
    const dialogs = document.querySelectorAll('.el-dialog');
    const visibleDialogs = Array.from(dialogs).filter(d => 
      window.getComputedStyle(d).display !== 'none'
    );
    
    console.log(`修復後可見對話框數量: ${visibleDialogs.length}`);
    
    if (visibleDialogs.length > 0) {
      console.log('✅ 對話框修復成功！');
    } else {
      console.log('❌ 對話框仍然隱藏，可能需要檢查 Vue 組件狀態');
    }
  }, 500);
}

// 導出函數到全局作用域
window.debugTagDialog = debugTagDialog;
window.forceShowDialog = forceShowDialog;
window.quickFix = quickFix;
window.checkDialogStatus = checkDialogStatus;

console.log('調試工具已載入！');
console.log('可用命令:');
console.log('- debugTagDialog() - 完整調試');
console.log('- quickFix() - 快速修復');
console.log('- forceShowDialog() - 強制顯示對話框');
console.log('- checkDialogStatus() - 檢查對話框狀態');
