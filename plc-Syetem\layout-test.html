<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 警報設定排版優化測試</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
        }
        
        .optimization-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .optimization-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid #27ae60;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .optimization-item h4 {
            margin: 0 0 8px 0;
            color: #27ae60;
            font-size: 14px;
            font-weight: 600;
        }
        
        .optimization-item p {
            margin: 0;
            color: #666;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-box {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 2px solid #e9ecef;
        }
        
        .comparison-box.before {
            border-color: #e74c3c;
        }
        
        .comparison-box.after {
            border-color: #27ae60;
        }
        
        .comparison-box h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        
        .comparison-box.before h4 {
            color: #e74c3c;
        }
        
        .comparison-box.after h4 {
            color: #27ae60;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            font-size: 14px;
            color: #555;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
            margin-right: 5px;
        }
        
        .iframe-container {
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
            
            .optimization-list {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 警報設定排版優化完成</h1>
            <p>緊湊美觀的警報設定介面，提升使用者體驗</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>🚀 優化項目總覽</h3>
                <div class="optimization-list">
                    <div class="optimization-item">
                        <h4>間距優化</h4>
                        <p>減少卡片間距從 16px 到 8px，表單項目間距從 16px 到 8px，讓版面更緊湊</p>
                    </div>
                    <div class="optimization-item">
                        <h4>元件尺寸</h4>
                        <p>統一使用 size="default"，調整標籤寬度從 120px 到 100px</p>
                    </div>
                    <div class="optimization-item">
                        <h4>網格間距</h4>
                        <p>調整 el-row gutter 從 20px 到 12px，讓欄位間距更合適</p>
                    </div>
                    <div class="optimization-item">
                        <h4>卡片樣式</h4>
                        <p>新增 compact-card 樣式，減少內邊距，讓內容更集中</p>
                    </div>
                    <div class="optimization-item">
                        <h4>分隔線優化</h4>
                        <p>調整分隔線邊距，讓區塊間的視覺分隔更自然</p>
                    </div>
                    <div class="optimization-item">
                        <h4>標題樣式</h4>
                        <p>統一警報標題樣式，使用較小字體和適當顏色</p>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>📊 優化前後對比</h3>
                <div class="before-after">
                    <div class="comparison-box before">
                        <h4>❌ 優化前</h4>
                        <ul class="feature-list">
                            <li>間距過大，版面鬆散</li>
                            <li>卡片佔用過多垂直空間</li>
                            <li>表單項目間距不一致</li>
                            <li>整體視覺效果不夠緊湊</li>
                            <li>需要大量滾動才能看完所有設定</li>
                        </ul>
                    </div>
                    <div class="comparison-box after">
                        <h4>✅ 優化後</h4>
                        <ul class="feature-list">
                            <li>緊湊的間距設計</li>
                            <li>更高效的空間利用</li>
                            <li>一致的視覺風格</li>
                            <li>更好的使用者體驗</li>
                            <li>減少滾動，提高操作效率</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🎯 測試重點</h3>
                <p><strong>請特別注意以下優化效果：</strong></p>
                <ul class="feature-list">
                    <li><strong>例外設定</strong> - 新增的例外設定功能，包含時間選擇和動作設定</li>
                    <li><strong>警報卡片</strong> - 所有警報級別（HH, HI, LO, LL）使用一致的緊湊樣式</li>
                    <li><strong>數位警報</strong> - 警報狀態和復歸狀態的緊湊排版</li>
                    <li><strong>通用設定</strong> - 播放語音、通知群組、SOP 說明的優化佈局</li>
                    <li><strong>響應式設計</strong> - 在不同螢幕尺寸下的適應性</li>
                </ul>
            </div>
            
            <div class="action-buttons">
                <a href="http://localhost:8849/#/plc-tags/tag" class="btn btn-primary" target="_blank">
                    🔗 開啟測點管理頁面
                </a>
                <a href="http://localhost:8849/alarm-test.html" class="btn btn-secondary" target="_blank">
                    🧪 開啟警報測試頁面
                </a>
            </div>
            
            <div class="iframe-container">
                <iframe src="http://localhost:8849/#/plc-tags/tag" title="測點管理系統預覽"></iframe>
            </div>
        </div>
    </div>
    
    <script>
        // 自動重新載入 iframe 以確保顯示最新內容
        setTimeout(() => {
            const iframe = document.querySelector('iframe');
            if (iframe) {
                iframe.src = iframe.src;
            }
        }, 2000);
        
        // 檢查登入狀態
        function checkLoginStatus() {
            const token = localStorage.getItem('plc-token');
            if (!token) {
                console.log('⚠️ 未檢測到登入狀態，請先登入系統');
                alert('請先登入 PLC 系統才能查看測點管理頁面');
            } else {
                console.log('✅ 已檢測到登入狀態');
            }
        }
        
        // 頁面載入時檢查登入狀態
        window.addEventListener('load', checkLoginStatus);
    </script>
</body>
</html>
