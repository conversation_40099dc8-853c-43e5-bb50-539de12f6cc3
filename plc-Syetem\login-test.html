<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLC 登入測試</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            margin: 0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            color: #409eff;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn-primary {
            background: #409eff;
            color: white;
        }
        .btn-success {
            background: #67c23a;
            color: white;
        }
        .btn-warning {
            background: #e6a23c;
            color: white;
        }
        .btn-info {
            background: #909399;
            color: white;
        }
        .credentials {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .credentials strong {
            color: #856404;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .iframe-container {
            width: 100%;
            height: 500px;
            border: 2px solid #409eff;
            border-radius: 8px;
            margin-top: 20px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #409eff;
        }
        .step-number {
            background: #409eff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 PLC 登入跳轉測試</h1>
            <p>測試登入成功後的頁面跳轉功能</p>
        </div>

        <div class="test-section">
            <h3>📋 測試步驟</h3>
            <div class="step">
                <div class="step-number">1</div>
                <div>點擊下方 "打開 PLC 登入頁面" 按鈕</div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div>使用測試帳號登入系統</div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div>檢查是否成功跳轉到標籤管理頁面</div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div>測試編輯對話框功能</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔑 登入資訊</h3>
            <div class="credentials">
                <strong>帳號：</strong> <EMAIL><br>
                <strong>密碼：</strong> 111111
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 快速操作</h3>
            <div class="button-group">
                <a href="http://localhost:8849/#/plc-login" target="_blank" class="btn btn-primary">
                    🌐 打開 PLC 登入頁面
                </a>
                <a href="http://localhost:8849/#/plc-tags/tag" target="_blank" class="btn btn-success">
                    📊 直接打開標籤管理
                </a>
                <a href="http://localhost:8849/" target="_blank" class="btn btn-info">
                    🏠 打開系統首頁
                </a>
                <button class="btn btn-warning" onclick="clearStorage()">
                    🗑️ 清除登入狀態
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 測試狀態</h3>
            <div id="status" class="status info">
                等待測試...
            </div>
            <button class="btn btn-primary" onclick="checkLoginStatus()">
                🔍 檢查登入狀態
            </button>
        </div>

        <div class="test-section">
            <h3>🖥️ 系統預覽</h3>
            <div class="iframe-container">
                <iframe id="systemFrame" src="about:blank"></iframe>
            </div>
            <div class="button-group" style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="loadLoginPage()">載入登入頁面</button>
                <button class="btn btn-success" onclick="loadTagPage()">載入標籤管理</button>
                <button class="btn btn-info" onclick="loadHomePage()">載入首頁</button>
            </div>
        </div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        const systemFrame = document.getElementById('systemFrame');

        function updateStatus(message, type = 'info') {
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function checkLoginStatus() {
            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const customerId = localStorage.getItem('customer_id');

            if (accessToken) {
                updateStatus(`✅ 已登入 - Token: ${accessToken.substring(0, 20)}... | 客戶ID: ${customerId}`, 'success');
            } else {
                updateStatus('❌ 未登入 - 請先登入系統', 'error');
            }
        }

        function clearStorage() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('customer_id');
            localStorage.removeItem('brand_id');
            updateStatus('🗑️ 登入狀態已清除', 'info');
        }

        function loadLoginPage() {
            systemFrame.src = 'http://localhost:8849/#/plc-login';
            updateStatus('📱 正在載入登入頁面...', 'info');
        }

        function loadTagPage() {
            systemFrame.src = 'http://localhost:8849/#/plc-tags/tag';
            updateStatus('📊 正在載入標籤管理頁面...', 'info');
        }

        function loadHomePage() {
            systemFrame.src = 'http://localhost:8849/';
            updateStatus('🏠 正在載入系統首頁...', 'info');
        }

        // 監聽 iframe 載入事件
        systemFrame.onload = function() {
            const currentUrl = systemFrame.contentWindow?.location?.href || 'unknown';
            updateStatus(`✅ 頁面載入完成: ${currentUrl}`, 'success');
        };

        systemFrame.onerror = function() {
            updateStatus('❌ 頁面載入失敗', 'error');
        };

        // 初始化
        checkLoginStatus();
        updateStatus('🚀 PLC 登入測試工具已準備就緒', 'info');
    </script>
</body>
</html>
