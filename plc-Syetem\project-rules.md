# PLC-Syetem 專案開發規則文檔

## 📋 專案概述

**專案名稱**: PLC-Syetem 工業控制系統  
**版本**: 2.0  
**最後更新**: 2025-01-27  
**適用範圍**: 所有開發人員、測試人員、部署人員  

## 🎯 核心開發原則

### 1. 架構完整性原則
- **不得修改**: PLC-Syetem 核心架構和配置
- **不得破壞**: 現有的模組化設計結構
- **必須保持**: Vue 3 + TypeScript + Element Plus 技術棧
- **嚴格遵循**: 單一職責原則和依賴注入模式

### 2. 數據真實性原則
- **禁止使用**: 任何模擬數據或假數據
- **必須使用**: 真實的後端 API 數據
- **強制要求**: 所有數據來源必須可追溯
- **嚴格驗證**: API 響應數據的完整性和正確性

### 3. 功能完整性原則
- **不得創建**: 舊系統沒有的新功能
- **必須實現**: 舊系統的所有現有功能
- **確保匹配**: 新舊系統功能 100% 對應
- **維持一致**: 用戶體驗和操作流程

## 🔧 技術開發規範

### 1. 程式碼品質標準

#### TypeScript 使用規範
```typescript
// ✅ 正確：使用明確的類型定義
interface PlcAlarmData {
  id: number;
  message: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// ❌ 錯誤：使用 any 類型
const alarmData: any = {};
```

#### Vue 3 Composition API 規範
```vue
<script setup lang="ts">
// ✅ 正確：使用 Composition API
import { ref, computed, onMounted } from 'vue';
import { usePermission } from '@/composables/usePermission';

const { hasPermission } = usePermission();
const alarmList = ref<PlcAlarmData[]>([]);

// ❌ 錯誤：混用 Options API
</script>
```

### 2. API 整合規範

#### HTTP 請求標準
```typescript
// ✅ 正確：使用統一的 API 服務
import { plcApiService } from '@/api/plc';

const fetchAlarmData = async () => {
  try {
    const response = await plcApiService.getAlarms();
    return response.data;
  } catch (error) {
    console.error('API 請求失敗:', error);
    throw error;
  }
};

// ❌ 錯誤：直接使用 axios 或其他 HTTP 客戶端
```

#### SignalR 連接規範
```typescript
// ✅ 正確：使用統一的 SignalR 服務
import { signalRService } from '@/utils/signalr';

onMounted(() => {
  signalRService.connect();
  signalRService.on('AlarmUpdate', handleAlarmUpdate);
});

onUnmounted(() => {
  signalRService.disconnect();
});
```

### 3. 元件開發規範

#### 元件命名規範
- **頁面元件**: `PlcAlarmList.vue`, `PlcSystemDashboard.vue`
- **通用元件**: `PlcDataTable.vue`, `PlcPermissionButton.vue`
- **工具元件**: `PlcDatePicker.vue`, `PlcStatusIndicator.vue`

#### 元件結構規範
```vue
<template>
  <!-- 模板內容 -->
</template>

<script setup lang="ts">
// 1. 導入依賴
// 2. 定義 Props 和 Emits
// 3. 響應式數據
// 4. 計算屬性
// 5. 方法定義
// 6. 生命週期鉤子
</script>

<style scoped>
/* 樣式定義 */
</style>
```

## 🚫 嚴格禁止事項

### 1. 架構層面禁止
- **禁止修改**: `vite.config.ts` 核心配置
- **禁止更改**: 路由結構和導航邏輯
- **禁止替換**: Element Plus UI 組件庫
- **禁止移除**: TypeScript 類型檢查

### 2. 數據層面禁止
- **禁止使用**: Mock 數據或假數據
- **禁止創建**: 不存在的 API 端點
- **禁止忽略**: API 錯誤處理
- **禁止跳過**: 數據驗證步驟

### 3. UI/UX 層面禁止
- **禁止修改**: 現有的 UI 設計和佈局
- **禁止更改**: 用戶操作流程
- **禁止添加**: 未經授權的新功能
- **禁止移除**: 現有的功能模組

## ✅ 強制要求事項

### 1. 程式碼品質要求
- **必須通過**: ESLint 和 Prettier 檢查
- **必須包含**: 完整的 TypeScript 類型定義
- **必須編寫**: 單元測試和整合測試
- **必須添加**: 詳細的程式碼註釋

### 2. 文檔要求
- **必須更新**: README.md 和相關文檔
- **必須記錄**: API 變更和功能修改
- **必須維護**: 技術文檔的時效性
- **必須提供**: 部署和測試指南

### 3. 測試要求
- **必須執行**: 所有自動化測試
- **必須驗證**: API 整合的正確性
- **必須確認**: 功能的完整性
- **必須檢查**: 性能和穩定性

## 🔍 程式碼審查標準

### 1. 審查檢查清單
- [ ] TypeScript 類型定義完整
- [ ] API 整合正確無誤
- [ ] 錯誤處理機制完善
- [ ] 權限控制實施到位
- [ ] 性能優化措施得當
- [ ] 安全性考慮周全
- [ ] 文檔更新及時
- [ ] 測試覆蓋率充足

### 2. 審查流程
1. **自我審查**: 開發者自行檢查程式碼品質
2. **同儕審查**: 團隊成員交叉審查
3. **技術審查**: 技術負責人最終審查
4. **測試驗證**: QA 團隊功能測試
5. **部署批准**: 專案負責人部署批准

## 📊 品質指標

### 1. 程式碼品質指標
- **TypeScript 覆蓋率**: ≥ 95%
- **ESLint 通過率**: 100%
- **單元測試覆蓋率**: ≥ 80%
- **API 整合成功率**: ≥ 99%

### 2. 性能指標
- **頁面載入時間**: ≤ 3 秒
- **API 響應時間**: ≤ 500ms
- **記憶體使用量**: ≤ 100MB
- **CPU 使用率**: ≤ 30%

## 🚨 違規處理

### 1. 輕微違規
- **警告通知**: 口頭提醒和書面記錄
- **程式碼修正**: 立即修正違規程式碼
- **知識分享**: 團隊內部技術分享

### 2. 嚴重違規
- **程式碼回滾**: 立即回滾有問題的程式碼
- **重新開發**: 按照規範重新實施
- **額外審查**: 增加審查頻率和深度

## 📝 文檔維護

### 1. 文檔更新責任
- **開發人員**: 負責技術文檔更新
- **專案經理**: 負責專案文檔維護
- **QA 團隊**: 負責測試文檔更新

### 2. 文檔版本控制
- **版本標記**: 每次更新必須標記版本
- **變更記錄**: 詳細記錄變更內容
- **審查批准**: 文檔變更需要審查批准

---

**最後更新**: 2025-01-27  
**版本**: 1.0  
**負責人**: PLC-Syetem 開發團隊  
**審查人**: 專案技術負責人
