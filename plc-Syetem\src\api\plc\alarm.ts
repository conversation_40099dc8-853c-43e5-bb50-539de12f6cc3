import { plcDataService, PLCPagedResponse } from '@/utils/plc/dataService'

/**
 * 警報系統 API
 * 對應後端 AlarmController
 */

// 確認警報請求
export interface AcknowledgeAlarmRequest {
  AcknowledgeTarget?: number
  AlarmSummaryId?: string
}

// 確認警報響應
export interface AcknowledgeAlarmResponse {
  success: boolean
  message: string
}

// 警報摘要資料
export interface AlarmSummary {
  AlarmSummaryId: string
  TagName: string
  AlarmMessage: string
  AlarmTime: string
  AlarmLevel: number
  AlarmType: string
  IsAcknowledged: boolean
  AcknowledgedBy?: string
  AcknowledgedTime?: string
  CustomerId: string
  ServerIp: string
  TagId: string
  CurrentValue?: number
  AlarmValue?: number
  Unit?: string
}

// 警報列表請求
export interface GetAlarmListRequest {
  pageIndex?: number
  pageSize?: number
  startTime?: string
  endTime?: string
  alarmLevel?: number
  isAcknowledged?: boolean
  tagName?: string
  customerId?: string
}

// 警報歷史請求
export interface GetAlarmHistoryRequest {
  pageIndex?: number
  pageSize?: number
  startTime?: string
  endTime?: string
  alarmLevel?: number
  tagName?: string
  customerId?: string
}

/**
 * 警報 API 服務 - 對應實際後端 AlarmController
 */
export const alarmAPI = {
  /**
   * 確認警報
   * POST /Alarm/AcknowledgeAlarm
   * 參數格式: [FromForm] - 需要 FormData 格式
   */
  acknowledgeAlarm: (data: AcknowledgeAlarmRequest): Promise<AcknowledgeAlarmResponse> =>
    plcDataService.post('/Alarm/AcknowledgeAlarm', data),

  /**
   * 取得警報歷史結果 (實際存在的端點)
   * POST /Alarm/GetAlarmHistoryResult
   * 參數格式: [FromForm] - 需要 FormData 格式
   */
  getAlarmHistoryResult: (data: GetAlarmHistoryRequest): Promise<PLCPagedResponse<AlarmSummary>> =>
    plcDataService.post('/Alarm/GetAlarmHistoryResult', data),

  /**
   * 取得警報歷史參數
   * GET /Alarm/GetAlarmHistoryParameter
   * 參數格式: [FromQuery] - URL 參數格式
   */
  getAlarmHistoryParameter: (params: any): Promise<any> =>
    plcDataService.get('/Alarm/GetAlarmHistoryParameter', params),

  // 為了向後兼容，保留舊的方法名稱但指向正確的端點
  /**
   * @deprecated 使用 getAlarmHistoryResult 替代
   */
  getAlarmList: (params: GetAlarmListRequest): Promise<PLCPagedResponse<AlarmSummary>> =>
    plcDataService.post('/Alarm/GetAlarmHistoryResult', params),

  /**
   * @deprecated 使用 getAlarmHistoryResult 替代
   */
  getAlarmHistory: (params: GetAlarmHistoryRequest): Promise<PLCPagedResponse<AlarmSummary>> =>
    plcDataService.post('/Alarm/GetAlarmHistoryResult', params),

  /**
   * 取得警報統計
   * GET /Alarm/GetAlarmStatistics
   */
  getAlarmStatistics: (customerId?: string): Promise<{
    totalAlarms: number
    unacknowledgedAlarms: number
    criticalAlarms: number
    warningAlarms: number
    infoAlarms: number
  }> =>
    plcDataService.get('/Alarm/GetAlarmStatistics', { customerId }),

  /**
   * 批量確認警報
   * POST /Alarm/BatchAcknowledgeAlarms
   */
  batchAcknowledgeAlarms: (data: {
    alarmSummaryIds: string[]
    acknowledgeTarget?: number
  }): Promise<AcknowledgeAlarmResponse> =>
    plcDataService.post('/Alarm/BatchAcknowledgeAlarms', data),

  /**
   * 取得警報設定
   * GET /Alarm/GetAlarmSettings
   */
  getAlarmSettings: (customerId?: string): Promise<{
    enableSound: boolean
    enablePopup: boolean
    autoRefreshInterval: number
    maxDisplayCount: number
  }> =>
    plcDataService.get('/Alarm/GetAlarmSettings', { customerId }),

  /**
   * 更新警報設定
   * POST /Alarm/UpdateAlarmSettings
   */
  updateAlarmSettings: (data: {
    customerId?: string
    enableSound: boolean
    enablePopup: boolean
    autoRefreshInterval: number
    maxDisplayCount: number
  }): Promise<{ success: boolean; message: string }> =>
    plcDataService.post('/Alarm/UpdateAlarmSettings', data)
}