import { plcDataService } from '@/utils/plc/dataService'

/**
 * 認證相關 API
 * 對應後端 IdentityController
 */

// 登入請求
export interface LoginRequest {
  account: string
  password: string
}

// 登入響應
export interface LoginResponse {
  accessToken: string
  refreshToken: string
  expires: string
  userInfo: {
    customerId: string
    account: string
    staffId: string
    permission: number
    staffName: string
    roleId: string
    isRoot: boolean
  }
}

// 刷新 Token 請求
export interface RefreshTokenRequest {
  refreshToken: string
}

// 刷新 Token 響應
export interface RefreshTokenResponse {
  accessToken: string
  refreshToken: string
  expires: string
}

/**
 * 認證 API 服務
 */
export const authAPI = {
  /**
   * 用戶登入
   * POST /Identity/Login
   */
  login: (data: LoginRequest): Promise<LoginResponse> =>
    plcDataService.post('/Identity/Login', data),

  /**
   * 刷新 Token
   * POST /Identity/RefreshToken
   */
  refreshToken: (data: RefreshTokenRequest): Promise<RefreshTokenResponse> =>
    plcDataService.post('/Identity/RefreshToken', data),

  /**
   * 用戶登出
   * POST /Identity/Logout
   */
  logout: (): Promise<void> =>
    plcDataService.post('/Identity/Logout'),

  /**
   * 驗證 Token
   * GET /Identity/ValidateToken
   */
  validateToken: (): Promise<{ valid: boolean }> =>
    plcDataService.get('/Identity/ValidateToken'),

  /**
   * 獲取用戶資訊
   * GET /Identity/UserInfo
   */
  getUserInfo: (): Promise<LoginResponse['userInfo']> =>
    plcDataService.get('/Identity/UserInfo')
}