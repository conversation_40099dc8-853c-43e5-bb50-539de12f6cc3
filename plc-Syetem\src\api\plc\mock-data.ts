/**
 * PLC系統Mock數據服務
 * 提供模擬數據，用於開發和測試
 */

import type {
  Tag,
  TagType,
  DataType,
  Device,
  TagGroup,
  TagStatistics,
  PLCPagedResponse,
  GetTagsRequest
} from './tags'

// Mock標籤類型數據
export const mockTagTypes: TagType[] = [
  { value: 'AI', label: '類比輸入' },
  { value: 'AO', label: '類比輸出' },
  { value: 'DI', label: '數位輸入' },
  { value: 'DO', label: '數位輸出' },
  { value: 'CALC', label: '計算標籤' },
  { value: 'VIRTUAL', label: '虛擬標籤' }
]

// Mock資料類型數據
export const mockDataTypes: DataType[] = [
  { value: 'BOOL', label: '布林值' },
  { value: 'INT', label: '整數' },
  { value: 'REAL', label: '實數' },
  { value: 'STRING', label: '字串' },
  { value: 'DWORD', label: '雙字' },
  { value: 'WORD', label: '字' }
]

// Mock設備數據 - 基於高雄火車站商業大樓電力管理系統
export const mockDevices: Device[] = [
  { DeviceId: 'DEV001', DeviceName: '高雄火車站商業大樓電力管理系統通道.ECP-A電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV002', DeviceName: '高雄火車站商業大樓電力管理系統通道.ECP-C電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV003', DeviceName: '高雄火車站商業大樓電力管理系統通道.MB-B21電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV004', DeviceName: '高雄火車站商業大樓電力管理系統通道.LV-B01-1電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV005', DeviceName: '高雄火車站商業大樓電力管理系統通道.CB-B05電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV006', DeviceName: '高雄火車站商業大樓電力管理系統通道.MB-B11電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV007', DeviceName: '高雄火車站商業大樓電力管理系統通道.LV-B01-2電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV008', DeviceName: '高雄火車站商業大樓電力管理系統通道.LV-B04-2電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV009', DeviceName: '高雄火車站商業大樓電力管理系統通道.EECP-D電表', DeviceType: 'PowerMeter', IsOnline: true },
  { DeviceId: 'DEV010', DeviceName: '高雄火車站商業大樓電力管理系統通道.CB-B01電表', DeviceType: 'PowerMeter', IsOnline: true }
]

// Mock群組數據
export const mockGroups: TagGroup[] = [
  {
    GroupId: 'GRP001',
    GroupName: '生產線A',
    GroupDescription: '主要生產線監控點',
    IsActive: true,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z'
  },
  {
    GroupId: 'GRP002',
    GroupName: '生產線B',
    GroupDescription: '次要生產線監控點',
    IsActive: true,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z'
  },
  {
    GroupId: 'GRP003',
    GroupName: '公用設施',
    GroupDescription: '水電氣監控點',
    IsActive: true,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z'
  },
  {
    GroupId: 'GRP004',
    GroupName: '安全系統',
    GroupDescription: '安全監控點',
    IsActive: true,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z'
  },
  {
    GroupId: 'GRP005',
    GroupName: '環境監控',
    GroupDescription: '溫濕度監控點',
    IsActive: true,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z'
  }
]

// Mock標籤數據 - 基於高雄火車站商業大樓電力管理系統
export const mockTags: Tag[] = [
  {
    TagId: 'TAG001',
    TagName: 'ECP-A電表第1相電流',
    TagDescription: 'ECP-A電表第1相電流',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV001',
    DeviceName: '高雄火車站商業大樓電力管理系統通道.ECP-A電表',
    GroupId: 'GRP001',
    GroupName: '預設地區',
    Address: '1A',
    Unit: 'A',
    MinValue: 0,
    MaxValue: 1000,
    IsReadOnly: false,
    IsActive: true,
    AlarmEnabled: true,
    HighAlarmLimit: 800,
    LowAlarmLimit: 0,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z',
    UpdatedTime: new Date().toISOString()
  },
  {
    TagId: 'TAG002',
    TagName: 'ECP-C電表第1相電流',
    TagDescription: 'ECP-C電表第1相電流',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV002',
    DeviceName: '高雄火車站商業大樓電力管理系統通道.ECP-C電表',
    GroupId: 'GRP001',
    GroupName: '預設地區',
    Address: '1A',
    Unit: 'A',
    MinValue: 0,
    MaxValue: 1000,
    IsReadOnly: false,
    IsActive: true,
    AlarmEnabled: true,
    HighAlarmLimit: 800,
    LowAlarmLimit: 0,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z',
    UpdatedTime: new Date().toISOString()
  },
  {
    TagId: 'TAG003',
    TagName: 'MB-B21電表第1相電流',
    TagDescription: 'MB-B21電表第1相電流',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV003',
    DeviceName: '高雄火車站商業大樓電力管理系統通道.MB-B21電表',
    GroupId: 'GRP001',
    GroupName: '預設地區',
    Address: '1A',
    Unit: 'A',
    MinValue: 0,
    MaxValue: 1000,
    IsReadOnly: false,
    IsActive: true,
    AlarmEnabled: true,
    HighAlarmLimit: 800,
    LowAlarmLimit: 0,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z',
    UpdatedTime: new Date().toISOString()
  },
  {
    TagId: 'TAG004',
    TagName: 'LV-B01-1電表第1相電流',
    TagDescription: 'LV-B01-1電表第1相電流',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV004',
    DeviceName: '高雄火車站商業大樓電力管理系統通道.LV-B01-1電表',
    GroupId: 'GRP001',
    GroupName: '預設地區',
    Address: '1A',
    Unit: 'A',
    MinValue: 0,
    MaxValue: 1000,
    IsReadOnly: false,
    IsActive: true,
    AlarmEnabled: true,
    HighAlarmLimit: 800,
    LowAlarmLimit: 0,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z',
    UpdatedTime: new Date().toISOString()
  },
  {
    TagId: 'TAG005',
    TagName: 'CB-B05電表第1相電流',
    TagDescription: 'CB-B05電表第1相電流',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV005',
    DeviceName: '高雄火車站商業大樓電力管理系統通道.CB-B05電表',
    GroupId: 'GRP001',
    GroupName: '預設地區',
    Address: '1A',
    Unit: 'A',
    MinValue: 0,
    MaxValue: 1000,
    IsReadOnly: false,
    IsActive: true,
    AlarmEnabled: true,
    HighAlarmLimit: 800,
    LowAlarmLimit: 0,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z',
    UpdatedTime: new Date().toISOString()
  },
  {
    TagId: 'TAG006',
    TagName: 'MB-B11電表第1相電流',
    TagDescription: 'MB-B11電表第1相電流',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV006',
    DeviceName: '高雄火車站商業大樓電力管理系統通道.MB-B11電表',
    GroupId: 'GRP001',
    GroupName: '預設地區',
    Address: '1A',
    Unit: 'A',
    MinValue: 0,
    MaxValue: 1000,
    IsReadOnly: false,
    IsActive: true,
    AlarmEnabled: true,
    HighAlarmLimit: 800,
    LowAlarmLimit: 0,
    CustomerId: 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
    CreatedTime: '2024-01-01T00:00:00Z',
    UpdatedTime: new Date().toISOString()
  },
  {
    TagId: 'TAG003',
    TagName: 'FLOW_001',
    TagDescription: '生產線A流量',
    TagType: 'AI',
    DataType: 'REAL',
    DeviceId: 'DEV004',
    GroupId: 'GRP001',
    Address: '40003',
    Unit: 'L/min',
    MinValue: 0,
    MaxValue: 1000,
    CurrentValue: 150.8,
    IsActive: true,
    LastUpdateTime: new Date().toISOString(),
    CreateTime: '2024-01-01T00:00:00Z',
    UpdateTime: new Date().toISOString()
  },
  {
    TagId: 'TAG004',
    TagName: 'MOTOR_001_RUN',
    TagDescription: '馬達1運行狀態',
    TagType: 'DI',
    DataType: 'BOOL',
    DeviceId: 'DEV001',
    GroupId: 'GRP001',
    Address: '10001',
    Unit: '',
    MinValue: 0,
    MaxValue: 1,
    CurrentValue: 1,
    IsActive: true,
    LastUpdateTime: new Date().toISOString(),
    CreateTime: '2024-01-01T00:00:00Z',
    UpdateTime: new Date().toISOString()
  },
  {
    TagId: 'TAG005',
    TagName: 'VALVE_001_CMD',
    TagDescription: '閥門1控制命令',
    TagType: 'DO',
    DataType: 'BOOL',
    DeviceId: 'DEV001',
    GroupId: 'GRP001',
    Address: '00001',
    Unit: '',
    MinValue: 0,
    MaxValue: 1,
    CurrentValue: 0,
    IsActive: true,
    LastUpdateTime: new Date().toISOString(),
    CreateTime: '2024-01-01T00:00:00Z',
    UpdateTime: new Date().toISOString()
  }
]

// Mock統計數據
export const mockStatistics: TagStatistics = {
  totalTags: mockTags.length,
  activeTags: mockTags.filter(tag => tag.IsActive).length,
  inactiveTags: mockTags.filter(tag => !tag.IsActive).length,
  onlineDevices: mockDevices.filter(device => device.IsOnline).length,
  offlineDevices: mockDevices.filter(device => !device.IsOnline).length,
  totalDevices: mockDevices.length,
  totalGroups: mockGroups.length
}

/**
 * Mock API服務類
 */
export class MockApiService {
  private static delay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  static async getTags(params: GetTagsRequest): Promise<PLCPagedResponse<Tag>> {
    await this.delay()
    
    let filteredTags = [...mockTags]
    
    // 應用篩選條件
    if (params.tagName) {
      filteredTags = filteredTags.filter(tag => 
        tag.TagName.toLowerCase().includes(params.tagName!.toLowerCase()) ||
        tag.TagDescription.toLowerCase().includes(params.tagName!.toLowerCase())
      )
    }
    
    if (params.tagType) {
      filteredTags = filteredTags.filter(tag => tag.TagType === params.tagType)
    }
    
    if (params.dataType) {
      filteredTags = filteredTags.filter(tag => tag.DataType === params.dataType)
    }
    
    if (params.deviceId) {
      filteredTags = filteredTags.filter(tag => tag.DeviceId === params.deviceId)
    }
    
    if (params.groupId) {
      filteredTags = filteredTags.filter(tag => tag.GroupId === params.groupId)
    }
    
    if (params.isActive !== undefined) {
      filteredTags = filteredTags.filter(tag => tag.IsActive === params.isActive)
    }
    
    // 分頁處理
    const startIndex = (params.pageIndex - 1) * params.pageSize
    const endIndex = startIndex + params.pageSize
    const pagedTags = filteredTags.slice(startIndex, endIndex)
    
    return {
      data: pagedTags,
      total: filteredTags.length,
      pageIndex: params.pageIndex,
      pageSize: params.pageSize,
      totalPages: Math.ceil(filteredTags.length / params.pageSize)
    }
  }

  static async getTagTypes(): Promise<TagType[]> {
    await this.delay(200)
    return mockTagTypes
  }

  static async getDataTypes(): Promise<DataType[]> {
    await this.delay(200)
    return mockDataTypes
  }

  static async getDevices(): Promise<Device[]> {
    await this.delay(200)
    return mockDevices
  }

  static async getGroups(): Promise<Group[]> {
    await this.delay(200)
    return mockGroups
  }

  static async getTagStatistics(): Promise<TagStatistics> {
    await this.delay(200)
    return mockStatistics
  }

  static async createTag(tag: Partial<Tag>): Promise<Tag> {
    await this.delay(800)
    const newTag: Tag = {
      TagId: `TAG${String(mockTags.length + 1).padStart(3, '0')}`,
      TagName: tag.TagName || '',
      TagDescription: tag.TagDescription || '',
      TagType: tag.TagType || 'AI',
      DataType: tag.DataType || 'REAL',
      DeviceId: tag.DeviceId || '',
      GroupId: tag.GroupId || '',
      Address: tag.Address || '',
      Unit: tag.Unit || '',
      MinValue: tag.MinValue || 0,
      MaxValue: tag.MaxValue || 100,
      CurrentValue: tag.CurrentValue || 0,
      IsActive: tag.IsActive !== undefined ? tag.IsActive : true,
      LastUpdateTime: new Date().toISOString(),
      CreateTime: new Date().toISOString(),
      UpdateTime: new Date().toISOString()
    }
    mockTags.push(newTag)
    return newTag
  }

  static async updateTag(tagId: string, tag: Partial<Tag>): Promise<Tag> {
    await this.delay(800)
    const index = mockTags.findIndex(t => t.TagId === tagId)
    if (index === -1) {
      throw new Error('標籤不存在')
    }
    
    mockTags[index] = {
      ...mockTags[index],
      ...tag,
      TagId: tagId,
      UpdateTime: new Date().toISOString()
    }
    
    return mockTags[index]
  }

  static async deleteTag(tagId: string): Promise<void> {
    await this.delay(500)
    const index = mockTags.findIndex(t => t.TagId === tagId)
    if (index === -1) {
      throw new Error('標籤不存在')
    }
    mockTags.splice(index, 1)
  }
}
