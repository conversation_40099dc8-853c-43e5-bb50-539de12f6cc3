import { plcDataService } from "@/utils/plc/dataService";

/**
 * 通知群組相關介面
 */
export interface NotifyGroupItem {
  id: string;
  name: string;
  method: 'SMS' | 'LINE' | 'Email';
  memberCount: number;
  startTime: string;
  endTime: string;
  duration: number;
  status: 'active' | 'inactive';
  members: NotifyGroupMember[];
  createdAt: string;
  updatedAt: string;
}

export interface NotifyGroupMember {
  id: string;
  name: string;
  contact: string;
  type: 'phone' | 'email' | 'line';
}

export interface CreateNotifyGroupRequest {
  name: string;
  method: 'SMS' | 'LINE' | 'Email';
  startTime: string;
  endTime: string;
  duration: number;
  members: Omit<NotifyGroupMember, 'id'>[];
}

export interface UpdateNotifyGroupRequest extends Partial<CreateNotifyGroupRequest> {
  id: string;
}

export interface NotifyGroupSearchParams {
  name?: string;
  method?: string;
  status?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 通知訊息相關介面
 */
export interface NotifyMessageItem {
  id: string;
  title: string;
  content: string;
  groupId: string;
  groupName: string;
  method: 'SMS' | 'LINE' | 'Email';
  recipientCount: number;
  sendTime: string;
  status: 'success' | 'failed' | 'pending';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
}

export interface SendNotifyMessageRequest {
  groupId: string;
  method: 'SMS' | 'LINE' | 'Email';
  title: string;
  content: string;
  sendType: 'now' | 'scheduled';
  sendTime?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface NotifyMessageSearchParams {
  groupId?: string;
  method?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 通知設定相關介面
 */
export interface LineNotifySettings {
  enabled: boolean;
  accessToken: string;
  defaultGroup: string;
  timeout: number;
  retryCount: number;
}

export interface EmailNotifySettings {
  enabled: boolean;
  smtpServer: string;
  smtpPort: number;
  encryption: 'none' | 'ssl' | 'tls';
  username: string;
  password: string;
  senderName: string;
  senderEmail: string;
  timeout: number;
}

export interface SmsNotifySettings {
  enabled: boolean;
  provider: 'cht' | 'twm' | 'fet' | 'other';
  apiUrl: string;
  account: string;
  password: string;
  senderNumber: string;
  timeout: number;
}

export interface GeneralNotifySettings {
  defaultPriority: 'low' | 'medium' | 'high' | 'urgent';
  messageRetentionDays: number;
  maxRetryCount: number;
  retryInterval: number;
  enableLogging: boolean;
  logLevel: 'error' | 'warning' | 'info' | 'debug';
}

export interface NotifySettings {
  line: LineNotifySettings;
  email: EmailNotifySettings;
  sms: SmsNotifySettings;
  general: GeneralNotifySettings;
}

/**
 * 統計相關介面
 */
export interface NotifyStatistics {
  totalSent: number;
  successCount: number;
  failedCount: number;
  pendingCount: number;
  methodStats: {
    sms: number;
    line: number;
    email: number;
  };
  dailyStats: {
    date: string;
    count: number;
  }[];
}

/**
 * API 回應介面
 */
export interface NotifyApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface NotifyListResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 通知群組 API - 對應後端 MessageController
 */
export const notifyGroupAPI = {
  // 創建新的 SMS 群組 - 對應 POST /Message/CreateNewSmsGroup
  createSmsGroup: (data: {
    Name: string;
    StartTime: string;
    EndTime: string;
    InformMethod?: number;
    RealInformMethod: string;
  }) => {
    return plcDataService.post('/Message/CreateNewSmsGroup', data);
  },

  // 更新 SMS 群組 - 對應 POST /Message/UpdateSmsGroup
  updateSmsGroup: (data: {
    GroupId: string;
    RealGroupId: string;
    Name: string;
    StartTime: string;
    EndTime: string;
    InformMethod?: number;
  }) => {
    return plcDataService.post('/Message/UpdateSmsGroup', data);
  },

  // 刪除 SMS 群組 - 對應實際存在的 POST /Message/DeleteSmsGroup
  // 參數格式: [FromForm] - 需要 FormData 格式
  deleteSmsGroup: (data: {
    GroupId: string;
    RealGroupId: string;
  }) => {
    return plcDataService.post('/Message/DeleteSmsGroup', data);
  },

  // 獲取訊息群組詳細列表 - 對應實際存在的 GET /Message/GetMessageGroupDetailList
  // 參數格式: [FromQuery] - URL 參數格式
  getMessageGroupDetailList: (params?: any) => {
    return plcDataService.get('/Message/GetMessageGroupDetailList', params);
  },

  // LINE 服務管理 - 對應實際存在的端點
  // 獲取 LINE 服務列表 - 對應 GET /Message/GetLineServiceList
  getLineServiceList: (params?: any) => {
    return plcDataService.get('/Message/GetLineServiceList', params);
  },

  // 創建新 LINE 服務 - 對應 POST /Message/CreateNewLineService
  createNewLineService: (data: any) => {
    return plcDataService.post('/Message/CreateNewLineService', data);
  },

  // 更新 LINE 服務 - 對應 POST /Message/UpdateLineService
  updateLineService: (data: any) => {
    return plcDataService.post('/Message/UpdateLineService', data);
  },

  // 刪除 LINE 服務 - 對應 POST /Message/DeleteLineService
  deleteLineService: (data: any) => {
    return plcDataService.post('/Message/DeleteLineService', data);
  }
};

/**
 * 通知訊息 API - 對應後端 MessageController
 */
export const notifyMessageAPI = {
  // 創建 LINE Notify Access Token - 對應 POST /Message/CreateLineNotifyAccessToken
  createLineNotifyAccessToken: (data: {
    Code: string;
    State: string;
    StateAsChatRoomId: string;
  }) => {
    return plcDataService.post('/Message/CreateLineNotifyAccessToken', data);
  },

  // 發送訊息 (推測的 API)
  sendMessage: (data: {
    groupId: string;
    message: string;
    method: string;
  }) => {
    return plcDataService.post('/Message/SendMessage', data);
  },

  // 獲取訊息歷史列表 - 對應實際存在的 GET /Message/GetMessageHitoryList
  // 參數格式: [FromQuery] - URL 參數格式
  getMessageHistoryList: (params?: any) => {
    return plcDataService.get('/Message/GetMessageHitoryList', params);
  },

  // 獲取統計數據 (推測的 API - 可能需要後端實現)
  getStatistics: (params?: any) => {
    return plcDataService.get('/Message/GetMessageStatistics', params);
  }
};

/**
 * 通知設定 API - 對應後端配置和推測的 API
 */
export const notifySettingsAPI = {
  // 獲取 LINE Notify 設定 (推測的 API)
  getLineSettings: () => {
    return plcDataService.get('/Message/GetLineNotifySettings');
  },

  // 更新 LINE Notify 設定 (推測的 API)
  updateLineSettings: (data: LineNotifySettings) => {
    return plcDataService.post('/Message/UpdateLineNotifySettings', data);
  },

  // 獲取 Email 設定 (推測的 API)
  getEmailSettings: () => {
    return plcDataService.get('/Message/GetEmailSettings');
  },

  // 更新 Email 設定 (推測的 API)
  updateEmailSettings: (data: EmailNotifySettings) => {
    return plcDataService.post('/Message/UpdateEmailSettings', data);
  },

  // 獲取 SMS 設定 (推測的 API)
  getSmsSettings: () => {
    return plcDataService.get('/Message/GetSmsSettings');
  },

  // 更新 SMS 設定 (推測的 API)
  updateSmsSettings: (data: SmsNotifySettings) => {
    return plcDataService.post('/Message/UpdateSmsSettings', data);
  },

  // 測試 LINE 連線 (推測的 API)
  testLineConnection: (accessToken: string) => {
    return plcDataService.post('/Message/TestLineNotifyConnection', { accessToken });
  },

  // 測試 Email 連線 (推測的 API)
  testEmailConnection: (settings: Omit<EmailNotifySettings, 'enabled'>) => {
    return plcDataService.post('/Message/TestEmailConnection', settings);
  },

  // 測試 SMS 連線 (推測的 API)
  testSmsConnection: (settings: Omit<SmsNotifySettings, 'enabled'>) => {
    return plcDataService.post('/Message/TestSmsConnection', settings);
  }
};

/**
 * 通知系統狀態 API - 推測的 API
 */
export const notifySystemAPI = {
  // 獲取系統狀態 (推測的 API)
  getStatus: () => {
    return plcDataService.get('/Message/GetSystemStatus');
  },

  // 獲取系統統計 (推測的 API)
  getStats: () => {
    return plcDataService.get('/Message/GetSystemStats');
  }
};