import { plcDataService, PLCPagedResponse } from '@/utils/plc/dataService'
import { MockApiService } from './mock-data'

// 檢查是否使用Mock模式
const useMock = (): boolean => {
  return import.meta.env.VITE_USE_MOCK === 'true'
}

// 獲取客戶ID
const getCustomerId = (): string => {
  // 優先從localStorage獲取
  const storedCustomerId = localStorage.getItem('PLC_CUSTOMER_ID')
  if (storedCustomerId) {
    return storedCustomerId
  }

  // 其次從環境變數獲取
  const envCustomerId = import.meta.env.VITE_CUSTOMER_ID
  if (envCustomerId) {
    return envCustomerId
  }

  // 最後使用默認值
  return "fdff1878-a54a-44ee-b82c-a62bdc5cdb55"
}

// 設置客戶ID
export const setCustomerId = (customerId: string): void => {
  localStorage.setItem('PLC_CUSTOMER_ID', customerId)
  console.log('客戶ID已設置:', customerId)
}

/**
 * 標籤管理 API
 * 對應後端 TagController
 */

// 標籤資料類型
export interface Tag {
  TagId: string
  TagName: string
  TagDescription?: string
  TagType: string
  DataType: string
  Address: string
  DeviceId?: string
  DeviceName?: string
  GroupId?: string
  GroupName?: string
  Unit?: string
  MinValue?: number
  MaxValue?: number
  DefaultValue?: number
  IsReadOnly: boolean
  IsActive: boolean
  ScanRate?: number
  AlarmEnabled: boolean
  HighAlarmLimit?: number
  LowAlarmLimit?: number
  HighWarningLimit?: number
  LowWarningLimit?: number
  CustomerId: string
  CreatedTime: string
  UpdatedTime?: string
  CreatedBy?: string
  UpdatedBy?: string
}

// 設備資料類型
export interface Device {
  DeviceId: string
  DeviceName: string
  DeviceType: string
  DeviceDescription?: string
  IpAddress?: string
  Port?: number
  Protocol?: string
  IsActive: boolean
  CustomerId: string
  CreatedTime: string
  UpdatedTime?: string
}

// 群組資料類型
export interface TagGroup {
  GroupId: string
  GroupName: string
  GroupDescription?: string
  ParentGroupId?: string
  IsActive: boolean
  CustomerId: string
  CreatedTime: string
  UpdatedTime?: string
}

// 標籤查詢請求
export interface GetTagsRequest {
  pageIndex?: number
  pageSize?: number
  tagName?: string
  tagType?: string
  dataType?: string
  deviceId?: string
  groupId?: string
  isActive?: boolean
  customerId?: string
}

// 創建標籤請求
export interface CreateTagRequest {
  TagName: string
  TagDescription?: string
  TagType: string
  DataType: string
  Address: string
  DeviceId?: string
  GroupId?: string
  Unit?: string
  MinValue?: number
  MaxValue?: number
  DefaultValue?: number
  IsReadOnly: boolean
  IsActive: boolean
  ScanRate?: number
  AlarmEnabled: boolean
  HighAlarmLimit?: number
  LowAlarmLimit?: number
  HighWarningLimit?: number
  LowWarningLimit?: number
  CustomerId?: string
}

// 更新標籤請求
export interface UpdateTagRequest {
  TagId: string
  TagName: string
  TagDescription?: string
  TagType: string
  DataType: string
  Address: string
  DeviceId?: string
  GroupId?: string
  Unit?: string
  MinValue?: number
  MaxValue?: number
  DefaultValue?: number
  IsReadOnly: boolean
  IsActive: boolean
  ScanRate?: number
  AlarmEnabled: boolean
  HighAlarmLimit?: number
  LowAlarmLimit?: number
  HighWarningLimit?: number
  LowWarningLimit?: number
}

// 批量操作請求
export interface BatchTagOperationRequest {
  tagIds: string[]
  operation: 'activate' | 'deactivate' | 'delete' | 'export'
  targetGroupId?: string
  targetDeviceId?: string
}

// 匯入標籤請求
export interface ImportTagsRequest {
  file: File
  overwriteExisting: boolean
  customerId?: string
}

// 標籤統計資訊
export interface TagStatistics {
  totalTags: number
  activeTags: number
  inactiveTags: number
  alarmEnabledTags: number
  readOnlyTags: number
  deviceCount: number
  groupCount: number
}

/**
 * 標籤管理 API 服務
 */
export const tagsAPI = {
  /**
   * 取得標籤列表
   * GET /api/Tag/GetTagList
   */
  getTags: (params: GetTagsRequest): Promise<PLCPagedResponse<Tag>> => {
    if (useMock()) {
      return MockApiService.getTags(params)
    }
    const customerId = getCustomerId()
    const requestParams = { ...params, customerId }
    return plcDataService.get('/api/Tag/GetTagList', requestParams)
  },

  /**
   * 取得標籤詳情
   * GET /Tag/GetTag/{tagId}
   */
  getTag: (tagId: string): Promise<Tag> => {
    const customerId = getCustomerId()
    return plcDataService.get(`/Tag/GetTag/${tagId}`, { customerId })
  },

  /**
   * 創建標籤
   * POST /Tag/CreateTag
   */
  createTag: (data: CreateTagRequest): Promise<{ success: boolean; message: string; tagId?: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/Tag/CreateTag', requestData)
  },

  /**
   * 更新標籤
   * PUT /Tag/UpdateTag
   */
  updateTag: (data: UpdateTagRequest): Promise<{ success: boolean; message: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.put('/Tag/UpdateTag', requestData)
  },

  /**
   * 刪除標籤
   * DELETE /Tag/DeleteTag/{tagId}
   */
  deleteTag: (tagId: string): Promise<{ success: boolean; message: string }> => {
    const customerId = getCustomerId()
    return plcDataService.delete(`/Tag/DeleteTag/${tagId}?customerId=${customerId}`)
  },

  /**
   * 批量操作標籤
   * POST /Tag/BatchOperation
   */
  batchOperation: (data: BatchTagOperationRequest): Promise<{ success: boolean; message: string; affectedCount?: number }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/Tag/BatchOperation', requestData)
  },

  /**
   * 匯入標籤
   * POST /Tag/ImportTags
   */
  importTags: (data: ImportTagsRequest): Promise<{ success: boolean; message: string; importedCount?: number; errorCount?: number }> => {
    const customerId = getCustomerId()
    const formData = new FormData()
    formData.append('file', data.file)
    formData.append('overwriteExisting', data.overwriteExisting.toString())
    formData.append('customerId', customerId)
    return plcDataService.post('/Tag/ImportTags', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 匯出標籤
   * GET /Tag/ExportTags
   */
  exportTags: (params: { tagIds?: string[]; customerId?: string }): Promise<Blob> => {
    const customerId = getCustomerId()
    const requestParams = { ...params, customerId }
    return plcDataService.get('/Tag/ExportTags', requestParams, {
      responseType: 'blob'
    })
  },

  /**
   * 取得標籤統計
   * GET /api/Tag/GetTagStatistics
   */
  getTagStatistics: (customerId?: string): Promise<TagStatistics> => {
    if (useMock()) {
      return MockApiService.getTagStatistics()
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/api/Tag/GetTagStatistics', { customerId: customerIdToUse })
  },

  /**
   * 取得設備列表
   * GET /Tag/GetDeviceList (根據後端確認此API存在)
   */
  getDevices: (customerId?: string): Promise<Device[]> => {
    if (useMock()) {
      return MockApiService.getDevices()
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/Tag/GetDeviceList', { customerId: customerIdToUse })
  },

  /**
   * 取得設備分類階層列表
   * GET /Tag/GetDeviceCategoryHierarchyList
   */
  getDeviceCategoryHierarchy: (customerId?: string): Promise<any> => {
    if (useMock()) {
      return Promise.resolve([])
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/Tag/GetDeviceCategoryHierarchyList', { customerId: customerIdToUse })
  },

  /**
   * 新增設備分類
   * POST /Tag/CreateNewDeviceCategory
   */
  createDeviceCategory: (data: any): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewDeviceCategory', data)
  },

  /**
   * 更新設備分類
   * POST /Tag/UpdateDeviceCategory
   */
  updateDeviceCategory: (data: any): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateDeviceCategory', data)
  },

  /**
   * 刪除設備分類
   * POST /Tag/DeleteDeviceCategory
   */
  deleteDeviceCategory: (data: any): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteDeviceCategory', data)
  },

  /**
   * 刪除設備
   * POST /Tag/DeleteDevice
   */
  deleteDevice: (data: { DeviceId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteDevice', data)
  },

  /**
   * 刪除測點
   * POST /Tag/DeleteTag
   */
  deleteTag: (data: { TagId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteTag', data)
  },

  /**
   * 刪除測點分類
   * POST /Tag/DeleteTagCategory
   */
  deleteTagCategory: (data: { Id: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteTagCategory', data)
  },

  /**
   * 刪除地區
   * POST /Tag/DeleteRegion
   */
  deleteRegion: (data: { RegionId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteRegion', data)
  },

  /**
   * 刪除通道
   * POST /Tag/DeleteTagChannel
   */
  deleteTagChannel: (data: { TagChannelId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteTagChannel', data)
  },

  /**
   * 刪除群組
   * POST /Tag/DeleteGroup
   */
  deleteGroup: (data: { GroupId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/DeleteGroup', data)
  },

  // ==================== 新增/編輯功能 ====================

  /**
   * 新增測點分類
   * POST /Tag/CreateNewTagCategory
   */
  createTagCategory: (data: { Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewTagCategory', data)
  },

  /**
   * 更新測點分類
   * POST /Tag/UpdateTagCategory
   */
  updateTagCategory: (data: { Id: string; Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateTagCategory', data)
  },

  /**
   * 新增地區
   * POST /Tag/CreateNewRegion
   */
  createRegion: (data: { Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewRegion', data)
  },

  /**
   * 更新地區
   * POST /Tag/UpdateRegion
   */
  updateRegion: (data: { Id: string; Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateRegion', data)
  },

  /**
   * 新增通道
   * POST /Tag/CreateNewTagChannel
   */
  createTagChannel: (data: { Name: string; Description?: string; ChannelType?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewTagChannel', data)
  },

  /**
   * 更新通道
   * POST /Tag/UpdateTagChannel
   */
  updateTagChannel: (data: { Id: string; Name: string; Description?: string; ChannelType?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateTagChannel', data)
  },

  /**
   * 新增群組
   * POST /Tag/CreateNewGroup
   */
  createGroup: (data: { Name: string; GroupCategoryId?: string; Description?: string; TagIds?: string[] }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewGroup', data)
  },

  /**
   * 更新群組
   * POST /Tag/UpdateGroup
   */
  updateGroup: (data: { Id: string; Name: string; GroupCategoryId?: string; Description?: string; TagIds?: string[] }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateGroup', data)
  },

  /**
   * 新增群組分類
   * POST /Tag/CreateNewGroupCategory
   */
  createGroupCategory: (data: { Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewGroupCategory', data)
  },

  /**
   * 更新群組分類
   * POST /Tag/UpdateGroupCategory
   */
  updateGroupCategory: (data: { Id: string; Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateGroupCategory', data)
  },

  /**
   * 新增裝置
   * POST /Tag/CreateNewDevice
   */
  createDevice: (data: { Name: string; DeviceCategoryId?: string; TagChannelId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewDevice', data)
  },

  /**
   * 更新裝置
   * POST /Tag/UpdateDevice
   */
  updateDevice: (data: { Id: string; Name: string; DeviceCategoryId?: string; TagChannelId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateDevice', data)
  },

  /**
   * 新增裝置分類
   * POST /Tag/CreateNewDeviceCategory
   */
  createDeviceCategory: (data: { Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewDeviceCategory', data)
  },

  /**
   * 更新裝置分類
   * POST /Tag/UpdateDeviceCategory
   */
  updateDeviceCategory: (data: { Id: string; Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateDeviceCategory', data)
  },

  /**
   * 取得群組列表
   * GET /api/Tag/GetGroups
   */
  getGroups: (customerId?: string): Promise<TagGroup[]> => {
    if (useMock()) {
      return MockApiService.getGroups()
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/api/Tag/GetGroups', { customerId: customerIdToUse })
  },

  /**
   * 創建設備
   * POST /Tag/CreateDevice
   */
  createDevice: (data: {
    DeviceName: string
    DeviceType: string
    DeviceDescription?: string
    IpAddress?: string
    Port?: number
    Protocol?: string
    IsActive: boolean
    CustomerId?: string
  }): Promise<{ success: boolean; message: string; deviceId?: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/Tag/CreateDevice', requestData)
  },

  /**
   * 創建群組
   * POST /Tag/CreateGroup
   */
  createGroup: (data: {
    GroupName: string
    GroupDescription?: string
    ParentGroupId?: string
    IsActive: boolean
    CustomerId?: string
  }): Promise<{ success: boolean; message: string; groupId?: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/Tag/CreateGroup', requestData)
  },

  /**
   * 取得標籤類型選項
   * GET /api/Tag/GetTagTypes
   */
  getTagTypes: (): Promise<{ value: string; label: string }[]> => {
    if (useMock()) {
      return MockApiService.getTagTypes()
    }
    const customerId = getCustomerId()
    return plcDataService.get('/api/Tag/GetTagTypes', { customerId })
  },

  /**
   * 取得資料類型選項
   * GET /api/Tag/GetDataTypes
   */
  getDataTypes: (): Promise<{ value: string; label: string }[]> => {
    if (useMock()) {
      return MockApiService.getDataTypes()
    }
    const customerId = getCustomerId()
    return plcDataService.get('/api/Tag/GetDataTypes', { customerId })
  },

  /**
   * 驗證標籤名稱唯一性
   * GET /Tag/ValidateTagName
   */
  validateTagName: (tagName: string, excludeTagId?: string): Promise<{ isValid: boolean; message?: string }> => {
    const customerId = getCustomerId()
    return plcDataService.get('/Tag/ValidateTagName', { tagName, excludeTagId, customerId })
  },

  /**
   * 取得標籤值歷史
   * GET /Tag/GetTagValueHistory
   */
  getTagValueHistory: (params: {
    tagId: string
    startTime?: string
    endTime?: string
    pageIndex?: number
    pageSize?: number
  }): Promise<PLCPagedResponse<{
    TagId: string
    Value: any
    Quality: string
    Timestamp: string
  }>> => {
    const customerId = getCustomerId()
    const requestParams = { ...params, customerId }
    return plcDataService.get('/Tag/GetTagValueHistory', requestParams)
  },

  /**
   * 取得標籤即時值
   * GET /Tag/GetTagRealTimeValue
   */
  getTagRealTimeValue: (tagId: string): Promise<{
    TagId: string
    TagName: string
    Value: any
    Quality: string
    Timestamp: string
    Unit?: string
  }> => {
    const customerId = getCustomerId()
    return plcDataService.get(`/Tag/GetTagRealTimeValue/${tagId}`, { customerId })
  },

  /**
   * 寫入標籤值
   * POST /Tag/WriteTagValue
   */
  writeTagValue: (data: {
    TagId: string
    Value: any
    CustomerId?: string
  }): Promise<{ success: boolean; message: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/Tag/WriteTagValue', requestData)
  },

  // ==================== 狀態更新功能 ====================

  /**
   * 設定通道狀態
   * POST /Tag/SetTagChannelAbility
   */
  setChannelStatus: (data: { TagChannelId: string; TargetStatus: boolean }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/SetTagChannelAbility', data)
  },

  /**
   * 設定裝置狀態
   * POST /Tag/SetTagDeviceAbility
   */
  setDeviceStatus: (data: { DeviceId: string; TargetStatus: boolean }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/SetTagDeviceAbility', data)
  }
}

// 地區相關接口
export interface RegionItem {
  id: string
  name: string
  code?: string
  description?: string
  parentId?: string
  status: 'active' | 'inactive'
  hasChildren?: boolean
  childCount?: number
  sortOrder?: number
  createTime?: string
  children?: RegionItem[]
}

// 測點相關接口（新系統）
export interface TagItem {
  id: string
  name: string
  description?: string
  tagType: 'Analog' | 'Digital' | 'Calculated'
  dataType: 'Boolean' | 'Int16' | 'Int32' | 'Float' | 'Double' | 'String'
  address: string
  deviceId: string
  deviceName?: string
  unit?: string
  minValue?: number
  maxValue?: number
  defaultValue?: number
  scanRate?: number
  status: 'active' | 'inactive'
  isReadOnly?: boolean
  alarmEnabled?: boolean
  highAlarmLimit?: number
  lowAlarmLimit?: number
  highWarningLimit?: number
  lowWarningLimit?: number
  currentValue?: any
  updateTime?: string
}

// 測點分類接口
export interface TagClassItem {
  id: string
  name: string
  parentId?: string
  description?: string
  children?: TagClassItem[]
}

// 地區相關 API
export const regionAPI = {
  // 獲取地區列表
  getRegionList: (params: any) => plcDataService.get('/api/regions', { params }),

  // 創建地區
  createRegion: (data: any) => plcDataService.post('/api/regions', data),

  // 更新地區
  updateRegion: (id: string, data: any) => plcDataService.put(`/api/regions/${id}`, data),

  // 刪除地區
  deleteRegion: (id: string) => plcDataService.delete(`/api/regions/${id}`),

  // 獲取地區樹
  getRegionTree: () => plcDataService.get('/api/regions/tree')
}

// 測點相關 API（新系統）
export const tagAPI = {
  // 獲取測點列表
  getTagList: (params: any) => plcDataService.get('/api/tags', { params }),

  // 創建測點
  createTag: (data: any) => plcDataService.post('/api/tags', data),

  // 更新測點
  updateTag: (id: string, data: any) => plcDataService.put(`/api/tags/${id}`, data),

  // 刪除測點
  deleteTag: (id: string) => plcDataService.delete(`/api/tags/${id}`),

  // 獲取測點詳情
  getTagDetail: (id: string) => plcDataService.get(`/api/tags/${id}`),

  // 獲取測點分類樹
  getTagClassTree: () => plcDataService.get('/api/tags/classes/tree'),

  // 創建測點分類
  createTagClass: (data: any) => plcDataService.post('/api/tags/classes', data),

  // 更新測點分類
  updateTagClass: (id: string, data: any) => plcDataService.put(`/api/tags/classes/${id}`, data),

  // 刪除測點分類
  deleteTagClass: (id: string) => plcDataService.delete(`/api/tags/classes/${id}`)
}