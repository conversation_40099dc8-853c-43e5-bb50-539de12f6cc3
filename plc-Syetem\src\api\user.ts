import { http } from "@/utils/http";

export type UserResult = {
  success: boolean;
  data: {
    /** 用户名 */
    username: string;
    /** 当前登陆用户的角色 */
    roles: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 登录 - 調用舊系統API */
export const getLogin = async (data?: any) => {
  try {
    // 獲取客戶ID
    const getCustomerId = (): string => {
      const storedCustomerId = localStorage.getItem('PLC_CUSTOMER_ID')
      if (storedCustomerId) {
        return storedCustomerId
      }
      // 從環境變數獲取
      if (typeof window !== 'undefined' && (window as any).process?.env?.VITE_CUSTOMER_ID) {
        return (window as any).process.env.VITE_CUSTOMER_ID
      }
      return "fdff1878-a54a-44ee-b82c-a62bdc5cdb55"
    }

    const customerId = getCustomerId()

    console.log('=== 新系統登入調用舊系統API ===')
    console.log('登入資料:', { Account: data.username, IdName: customerId })
    console.log('使用的API端點:', 'http://192.168.1.152:8345/api/Staff/StaffLogin')

    // 完全按照舊前端的方式：使用 application/x-www-form-urlencoded
    const formData = new URLSearchParams()
    formData.append('Account', data.username)
    formData.append('Password', data.password)
    formData.append('IdName', customerId)

    console.log('發送的表單數據:', formData.toString())

    const response = await fetch('http://192.168.1.152:8345/api/Staff/StaffLogin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    })

    console.log('HTTP 狀態:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    console.log('後端回應:', result)

    // 檢查登入是否成功 - 按照舊前端的邏輯
    if (!result.Detail) {
      const errorMessage = result.Message || "登入失敗，請檢查登入資料"
      console.error("登入失敗:", errorMessage)
      return {
        success: false,
        message: errorMessage
      }
    }

    const {
      AccessToken,
      RefreshToken,
      StaffName,
      PermissionCode,
      RoleId,
      UniformNumber,
      CustomerID,
      CustomerName,
      EnableState,
    } = result.Detail

    // 完全按照舊前端的方式保存token
    localStorage.setItem('access_token', AccessToken)
    localStorage.setItem('refresh_token', RefreshToken)
    localStorage.setItem('brand_id', customerId)
    localStorage.setItem('customer_id', CustomerID)

    console.log('✅ 登入成功！')
    console.log('Token已保存:', AccessToken.substring(0, 20) + '...')
    console.log('客戶ID:', CustomerID)
    console.log('員工姓名:', StaffName)

    // 返回新系統期望的格式
    return {
      success: true,
      data: {
        username: data.username,
        roles: ["admin"], // 根據PermissionCode可以調整
        accessToken: AccessToken,
        refreshToken: RefreshToken,
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小時後過期
      }
    }

  } catch (error: any) {
    console.error('❌ 登入失敗:', error)
    return {
      success: false,
      message: error.message || '登入失敗，請檢查帳號密碼'
    }
  }
};

/** 刷新token */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};
