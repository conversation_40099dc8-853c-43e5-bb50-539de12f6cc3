import { RouteRecordRaw } from "vue-router";

/**
 * PLC 警報系統路由配置
 */
const plcAlarmRoutes: RouteRecordRaw = {
  path: "/plc-alarm",
  name: "PLCAlarm",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-alarm/realtime",
  meta: {
    title: "警報系統",
    icon: "ep:warning",
    rank: 2
  },
  children: [
    {
      path: "/plc-alarm/realtime",
      name: "PLCAlarmRealtime",
      component: () => import("@/views/plc/alarm/index.vue"),
      meta: {
        title: "即時監控",
        icon: "ep:monitor",
        showParent: true
      }
    },
    {
      path: "/plc-alarm/history",
      name: "PLCAlarmHistory",
      component: () => import("@/views/plc/alarm/history.vue"),
      meta: {
        title: "歷史記錄",
        icon: "ep:document",
        showParent: true
      }
    },
    {
      path: "/plc-alarm/reliability",
      name: "PLCAlarmReliability",
      component: () => import("@/views/plc/alarm/reliability.vue"),
      meta: {
        title: "可靠性分析",
        icon: "ep:data-analysis",
        showParent: true
      }
    }
  ]
};

export default plcAlarmRoutes;