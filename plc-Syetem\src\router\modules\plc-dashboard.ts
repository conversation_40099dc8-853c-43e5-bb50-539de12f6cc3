import { RouteRecordRaw } from "vue-router";

/**
 * PLC 儀表板路由配置
 */
const plcDashboardRoutes: RouteRecordRaw = {
  path: "/plc-dashboard",
  name: "PLCDashboard",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-dashboard/index",
  meta: {
    title: "PLC 儀表板",
    icon: "ep:data-board",
    rank: 1
  },
  children: [
    {
      path: "/plc-dashboard/index",
      name: "PLCDashboardIndex",
      component: () => import("@/views/plc/dashboard/index.vue"),
      meta: {
        title: "系統概覽",
        icon: "ep:monitor",
        showParent: true
      }
    }
  ]
};

export default plcDashboardRoutes;
