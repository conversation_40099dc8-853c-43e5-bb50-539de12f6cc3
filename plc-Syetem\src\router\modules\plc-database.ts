import { RouteRecordRaw } from "vue-router";

/**
 * PLC 資料庫管理路由配置
 */
const plcDatabaseRoutes: RouteRecordRaw = {
  path: "/plc/database",
  name: "PLCDatabase",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc/database/index",
  meta: {
    title: "數據中心",
    icon: "ep:data-board",
    rank: 3
  },
  children: [
    {
      path: "/plc/database/index",
      name: "PLCDatabaseIndex",
      component: () => import("@/views/plc/database/index.vue"),
      meta: {
        title: "數據中心",
        icon: "ep:data-board",
        showParent: true
      }
    },
    {
      path: "/plc/database/realtime",
      name: "PLCDatabaseRealtime",
      component: () => import("@/views/plc/database/realtime.vue"),
      meta: {
        title: "即時資料",
        icon: "ep:monitor",
        showParent: true
      }
    },
    {
      path: "/plc/database/history",
      name: "PLCDatabaseHistory",
      component: () => import("@/views/plc/database/history.vue"),
      meta: {
        title: "歷史報表",
        icon: "ep:clock",
        showParent: true
      }
    },
    {
      path: "/plc/database/runtime",
      name: "PLCDatabaseRuntime",
      component: () => import("@/views/plc/database/runtime.vue"),
      meta: {
        title: "運轉時數",
        icon: "ep:data-analysis",
        showParent: true
      }
    },
    {
      path: "/plc/database/custom-report",
      name: "PLCDatabaseCustomReport",
      component: () => import("@/views/plc/database/custom-report.vue"),
      meta: {
        title: "匯出報表",
        icon: "ep:document",
        showParent: true
      }
    }
  ]
};

export default plcDatabaseRoutes;