import { RouteRecordRaw } from "vue-router";

/**
 * PLC GUI 監控系統路由配置
 */
const plcGuiRoutes: RouteRecordRaw = {
  path: "/plc-gui",
  name: "PLCGUI",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-gui/setting",
  meta: {
    title: "GUI 監控",
    icon: "ep:monitor",
    rank: 4
  },
  children: [
    {
      path: "/plc-gui/setting",
      name: "PLCGUISetting",
      component: () => import("@/views/plc/gui/setting.vue"),
      meta: {
        title: "GUI設定",
        icon: "ep:setting",
        showParent: true
      }
    },
    {
      path: "/plc-gui/main/:id",
      name: "PLCG<PERSON>Main",
      component: () => import("@/views/plc/gui/main.vue"),
      meta: {
        title: "圖形監控",
        icon: "ep:monitor",
        showParent: true,
        showLink: false  // 隱藏在側邊欄中，因為需要動態 id 參數
      }
    }
  ]
};

export default plcGuiRoutes;