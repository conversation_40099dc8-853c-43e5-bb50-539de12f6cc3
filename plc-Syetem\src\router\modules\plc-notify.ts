import { RouteRecordRaw } from "vue-router";

/**
 * PLC 通知系統路由配置
 */
const plcNotifyRoutes: RouteRecordRaw = {
  path: "/plc-notify",
  name: "PLCNotify",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-notify/manage",
  meta: {
    title: "通知系統",
    icon: "ep:bell",
    rank: 6
  },
  children: [
    {
      path: "/plc-notify/manage",
      name: "PLCNotifyManage",
      component: () => import("@/views/plc/notify/index.vue"),
      meta: {
        title: "通知管理",
        icon: "ep:bell",
        showParent: true
      }
    },
    {
      path: "/plc-notify/group",
      name: "PLCNotifyGroup",
      component: () => import("@/views/plc/notify/group.vue"),
      meta: {
        title: "群組管理",
        icon: "ep:user-filled",
        showParent: true
      }
    },
    {
      path: "/plc-notify/message",
      name: "PLCNotifyMessage",
      component: () => import("@/views/plc/notify/message.vue"),
      meta: {
        title: "訊息管理",
        icon: "ep:chat-dot-round",
        showParent: true
      }
    },
    {
      path: "/plc-notify/setting",
      name: "PLCNotifySetting",
      component: () => import("@/views/plc/notify/setting.vue"),
      meta: {
        title: "通知設定",
        icon: "ep:setting",
        showParent: true
      }
    }
  ]
};

export default plcNotifyRoutes;