import { RouteRecordRaw } from "vue-router";

/**
 * PLC 排程系統路由配置
 */
const plcScheduleRoutes: RouteRecordRaw = {
  path: "/plc-schedule",
  name: "PLCSchedule",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-schedule/calendar",
  meta: {
    title: "排程系統",
    icon: "ep:calendar",
    rank: 7
  },
  children: [
    {
      path: "/plc-schedule/calendar",
      name: "PLCScheduleCalendar",
      component: () => import("@/views/plc/schedule/index.vue"),
      meta: {
        title: "行事曆",
        icon: "ep:calendar",
        showParent: true
      }
    },
    {
      path: "/plc-schedule/work",
      name: "PLCScheduleWork",
      component: () => import("@/views/plc/schedule/work.vue"),
      meta: {
        title: "工作排程",
        icon: "ep:briefcase",
        showParent: true
      }
    }
  ]
};

export default plcScheduleRoutes;