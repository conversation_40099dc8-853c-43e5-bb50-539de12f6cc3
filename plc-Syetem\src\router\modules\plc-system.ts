import { RouteRecordRaw } from "vue-router";

/**
 * PLC 系統管理路由配置
 */
const plcSystemRoutes: RouteRecordRaw = {
  path: "/plc/system",
  name: "PLCSystem",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc/system/bill",
  meta: {
    title: "系統管理",
    icon: "ep:setting",
    rank: 8
  },
  children: [
    {
      path: "/plc/system/cctv",
      name: "PLCSystemCCTV",
      component: () => import("@/views/plc/cctv/index.vue"),
      meta: {
        title: "CCTV監控",
        icon: "ep:video-camera",
        showParent: true
      }
    },
    {
      path: "/plc/system/bill",
      name: "PLCSystemBill",
      component: () => import("@/views/plc/system/bill.vue"),
      meta: {
        title: "電費計算",
        icon: "ep:lightning",
        showParent: true
      }
    },
    {
      path: "/plc/system/btu",
      name: "PLCSystemBTU",
      component: () => import("@/views/plc/system/btu.vue"),
      meta: {
        title: "BTU計算",
        icon: "ep:odometer",
        showParent: true
      }
    },
    {
      path: "/plc/system/uninstall",
      name: "PLCSystemUninstall",
      component: () => import("@/views/plc/system/uninstall.vue"),
      meta: {
        title: "電力卸載",
        icon: "ep:switch",
        showParent: true
      }
    },
    {
      path: "/plc/system/waterbill",
      name: "PLCSystemWaterBill",
      component: () => import("@/views/plc/system/waterbill.vue"),
      meta: {
        title: "水費計算",
        icon: "ep:histogram",
        showParent: true
      }
    }
  ]
};

export default plcSystemRoutes;