import { RouteRecordRaw } from "vue-router";

/**
 * PLC 標籤管理路由配置
 */
const plcTagsRoutes: RouteRecordRaw = {
  path: "/plc-tags",
  name: "PLCTags",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-tags/manage",
  meta: {
    title: "標籤管理",
    icon: "ep:price-tag",
    rank: 9
  },
  children: [
    {
      path: "/plc-tags/manage",
      name: "PLCTagsManage",
      component: () => import("@/views/plc/tags/index.vue"),
      meta: {
        title: "標籤總覽",
        icon: "ep:price-tag",
        showParent: true
      }
    },
    {
      path: "/plc-tags/channel",
      name: "PLCTagsChannel",
      component: () => import("@/views/plc/tags/channel.vue"),
      meta: {
        title: "通道管理",
        icon: "ep:connection",
        showParent: true
      }
    },
    {
      path: "/plc-tags/device",
      name: "PLCTagsDevice",
      component: () => import("@/views/plc/tags/device.vue"),
      meta: {
        title: "設備管理",
        icon: "ep:cpu",
        showParent: true
      }
    },
    {
      path: "/plc-tags/group",
      name: "PLCTagsGroup",
      component: () => import("@/views/plc/tags/group.vue"),
      meta: {
        title: "群組管理",
        icon: "ep:collection",
        showParent: true
      }
    },
    {
      path: "/plc-tags/region",
      name: "PLCTagsRegion",
      component: () => import("@/views/plc/tags/region.vue"),
      meta: {
        title: "區域管理",
        icon: "ep:location",
        showParent: true
      }
    },
    {
      path: "/plc-tags/tag",
      name: "PLCTagsTag",
      component: () => import("@/views/plc/tags/tag.vue"),
      meta: {
        title: "標籤管理",
        icon: "ep:price-tag",
        showParent: true
      }
    }
  ]
};

export default plcTagsRoutes;