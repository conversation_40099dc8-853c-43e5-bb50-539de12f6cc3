import { RouteRecordRaw } from "vue-router";

/**
 * PLC 用戶管理路由配置
 */
const plcUserRoutes: RouteRecordRaw = {
  path: "/plc-user",
  name: "PLCUser",
  component: () => import("@/layout/index.vue"),
  redirect: "/plc-user/manage",
  meta: {
    title: "用戶管理",
    icon: "ep:user",
    rank: 10
  },
  children: [
    {
      path: "/plc-user/manage",
      name: "PLCUserManage",
      component: () => import("@/views/plc/user/index.vue"),
      meta: {
        title: "用戶總覽",
        icon: "ep:user",
        showParent: true
      }
    },
    {
      path: "/plc-user/list",
      name: "P<PERSON><PERSON><PERSON>List",
      component: () => import("@/views/plc/user/list.vue"),
      meta: {
        title: "用戶列表",
        icon: "ep:user-filled",
        showParent: true
      }
    },
    {
      path: "/plc-user/role",
      name: "P<PERSON><PERSON>ser<PERSON>ole",
      component: () => import("@/views/plc/user/role.vue"),
      meta: {
        title: "權限管理",
        icon: "ep:key",
        showParent: true
      }
    }
  ]
};

export default plcUserRoutes;