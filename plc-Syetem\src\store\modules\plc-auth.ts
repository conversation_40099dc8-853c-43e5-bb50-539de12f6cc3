import { defineStore } from 'pinia'
import {
  PLCUserInfo,
  PLCTokenInfo,
  getPLCToken,
  setPLCToken,
  removePLCToken,
  getPLCUserInfo,
  isPLCLoggedIn,
  createPLCUserInfoFromToken
} from '@/utils/plc/auth'
import { authAPI, type LoginRequest } from '@/api/plc/auth'

/**
 * PLC 認證 Store
 * 管理用戶登入狀態、權限等資訊
 */
export const usePLCAuthStore = defineStore('plc-auth', {
  state: () => ({
    // 用戶資訊（對應後端 JWT Payload）
    customerId: '',
    account: '',
    staffId: '',
    permission: 0,
    staffName: '',
    roleId: '',
    isRoot: false,
    avatar: '',

    // Token 資訊
    accessToken: '',
    refreshToken: '',
    expires: 0,

    // 登入狀態
    isLoggedIn: false,

    // 載入狀態
    loading: false
  }),

  getters: {
    // 獲取用戶資訊
    userInfo: (state): PLCUserInfo => ({
      customerId: state.customerId,
      account: state.account,
      staffId: state.staffId,
      permission: state.permission,
      staffName: state.staffName,
      roleId: state.roleId,
      isRoot: state.isRoot,
      avatar: state.avatar
    }),

    // 獲取 Token 資訊
    tokenInfo: (state): PLCTokenInfo => ({
      accessToken: state.accessToken,
      refreshToken: state.refreshToken,
      expires: state.expires,
      userInfo: {
        customerId: state.customerId,
        account: state.account,
        staffId: state.staffId,
        permission: state.permission,
        staffName: state.staffName,
        roleId: state.roleId,
        isRoot: state.isRoot,
        avatar: state.avatar
      }
    }),

    // 檢查是否有特定權限
    hasPermission: (state) => (requiredPermission: number): boolean => {
      if (state.isRoot) return true
      return (state.permission & requiredPermission) === requiredPermission
    }
  },

  actions: {
    // 設置認證資料
    setAuthData(tokenInfo: PLCTokenInfo) {
      const { accessToken, refreshToken, expires, userInfo } = tokenInfo

      // 設置 Token 資訊
      this.accessToken = accessToken
      this.refreshToken = refreshToken || ''
      this.expires = expires

      // 設置用戶資訊
      this.customerId = userInfo.customerId
      this.account = userInfo.account
      this.staffId = userInfo.staffId
      this.permission = userInfo.permission
      this.staffName = userInfo.staffName || ''
      this.roleId = userInfo.roleId || ''
      this.isRoot = userInfo.isRoot || false
      this.avatar = userInfo.avatar || ''

      // 設置登入狀態
      this.isLoggedIn = true

      // 保存到本地存儲
      setPLCToken(tokenInfo)
    },

    // 從本地存儲載入認證資料
    loadAuthData() {
      const tokenInfo = getPLCToken()
      if (tokenInfo) {
        this.setAuthData(tokenInfo)
        return true
      }
      return false
    },

    // 清除認證資料
    clearAuthData() {
      this.customerId = ''
      this.account = ''
      this.staffId = ''
      this.permission = 0
      this.staffName = ''
      this.roleId = ''
      this.isRoot = false
      this.avatar = ''
      this.accessToken = ''
      this.refreshToken = ''
      this.expires = 0
      this.isLoggedIn = false

      // 清除本地存儲
      removePLCToken()
    },

    // 登入
    async login(credentials: LoginRequest) {
      this.loading = true
      try {
        // 調用登入 API
        const response = await authAPI.login(credentials)

        // 創建 Token 資訊
        const tokenInfo: PLCTokenInfo = {
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          expires: new Date(response.expires).getTime(),
          userInfo: response.userInfo
        }

        // 設置認證數據
        this.setAuthData(tokenInfo)

        return { success: true, message: '登入成功', data: response }
      } catch (error: any) {
        console.error('登入失敗:', error)
        throw new Error(error.message || '登入失敗，請檢查帳號密碼')
      } finally {
        this.loading = false
      }
    },

    // 登出
    async logout() {
      this.loading = true
      try {
        // 調用登出 API
        await authAPI.logout()

        this.clearAuthData()
        return { success: true, message: '登出成功' }
      } catch (error: any) {
        console.error('登出失敗:', error)
        // 即使 API 失敗也要清除本地資料
        this.clearAuthData()
        return { success: false, message: error.message || '登出失敗' }
      } finally {
        this.loading = false
      }
    },

    // 刷新 Token
    async refreshToken() {
      if (!this.refreshToken) {
        throw new Error('沒有 Refresh Token')
      }

      try {
        // 調用刷新 Token API
        const response = await authAPI.refreshToken({
          refreshToken: this.refreshToken
        })

        // 更新 Token 資訊
        const newTokenInfo: PLCTokenInfo = {
          accessToken: response.accessToken,
          refreshToken: response.refreshToken,
          expires: new Date(response.expires).getTime(),
          userInfo: this.userInfo // 保持原有用戶資訊
        }

        // 設置新的認證數據
        this.setAuthData(newTokenInfo)

        return { success: true, message: 'Token 刷新成功', data: response }
      } catch (error: any) {
        console.error('Token 刷新失敗:', error)
        this.clearAuthData()
        throw new Error(error.message || 'Token 刷新失敗')
      }
    },

    // 檢查登入狀態
    checkAuthStatus(): boolean {
      return isPLCLoggedIn()
    }
  }
})