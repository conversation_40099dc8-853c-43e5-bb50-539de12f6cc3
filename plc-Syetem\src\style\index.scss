@use "theme";
@use "transition";
@use "element-plus";
@use "sidebar";
@use "dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;

  /* 常用border-color 需要时可取用 */
  --pure-border-color: rgb(5 5 5 / 6%);

  /* switch关闭状态下的color 需要时可取用 */
  --pure-switch-off-color: #a6a6a6;

  /** 主题色 */
  --pure-theme-sub-menu-active-text: initial;
  --pure-theme-menu-bg: none;
  --pure-theme-menu-hover: none;
  --pure-theme-sub-menu-bg: transparent;
  --pure-theme-menu-text: initial;
  --pure-theme-sidebar-logo: none;
  --pure-theme-menu-title-hover: initial;
  --pure-theme-menu-active-before: transparent;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}
