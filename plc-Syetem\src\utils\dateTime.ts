/**
 * 日期時間工具函數
 */

/**
 * 格式化日期時間
 * @param dateTime 日期時間字符串或Date對象
 * @param format 格式化模板，默認為 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化後的日期時間字符串
 */
export function formatDateTime(
  dateTime: string | Date | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!dateTime) return '-'
  
  try {
    const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
    
    if (isNaN(date.getTime())) {
      return '-'
    }
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  } catch (error) {
    console.error('日期格式化錯誤:', error)
    return '-'
  }
}

/**
 * 格式化日期
 * @param date 日期字符串或Date對象
 * @returns 格式化後的日期字符串 (YYYY-MM-DD)
 */
export function formatDate(date: string | Date | null | undefined): string {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化時間
 * @param time 時間字符串或Date對象
 * @returns 格式化後的時間字符串 (HH:mm:ss)
 */
export function formatTime(time: string | Date | null | undefined): string {
  return formatDateTime(time, 'HH:mm:ss')
}

/**
 * 獲取相對時間描述
 * @param dateTime 日期時間字符串或Date對象
 * @returns 相對時間描述，如 "2分鐘前"、"1小時前"
 */
export function getRelativeTime(dateTime: string | Date | null | undefined): string {
  if (!dateTime) return '-'
  
  try {
    const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    
    if (diffMs < 0) return '未來時間'
    
    const diffSeconds = Math.floor(diffMs / 1000)
    const diffMinutes = Math.floor(diffSeconds / 60)
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffSeconds < 60) {
      return '剛剛'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分鐘前`
    } else if (diffHours < 24) {
      return `${diffHours}小時前`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return formatDateTime(date)
    }
  } catch (error) {
    console.error('相對時間計算錯誤:', error)
    return '-'
  }
}

/**
 * 檢查日期是否為今天
 * @param date 日期字符串或Date對象
 * @returns 是否為今天
 */
export function isToday(date: string | Date | null | undefined): boolean {
  if (!date) return false
  
  try {
    const targetDate = typeof date === 'string' ? new Date(date) : date
    const today = new Date()
    
    return (
      targetDate.getFullYear() === today.getFullYear() &&
      targetDate.getMonth() === today.getMonth() &&
      targetDate.getDate() === today.getDate()
    )
  } catch (error) {
    return false
  }
}

/**
 * 檢查日期是否為昨天
 * @param date 日期字符串或Date對象
 * @returns 是否為昨天
 */
export function isYesterday(date: string | Date | null | undefined): boolean {
  if (!date) return false
  
  try {
    const targetDate = typeof date === 'string' ? new Date(date) : date
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    
    return (
      targetDate.getFullYear() === yesterday.getFullYear() &&
      targetDate.getMonth() === yesterday.getMonth() &&
      targetDate.getDate() === yesterday.getDate()
    )
  } catch (error) {
    return false
  }
}

/**
 * 獲取日期範圍的預設選項
 * @returns 日期範圍選項數組
 */
export function getDateRangeShortcuts() {
  return [
    {
      text: '今天',
      value: () => {
        const start = new Date()
        start.setHours(0, 0, 0, 0)
        const end = new Date()
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    },
    {
      text: '昨天',
      value: () => {
        const start = new Date()
        start.setDate(start.getDate() - 1)
        start.setHours(0, 0, 0, 0)
        const end = new Date()
        end.setDate(end.getDate() - 1)
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    },
    {
      text: '最近7天',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 6)
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    },
    {
      text: '最近30天',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 29)
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    },
    {
      text: '本月',
      value: () => {
        const start = new Date()
        start.setDate(1)
        start.setHours(0, 0, 0, 0)
        const end = new Date()
        end.setMonth(end.getMonth() + 1, 0)
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    },
    {
      text: '上月',
      value: () => {
        const start = new Date()
        start.setMonth(start.getMonth() - 1, 1)
        start.setHours(0, 0, 0, 0)
        const end = new Date()
        end.setDate(0)
        end.setHours(23, 59, 59, 999)
        return [start, end]
      }
    }
  ]
}

/**
 * 將日期轉換為 ISO 字符串（本地時區）
 * @param date 日期對象
 * @returns ISO 格式的日期時間字符串
 */
export function toLocalISOString(date: Date): string {
  const offset = date.getTimezoneOffset()
  const localDate = new Date(date.getTime() - offset * 60 * 1000)
  return localDate.toISOString().slice(0, 19).replace('T', ' ')
}

/**
 * 解析 ISO 日期字符串為本地日期
 * @param isoString ISO 格式的日期時間字符串
 * @returns 本地日期對象
 */
export function parseLocalISOString(isoString: string): Date {
  return new Date(isoString.replace(' ', 'T'))
}
