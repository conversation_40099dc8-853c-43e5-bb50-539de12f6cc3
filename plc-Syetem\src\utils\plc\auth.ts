import Cookies from "js-cookie";
import { storageLocal } from "@pureadmin/utils";

/**
 * PLC 認證系統 - 完全對應後端 JWT 結構
 * 確保與 ActionCheckAccessTokenFilter 和 ActionCheckPermissionFilter 完全相容
 */

// PLC JWT Payload 結構（對應後端）
export interface PLCJWTPayload {
  CustomerId: string;    // 客戶 ID
  Account: string;       // 帳號
  StaffId: string;       // 員工 ID
  Permission: number;    // 權限代碼
  exp: number;          // 過期時間
  iat?: number;         // 簽發時間
  iss?: string;         // 簽發者
}

// PLC 用戶資訊結構
export interface PLCUserInfo {
  customerId: string;
  account: string;
  staffId: string;
  permission: number;
  staffName?: string;
  roleId?: string;
  isRoot?: boolean;
  avatar?: string;
}

// PLC Token 資訊結構
export interface PLCTokenInfo {
  accessToken: string;
  refreshToken?: string;
  expires: number;
  userInfo: PLCUserInfo;
}

// Storage Keys
export const PLC_TOKEN_KEY = "plc-token";
export const PLC_USER_KEY = "plc-user-info";
export const PLC_TABS_KEY = "plc-multiple-tabs";

/**
 * 解析 JWT Token 獲取 Payload
 */
export function parsePLCJWTPayload(token: string): PLCJWTPayload | null {
  try {
    // JWT 格式：header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    // 解碼 payload (Base64URL)
    const payload = parts[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded) as PLCJWTPayload;
  } catch (error) {
    console.error('Failed to parse JWT payload:', error);
    return null;
  }
}

/**
 * 驗證 Token 是否有效
 */
export function isPLCTokenValid(token: string): boolean {
  const payload = parsePLCJWTPayload(token);
  if (!payload) return false;

  // 檢查是否過期
  const now = Math.floor(Date.now() / 1000);
  return payload.exp > now;
}

/**
 * 獲取 PLC Token
 */
export function getPLCToken(): PLCTokenInfo | null {
  const tokenData = Cookies.get(PLC_TOKEN_KEY);
  if (!tokenData) {
    return storageLocal().getItem(PLC_USER_KEY);
  }

  try {
    const parsed = JSON.parse(tokenData);
    
    // 驗證 Token 是否有效
    if (!isPLCTokenValid(parsed.accessToken)) {
      removePLCToken();
      return null;
    }

    return parsed;
  } catch (error) {
    console.error('Failed to parse PLC token:', error);
    removePLCToken();
    return null;
  }
}

/**
 * 設置 PLC Token
 */
export function setPLCToken(tokenInfo: PLCTokenInfo): void {
  const { accessToken, refreshToken, expires, userInfo } = tokenInfo;

  // 設置 Cookie 中的 Token 資訊
  const cookieData = {
    accessToken,
    refreshToken,
    expires
  };

  const cookieString = JSON.stringify(cookieData);
  
  // 計算過期時間（天數）
  const expireDays = expires > 0 ? (expires - Date.now()) / 86400000 : 7;
  
  if (expireDays > 0) {
    Cookies.set(PLC_TOKEN_KEY, cookieString, { expires: expireDays });
  } else {
    Cookies.set(PLC_TOKEN_KEY, cookieString);
  }

  // 設置多標籤頁支持
  Cookies.set(PLC_TABS_KEY, "true", { expires: expireDays });

  // 設置 LocalStorage 中的用戶資訊
  storageLocal().setItem(PLC_USER_KEY, {
    ...tokenInfo,
    userInfo
  });
}

/**
 * 移除 PLC Token
 */
export function removePLCToken(): void {
  Cookies.remove(PLC_TOKEN_KEY);
  Cookies.remove(PLC_TABS_KEY);
  storageLocal().removeItem(PLC_USER_KEY);
}

/**
 * 格式化 Token 為 Bearer 格式（對應後端要求）
 */
export function formatPLCToken(token: string): string {
  return "Bearer " + token;
}

/**
 * 獲取當前用戶資訊
 */
export function getPLCUserInfo(): PLCUserInfo | null {
  const tokenInfo = getPLCToken();
  return tokenInfo?.userInfo || null;
}

/**
 * 檢查權限（對應後端 Permission 欄位）
 */
export function hasPLCPermission(requiredPermission: number): boolean {
  const userInfo = getPLCUserInfo();
  if (!userInfo) return false;

  // Root 用戶擁有所有權限
  if (userInfo.isRoot) return true;

  // 檢查權限位元
  return (userInfo.permission & requiredPermission) === requiredPermission;
}

/**
 * 檢查是否為 Root 用戶
 */
export function isPLCRootUser(): boolean {
  const userInfo = getPLCUserInfo();
  return userInfo?.isRoot || false;
}

/**
 * 獲取客戶 ID（用於 SignalR 註冊）
 */
export function getPLCCustomerId(): string | null {
  const userInfo = getPLCUserInfo();
  return userInfo?.customerId || null;
}

/**
 * 檢查是否已登入
 */
export function isPLCLoggedIn(): boolean {
  const tokenInfo = getPLCToken();
  return tokenInfo !== null && isPLCTokenValid(tokenInfo.accessToken);
}

/**
 * 從 JWT Token 創建用戶資訊
 */
export function createPLCUserInfoFromToken(token: string, additionalInfo?: Partial<PLCUserInfo>): PLCUserInfo | null {
  const payload = parsePLCJWTPayload(token);
  if (!payload) return null;

  return {
    customerId: payload.CustomerId,
    account: payload.Account,
    staffId: payload.StaffId,
    permission: payload.Permission,
    ...additionalInfo
  };
}
