import { plcHttp } from '@/utils/plc/http'

/**
 * PLC DataService - 統一的 API 請求服務
 * 完全對應後端 API 結構，確保 100% 相容性
 */
export class PLCDataService {
  private baseURL: string

  constructor(baseURL: string = '') {
    this.baseURL = baseURL
  }

  // GET 請求
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await plcHttp.get<T>(`${this.baseURL}${url}`, { params })
    return response.data
  }

  // POST 請求 - 自動轉換為 FormData 格式以符合後端 [FromForm] 要求
  async post<T>(url: string, data?: any): Promise<T> {
    let requestData = data
    let headers: any = {}

    // 如果數據是對象且不是 FormData，轉換為 application/x-www-form-urlencoded 格式
    if (data && typeof data === 'object' && !(data instanceof FormData)) {
      const formData = new URLSearchParams()
      Object.keys(data).forEach(key => {
        if (data[key] !== undefined && data[key] !== null) {
          formData.append(key, data[key].toString())
        }
      })
      requestData = formData
      headers['Content-Type'] = 'application/x-www-form-urlencoded'
    }

    const response = await plcHttp.post<T>(`${this.baseURL}${url}`, requestData, { headers })
    return response.data
  }

  // PUT 請求
  async put<T>(url: string, data?: any): Promise<T> {
    const response = await plcHttp.put<T>(`${this.baseURL}${url}`, data)
    return response.data
  }

  // DELETE 請求
  async delete<T>(url: string): Promise<T> {
    const response = await plcHttp.delete<T>(`${this.baseURL}${url}`)
    return response.data
  }

  // POST 請求 (FormData)
  async postForm<T>(url: string, formData: FormData): Promise<T> {
    const response = await plcHttp.post<T>(`${this.baseURL}${url}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }
}

// 統一的響應格式接口（對應後端）
export interface PLCApiResponse<T> {
  Success: boolean
  Message: string
  Detail: T
  ErrorCode?: string
}

// 分頁響應格式
export interface PLCPagedResponse<T> {
  Items: T[]
  TotalCount: number
  PageIndex: number
  PageSize: number
}

// 創建全局實例 - 使用 /api 前綴以配合 Vite 代理
export const plcDataService = new PLCDataService('/api')

// 導出類型
export type { PLCDataService }