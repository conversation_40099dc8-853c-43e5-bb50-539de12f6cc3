import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr'
import { getPLCCustomerId, formatPLCToken, getPLCToken } from '@/utils/plc/auth'
import { ElMessage } from 'element-plus'

/**
 * PLC SignalR 服務
 * 管理與後端 6 個 Hub 的連接，確保完全相容
 */

// Hub 連接狀態
export enum HubConnectionState {
  Disconnected = 'Disconnected',
  Connecting = 'Connecting',
  Connected = 'Connected',
  Disconnecting = 'Disconnecting',
  Reconnecting = 'Reconnecting'
}

// Hub 資訊接口
export interface HubInfo {
  name: string
  url: string
  connection: HubConnection | null
  state: HubConnectionState
  serviceCode: number
}

// 警報數據接口
export interface AlarmData {
  AlarmSummaryId: string
  TagName: string
  AlarmMessage: string
  AlarmTime: string
  AlarmLevel: number
  ServerIp: string
  CustomerId: string
}

// 頁面標籤數據接口
export interface PageTagData {
  PageId: string
  TagId: string
  TagName: string
  Value: any
  Timestamp: string
  Quality: string
}

// DesigoCC 標籤值接口
export interface DesigoCCTagValue {
  TagId: string
  TagName: string
  Value: any
  Timestamp: string
  Quality: string
  Unit?: string
}

// DesigoCC 警報接口
export interface DesigoCCAlarm {
  AlarmId: string
  TagName: string
  AlarmMessage: string
  AlarmTime: string
  AlarmLevel: number
  ServerIp: string
}

// DesigoCC 事件接口
export interface DesigoCCEvent {
  EventId: string
  EventType: string
  EventMessage: string
  EventTime: string
  TagName?: string
}

/**
 * PLC SignalR 服務類
 */
export class PLCSignalRService {
  private hubs: Map<string, HubInfo> = new Map()
  private reconnectAttempts: Map<string, number> = new Map()
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000 // 5秒
  private eventListeners: Map<string, Array<(data: any) => void>> = new Map()

  constructor() {
    this.initializeHubs()
  }

  /**
   * 初始化所有 Hub 配置
   */
  private initializeHubs(): void {
    const hubConfigs = [
      { name: 'alarm', url: '/AlarmSummary', serviceCode: 1 },
      { name: 'pageTag', url: '/PageTag', serviceCode: 2 },
      { name: 'desigoCC', url: '/DesigoCC', serviceCode: 3 },
      { name: 'tagModel', url: '/TagModel', serviceCode: 4 },
      { name: 'obix', url: '/Obix', serviceCode: 5 },
      { name: 'cctv', url: '/Cctv', serviceCode: 6 }
    ]

    hubConfigs.forEach(config => {
      this.hubs.set(config.name, {
        name: config.name,
        url: config.url,
        connection: null,
        state: HubConnectionState.Disconnected,
        serviceCode: config.serviceCode
      })
      this.reconnectAttempts.set(config.name, 0)
    })
  }

  /**
   * 創建 Hub 連接
   */
  private createHubConnection(hubInfo: HubInfo): HubConnection {
    const tokenInfo = getPLCToken()

    const connection = new HubConnectionBuilder()
      .withUrl(hubInfo.url, {
        accessTokenFactory: () => tokenInfo?.accessToken || ''
      })
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          // 指數退避重連策略
          return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000)
        }
      })
      .configureLogging(LogLevel.Information)
      .build()

    // 設置連接事件
    this.setupConnectionEvents(connection, hubInfo)

    return connection
  }

  /**
   * 設置連接事件處理
   */
  private setupConnectionEvents(connection: HubConnection, hubInfo: HubInfo): void {
    connection.onclose((error) => {
      console.warn(`${hubInfo.name} Hub 連接關閉:`, error)
      hubInfo.state = HubConnectionState.Disconnected
      this.handleReconnect(hubInfo)
    })

    connection.onreconnecting((error) => {
      console.warn(`${hubInfo.name} Hub 重新連接中:`, error)
      hubInfo.state = HubConnectionState.Reconnecting
    })

    connection.onreconnected((connectionId) => {
      console.log(`${hubInfo.name} Hub 重新連接成功:`, connectionId)
      hubInfo.state = HubConnectionState.Connected
      this.reconnectAttempts.set(hubInfo.name, 0)

      // 重新註冊客戶端
      this.registerClient(hubInfo)
    })
  }

  /**
   * 處理重連邏輯
   */
  private async handleReconnect(hubInfo: HubInfo): Promise<void> {
    const attempts = this.reconnectAttempts.get(hubInfo.name) || 0

    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(hubInfo.name, attempts + 1)

      setTimeout(async () => {
        try {
          await this.connectHub(hubInfo.name)
        } catch (error) {
          console.error(`${hubInfo.name} Hub 重連失敗:`, error)
        }
      }, this.reconnectInterval)
    } else {
      ElMessage.error(`${hubInfo.name} Hub 連接失敗，已達到最大重試次數`)
    }
  }

  /**
   * 註冊客戶端到 Hub
   */
  private async registerClient(hubInfo: HubInfo): Promise<void> {
    const customerId = getPLCCustomerId()
    if (!customerId || !hubInfo.connection) return

    try {
      await hubInfo.connection.invoke('RegisterClientAsync', customerId, hubInfo.serviceCode)
      console.log(`${hubInfo.name} Hub 客戶端註冊成功`)
    } catch (error) {
      console.error(`${hubInfo.name} Hub 客戶端註冊失敗:`, error)
    }
  }

  /**
   * 連接指定 Hub
   */
  async connectHub(hubName: string): Promise<void> {
    const hubInfo = this.hubs.get(hubName)
    if (!hubInfo) {
      throw new Error(`Hub ${hubName} 不存在`)
    }

    if (hubInfo.state === HubConnectionState.Connected) {
      console.log(`${hubName} Hub 已經連接`)
      return
    }

    try {
      hubInfo.state = HubConnectionState.Connecting

      // 創建新連接
      hubInfo.connection = this.createHubConnection(hubInfo)

      // 設置 Hub 特定的事件監聽
      this.setupHubSpecificEvents(hubInfo)

      // 開始連接
      await hubInfo.connection.start()
      hubInfo.state = HubConnectionState.Connected

      // 註冊客戶端
      await this.registerClient(hubInfo)

      console.log(`${hubName} Hub 連接成功`)
    } catch (error) {
      hubInfo.state = HubConnectionState.Disconnected
      console.error(`${hubName} Hub 連接失敗:`, error)
      throw error
    }
  }

  /**
   * 設置 Hub 特定的事件監聽
   */
  private setupHubSpecificEvents(hubInfo: HubInfo): void {
    if (!hubInfo.connection) return

    switch (hubInfo.name) {
      case 'alarm':
        this.setupAlarmHubEvents(hubInfo.connection)
        break
      case 'pageTag':
        this.setupPageTagHubEvents(hubInfo.connection)
        break
      case 'desigoCC':
        this.setupDesigoCCHubEvents(hubInfo.connection)
        break
      case 'tagModel':
        this.setupTagModelHubEvents(hubInfo.connection)
        break
      case 'obix':
        this.setupObixHubEvents(hubInfo.connection)
        break
      case 'cctv':
        this.setupCctvHubEvents(hubInfo.connection)
        break
    }
  }

  /**
   * 設置警報 Hub 事件
   */
  private setupAlarmHubEvents(connection: HubConnection): void {
    // 接收警報數據
    connection.on('SendAlarmData', (serverIp: string, alarm: AlarmData) => {
      console.log('收到警報數據:', { serverIp, alarm })
      // 觸發自定義事件
      this.emitEvent('alarm:data', { serverIp, alarm })
    })

    // 接收警報確認結果
    connection.on('AlarmAcknowledged', (alarmSummaryId: string, acknowledgedBy: string) => {
      console.log('警報已確認:', { alarmSummaryId, acknowledgedBy })
      this.emitEvent('alarm:acknowledged', { alarmSummaryId, acknowledgedBy })
    })
  }

  /**
   * 設置頁面標籤 Hub 事件
   */
  private setupPageTagHubEvents(connection: HubConnection): void {
    // 接收頁面數據
    connection.on('SendPageData', (pageData: PageTagData[]) => {
      console.log('收到頁面數據:', pageData)
      this.emitEvent('pageTag:data', pageData)
    })

    // 接收標籤值更新
    connection.on('TagValueUpdated', (tagData: PageTagData) => {
      console.log('標籤值已更新:', tagData)
      this.emitEvent('pageTag:updated', tagData)
    })
  }

  /**
   * 設置 DesigoCC Hub 事件
   */
  private setupDesigoCCHubEvents(connection: HubConnection): void {
    // 接收即時數據
    connection.on('SendRealTimeData', (serverIp: string, tagValueList: DesigoCCTagValue[], customerId: string) => {
      console.log('收到 DesigoCC 即時數據:', { serverIp, tagValueList, customerId })
      this.emitEvent('desigoCC:realTimeData', { serverIp, tagValueList, customerId })
    })

    // 接收警報數據
    connection.on('SendAlarmData', (serverIp: string, alarm: DesigoCCAlarm) => {
      console.log('收到 DesigoCC 警報:', { serverIp, alarm })
      this.emitEvent('desigoCC:alarm', { serverIp, alarm })
    })

    // 接收所有事件
    connection.on('SendAllEvents', (events: DesigoCCEvent[]) => {
      console.log('收到 DesigoCC 事件:', events)
      this.emitEvent('desigoCC:events', events)
    })
  }

  /**
   * 設置標籤模型 Hub 事件
   */
  private setupTagModelHubEvents(connection: HubConnection): void {
    // 接收標籤模型更新
    connection.on('TagModelUpdated', (tagModel: any) => {
      console.log('標籤模型已更新:', tagModel)
      this.emitEvent('tagModel:updated', tagModel)
    })

    // 接收標籤配置變更
    connection.on('TagConfigChanged', (tagConfig: any) => {
      console.log('標籤配置已變更:', tagConfig)
      this.emitEvent('tagModel:configChanged', tagConfig)
    })
  }

  /**
   * 設置 Obix Hub 事件
   */
  private setupObixHubEvents(connection: HubConnection): void {
    // 接收 Obix 標籤更新
    connection.on('SendAllUseTagsForUpdateAsync', (customerId: string) => {
      console.log('收到 Obix 標籤更新請求:', customerId)
      this.emitEvent('obix:tagsUpdate', customerId)
    })

    // 接收 Obix 數據
    connection.on('ObixDataReceived', (data: any) => {
      console.log('收到 Obix 數據:', data)
      this.emitEvent('obix:data', data)
    })
  }

  /**
   * 設置 CCTV Hub 事件
   */
  private setupCctvHubEvents(connection: HubConnection): void {
    // 接收 CCTV 狀態更新
    connection.on('CctvStatusUpdated', (cctvStatus: any) => {
      console.log('CCTV 狀態已更新:', cctvStatus)
      this.emitEvent('cctv:statusUpdated', cctvStatus)
    })

    // 接收所有 CCTV 資訊
    connection.on('AllCctvsReceived', (cctvs: any[]) => {
      console.log('收到所有 CCTV 資訊:', cctvs)
      this.emitEvent('cctv:allReceived', cctvs)
    })

    // 接收標籤 CCTV 資訊
    connection.on('TagCctvsReceived', (tagCctvs: any[]) => {
      console.log('收到標籤 CCTV 資訊:', tagCctvs)
      this.emitEvent('cctv:tagReceived', tagCctvs)
    })
  }

  /**
   * 觸發自定義事件
   */
  private emitEvent(eventName: string, data: any): void {
    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件監聽器執行失敗 (${eventName}):`, error)
        }
      })
    }
  }

  /**
   * 添加事件監聽器
   */
  on(eventName: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    this.eventListeners.get(eventName)!.push(listener)
  }

  /**
   * 移除事件監聽器
   */
  off(eventName: string, listener?: (data: any) => void): void {
    if (!this.eventListeners.has(eventName)) return

    if (listener) {
      const listeners = this.eventListeners.get(eventName)!
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    } else {
      this.eventListeners.delete(eventName)
    }
  }

  /**
   * 連接所有 Hub
   */
  async connectAllHubs(): Promise<void> {
    const hubNames = Array.from(this.hubs.keys())
    const promises = hubNames.map(hubName => this.connectHub(hubName))

    try {
      await Promise.all(promises)
      console.log('所有 Hub 連接成功')
    } catch (error) {
      console.error('部分 Hub 連接失敗:', error)
      throw error
    }
  }

  /**
   * 斷開指定 Hub
   */
  async disconnectHub(hubName: string): Promise<void> {
    const hubInfo = this.hubs.get(hubName)
    if (!hubInfo || !hubInfo.connection) return

    try {
      hubInfo.state = HubConnectionState.Disconnecting
      await hubInfo.connection.stop()
      hubInfo.connection = null
      hubInfo.state = HubConnectionState.Disconnected
      console.log(`${hubName} Hub 已斷開`)
    } catch (error) {
      console.error(`${hubName} Hub 斷開失敗:`, error)
      throw error
    }
  }

  /**
   * 斷開所有 Hub
   */
  async disconnectAllHubs(): Promise<void> {
    const hubNames = Array.from(this.hubs.keys())
    const promises = hubNames.map(hubName => this.disconnectHub(hubName))

    try {
      await Promise.all(promises)
      console.log('所有 Hub 已斷開')
    } catch (error) {
      console.error('部分 Hub 斷開失敗:', error)
    }
  }

  /**
   * 獲取 Hub 狀態
   */
  getHubState(hubName: string): HubConnectionState | null {
    const hubInfo = this.hubs.get(hubName)
    return hubInfo ? hubInfo.state : null
  }

  /**
   * 獲取所有 Hub 狀態
   */
  getAllHubStates(): Record<string, HubConnectionState> {
    const states: Record<string, HubConnectionState> = {}
    this.hubs.forEach((hubInfo, hubName) => {
      states[hubName] = hubInfo.state
    })
    return states
  }

  /**
   * 發送頁面連接資訊（PageTag Hub 專用）
   */
  async sendPageConnection(pageId: string): Promise<void> {
    const hubInfo = this.hubs.get('pageTag')
    if (!hubInfo?.connection || hubInfo.state !== HubConnectionState.Connected) {
      throw new Error('PageTag Hub 未連接')
    }

    try {
      const connectionData = JSON.stringify({
        ClientId: hubInfo.connection.connectionId,
        PageId: pageId
      })

      await hubInfo.connection.invoke('FromClientSendConnectedIdAndPageIdAsync', connectionData)
      console.log(`頁面連接資訊已發送: ${pageId}`)
    } catch (error) {
      console.error('發送頁面連接資訊失敗:', error)
      throw error
    }
  }

  /**
   * 請求所有 CCTV 資訊（CCTV Hub 專用）
   */
  async requestAllCctvs(requestId: string): Promise<void> {
    const hubInfo = this.hubs.get('cctv')
    if (!hubInfo?.connection || hubInfo.state !== HubConnectionState.Connected) {
      throw new Error('CCTV Hub 未連接')
    }

    try {
      await hubInfo.connection.invoke('RequestGetAllCctvs', requestId)
      console.log(`已請求所有 CCTV 資訊: ${requestId}`)
    } catch (error) {
      console.error('請求所有 CCTV 資訊失敗:', error)
      throw error
    }
  }

  /**
   * 請求標籤 CCTV 資訊（CCTV Hub 專用）
   */
  async requestTagCctvs(requestId: string): Promise<void> {
    const hubInfo = this.hubs.get('cctv')
    if (!hubInfo?.connection || hubInfo.state !== HubConnectionState.Connected) {
      throw new Error('CCTV Hub 未連接')
    }

    try {
      await hubInfo.connection.invoke('RequestGetAllTagCctvs', requestId)
      console.log(`已請求標籤 CCTV 資訊: ${requestId}`)
    } catch (error) {
      console.error('請求標籤 CCTV 資訊失敗:', error)
      throw error
    }
  }
}

// 創建全局實例
export const plcSignalRService = new PLCSignalRService()

// 導出類型
export type { PLCSignalRService }