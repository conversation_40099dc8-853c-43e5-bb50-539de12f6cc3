<template>
  <div class="account-settings-container">
    <div class="settings-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>帳戶設定</span>
          </div>
        </template>
        
        <el-form :model="userForm" label-width="120px">
          <el-form-item label="用戶名">
            <el-input v-model="userForm.username" disabled />
          </el-form-item>
          
          <el-form-item label="員工姓名">
            <el-input v-model="userForm.staffName" />
          </el-form-item>
          
          <el-form-item label="角色">
            <el-input v-model="userForm.role" disabled />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary">保存設定</el-button>
            <el-button>重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

const authStore = usePLCAuthStore()

const userForm = reactive({
  username: authStore.userInfo.account || '',
  staffName: authStore.userInfo.staffName || '',
  role: authStore.userInfo.isRoot ? '管理員' : '一般用戶'
})
</script>

<style scoped>
.account-settings-container {
  padding: 20px;
}

.settings-content {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}
</style>
