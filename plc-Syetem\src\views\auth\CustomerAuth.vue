<template>
  <div class="customer-auth-container">
    <div class="auth-content">
      <div class="loading-spinner">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
      </div>
      <h2>正在驗證客戶身份...</h2>
      <p>客戶ID: {{ customerId }}</p>
      <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElIcon, ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useUserStoreHook } from '@/store/modules/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStoreHook()

const customerId = ref<string>('')
const errorMessage = ref<string>('')

onMounted(async () => {
  try {
    // 從路由參數中獲取客戶ID
    const customerIdParam = route.params.customerId as string
    
    if (!customerIdParam) {
      throw new Error('客戶ID不能為空')
    }
    
    customerId.value = customerIdParam
    
    // 驗證客戶ID格式（UUID格式）
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(customerIdParam)) {
      throw new Error('客戶ID格式不正確')
    }
    
    // 將客戶ID存儲到localStorage和環境變數中
    localStorage.setItem('PLC_CUSTOMER_ID', customerIdParam)
    
    // 更新用戶store中的客戶ID
    if (userStore.setCustomerId) {
      userStore.setCustomerId(customerIdParam)
    }
    
    // 顯示成功訊息
    ElMessage.success(`客戶身份驗證成功: ${customerIdParam}`)
    
    // 延遲1秒後重定向到儀表板
    setTimeout(() => {
      router.replace('/plc-dashboard')
    }, 1000)
    
  } catch (error) {
    console.error('客戶身份驗證失敗:', error)
    errorMessage.value = error instanceof Error ? error.message : '未知錯誤'
    
    // 3秒後重定向到登入頁
    setTimeout(() => {
      router.replace('/login')
    }, 3000)
  }
})
</script>

<style scoped>
.customer-auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.loading-spinner {
  font-size: 2rem;
  color: #409eff;
  margin-bottom: 1rem;
}

h2 {
  color: #333;
  margin-bottom: 1rem;
}

p {
  color: #666;
  margin-bottom: 0.5rem;
}

.error-message {
  color: #f56c6c;
  font-weight: bold;
}
</style>
