<template>
  <div class="alarm-history-container">
    <!-- 搜尋條件區域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="時間範圍">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="開始時間"
            end-placeholder="結束時間"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            style="width: 350px"
          />
        </el-form-item>

        <el-form-item label="警報等級">
          <el-select v-model="searchForm.alarmLevel" placeholder="請選擇警報等級" clearable style="width: 150px">
            <el-option label="全部" :value="undefined" />
            <el-option label="嚴重" :value="1" />
            <el-option label="警告" :value="2" />
            <el-option label="資訊" :value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="標籤名稱">
          <el-input
            v-model="searchForm.tagName"
            placeholder="請輸入標籤名稱"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch" :loading="loading">
            搜尋
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
          <el-button :icon="Download" @click="handleExport" :loading="exporting">
            匯出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 警報歷史列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>警報歷史記錄</span>
          <div class="header-actions">
            <el-tag :type="getStatisticsTagType('total')" size="large">
              總計: {{ statistics.totalAlarms }}
            </el-tag>
            <el-tag :type="getStatisticsTagType('critical')" size="large">
              嚴重: {{ statistics.criticalAlarms }}
            </el-tag>
            <el-tag :type="getStatisticsTagType('warning')" size="large">
              警告: {{ statistics.warningAlarms }}
            </el-tag>
            <el-tag :type="getStatisticsTagType('info')" size="large">
              資訊: {{ statistics.infoAlarms }}
            </el-tag>
          </div>
        </div>
      </template>

      <el-table
        :data="alarmHistoryList"
        v-loading="loading"
        stripe
        border
        height="600"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="AlarmTime" label="警報時間" width="180" sortable>
          <template #default="{ row }">
            <el-icon><Clock /></el-icon>
            {{ formatDateTime(row.AlarmTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="TagName" label="標籤名稱" width="150" show-overflow-tooltip />

        <el-table-column prop="AlarmMessage" label="警報訊息" min-width="200" show-overflow-tooltip />

        <el-table-column prop="AlarmLevel" label="警報等級" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getAlarmLevelType(row.AlarmLevel)" size="small">
              {{ getAlarmLevelText(row.AlarmLevel) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="AlarmType" label="警報類型" width="120" align="center" />

        <el-table-column prop="CurrentValue" label="當前值" width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.CurrentValue !== null && row.CurrentValue !== undefined">
              {{ row.CurrentValue }} {{ row.Unit || '' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="AlarmValue" label="警報值" width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.AlarmValue !== null && row.AlarmValue !== undefined">
              {{ row.AlarmValue }} {{ row.Unit || '' }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="IsAcknowledged" label="確認狀態" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.IsAcknowledged ? 'success' : 'danger'" size="small">
              {{ row.IsAcknowledged ? '已確認' : '未確認' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="AcknowledgedBy" label="確認人員" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.AcknowledgedBy">{{ row.AcknowledgedBy }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="AcknowledgedTime" label="確認時間" width="180">
          <template #default="{ row }">
            <span v-if="row.AcknowledgedTime">
              <el-icon><Clock /></el-icon>
              {{ formatDateTime(row.AcknowledgedTime) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="!row.IsAcknowledged"
              type="primary"
              size="small"
              @click="handleAcknowledge(row)"
              :loading="acknowledging"
            >
              確認
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewDetail(row)"
            >
              詳情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 批量操作 -->
    <div v-if="selectedRows.length > 0" class="batch-actions">
      <el-card shadow="never">
        <div class="batch-content">
          <span>已選擇 {{ selectedRows.length }} 項</span>
          <div class="batch-buttons">
            <el-button
              type="primary"
              :icon="Check"
              @click="handleBatchAcknowledge"
              :loading="batchAcknowledging"
              :disabled="!hasUnacknowledgedSelected"
            >
              批量確認
            </el-button>
            <el-button @click="handleClearSelection">
              取消選擇
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 警報詳情對話框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="警報詳情"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedAlarm" class="alarm-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="警報ID">
            {{ selectedAlarm.AlarmSummaryId }}
          </el-descriptions-item>
          <el-descriptions-item label="標籤ID">
            {{ selectedAlarm.TagId }}
          </el-descriptions-item>
          <el-descriptions-item label="標籤名稱">
            {{ selectedAlarm.TagName }}
          </el-descriptions-item>
          <el-descriptions-item label="警報訊息">
            {{ selectedAlarm.AlarmMessage }}
          </el-descriptions-item>
          <el-descriptions-item label="警報時間">
            {{ formatDateTime(selectedAlarm.AlarmTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="警報等級">
            <el-tag :type="getAlarmLevelType(selectedAlarm.AlarmLevel)">
              {{ getAlarmLevelText(selectedAlarm.AlarmLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="警報類型">
            {{ selectedAlarm.AlarmType }}
          </el-descriptions-item>
          <el-descriptions-item label="當前值">
            {{ selectedAlarm.CurrentValue ?? '-' }} {{ selectedAlarm.Unit || '' }}
          </el-descriptions-item>
          <el-descriptions-item label="警報值">
            {{ selectedAlarm.AlarmValue ?? '-' }} {{ selectedAlarm.Unit || '' }}
          </el-descriptions-item>
          <el-descriptions-item label="確認狀態">
            <el-tag :type="selectedAlarm.IsAcknowledged ? 'success' : 'danger'">
              {{ selectedAlarm.IsAcknowledged ? '已確認' : '未確認' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="確認人員">
            {{ selectedAlarm.AcknowledgedBy || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="確認時間">
            {{ selectedAlarm.AcknowledgedTime ? formatDateTime(selectedAlarm.AcknowledgedTime) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客戶ID">
            {{ selectedAlarm.CustomerId }}
          </el-descriptions-item>
          <el-descriptions-item label="伺服器IP">
            {{ selectedAlarm.ServerIp }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">關閉</el-button>
          <el-button
            v-if="selectedAlarm && !selectedAlarm.IsAcknowledged"
            type="primary"
            @click="handleAcknowledgeFromDetail"
            :loading="acknowledging"
          >
            確認警報
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Download, Clock, Check } from '@element-plus/icons-vue'
import { alarmAPI, type AlarmSummary, type GetAlarmHistoryRequest } from '@/api/plc/alarm'
import { formatDateTime } from '@/utils/dateTime'

// 響應式數據
const loading = ref(false)
const exporting = ref(false)
const acknowledging = ref(false)
const batchAcknowledging = ref(false)
const detailDialogVisible = ref(false)

// 搜尋表單
const searchForm = reactive({
  dateRange: [] as string[],
  alarmLevel: undefined as number | undefined,
  tagName: ''
})

// 分頁資訊
const pagination = reactive({
  pageIndex: 1,
  pageSize: 20,
  total: 0
})

// 警報歷史列表
const alarmHistoryList = ref<AlarmSummary[]>([])

// 選中的行
const selectedRows = ref<AlarmSummary[]>([])

// 選中的警報詳情
const selectedAlarm = ref<AlarmSummary | null>(null)

// 統計資訊
const statistics = reactive({
  totalAlarms: 0,
  criticalAlarms: 0,
  warningAlarms: 0,
  infoAlarms: 0
})

// 計算屬性
const hasUnacknowledgedSelected = computed(() => {
  return selectedRows.value.some(row => !row.IsAcknowledged)
})

// 載入警報歷史
const loadAlarmHistory = async () => {
  try {
    loading.value = true

    const params: GetAlarmHistoryRequest = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      alarmLevel: searchForm.alarmLevel,
      tagName: searchForm.tagName || undefined
    }

    // 處理時間範圍
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }

    const response = await alarmAPI.getAlarmHistory(params)

    alarmHistoryList.value = response.data || []
    pagination.total = response.total || 0

    // 更新統計資訊
    updateStatistics()

  } catch (error) {
    console.error('載入警報歷史失敗:', error)
    ElMessage.error('載入警報歷史失敗')
  } finally {
    loading.value = false
  }
}

// 載入統計資訊
const loadStatistics = async () => {
  try {
    const stats = await alarmAPI.getAlarmStatistics()
    Object.assign(statistics, stats)
  } catch (error) {
    console.error('載入統計資訊失敗:', error)
  }
}

// 更新統計資訊（基於當前列表）
const updateStatistics = () => {
  statistics.totalAlarms = alarmHistoryList.value.length
  statistics.criticalAlarms = alarmHistoryList.value.filter(alarm => alarm.AlarmLevel === 1).length
  statistics.warningAlarms = alarmHistoryList.value.filter(alarm => alarm.AlarmLevel === 2).length
  statistics.infoAlarms = alarmHistoryList.value.filter(alarm => alarm.AlarmLevel === 3).length
}

// 搜尋
const handleSearch = () => {
  pagination.pageIndex = 1
  loadAlarmHistory()
}

// 重置
const handleReset = () => {
  searchForm.dateRange = []
  searchForm.alarmLevel = undefined
  searchForm.tagName = ''
  pagination.pageIndex = 1
  loadAlarmHistory()
}

// 匯出
const handleExport = async () => {
  try {
    exporting.value = true

    const params: GetAlarmHistoryRequest = {
      pageIndex: 1,
      pageSize: 10000, // 匯出所有數據
      alarmLevel: searchForm.alarmLevel,
      tagName: searchForm.tagName || undefined
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0]
      params.endTime = searchForm.dateRange[1]
    }

    const response = await alarmAPI.getAlarmHistory(params)
    const data = response.data || []

    // 轉換為 CSV 格式
    const csvContent = convertToCSV(data)

    // 下載檔案
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `alarm_history_${new Date().getTime()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('匯出成功')

  } catch (error) {
    console.error('匯出失敗:', error)
    ElMessage.error('匯出失敗')
  } finally {
    exporting.value = false
  }
}

// 轉換為 CSV
const convertToCSV = (data: AlarmSummary[]): string => {
  const headers = [
    '警報時間', '標籤名稱', '警報訊息', '警報等級', '警報類型',
    '當前值', '警報值', '單位', '確認狀態', '確認人員', '確認時間'
  ]

  const rows = data.map(alarm => [
    formatDateTime(alarm.AlarmTime),
    alarm.TagName,
    alarm.AlarmMessage,
    getAlarmLevelText(alarm.AlarmLevel),
    alarm.AlarmType,
    alarm.CurrentValue ?? '',
    alarm.AlarmValue ?? '',
    alarm.Unit || '',
    alarm.IsAcknowledged ? '已確認' : '未確認',
    alarm.AcknowledgedBy || '',
    alarm.AcknowledgedTime ? formatDateTime(alarm.AcknowledgedTime) : ''
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  return '\uFEFF' + csvContent // 添加 BOM 以支援中文
}

// 分頁大小變更
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageIndex = 1
  loadAlarmHistory()
}

// 當前頁變更
const handleCurrentChange = (page: number) => {
  pagination.pageIndex = page
  loadAlarmHistory()
}

// 選擇變更
const handleSelectionChange = (selection: AlarmSummary[]) => {
  selectedRows.value = selection
}

// 清除選擇
const handleClearSelection = () => {
  selectedRows.value = []
}

// 確認警報
const handleAcknowledge = async (alarm: AlarmSummary) => {
  try {
    await ElMessageBox.confirm(
      `確定要確認警報「${alarm.AlarmMessage}」嗎？`,
      '確認警報',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    acknowledging.value = true

    await alarmAPI.acknowledgeAlarm({
      AlarmSummaryId: alarm.AlarmSummaryId
    })

    ElMessage.success('警報確認成功')
    loadAlarmHistory()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('確認警報失敗:', error)
      ElMessage.error('確認警報失敗')
    }
  } finally {
    acknowledging.value = false
  }
}

// 批量確認警報
const handleBatchAcknowledge = async () => {
  const unacknowledgedAlarms = selectedRows.value.filter(row => !row.IsAcknowledged)

  if (unacknowledgedAlarms.length === 0) {
    ElMessage.warning('沒有需要確認的警報')
    return
  }

  try {
    await ElMessageBox.confirm(
      `確定要批量確認 ${unacknowledgedAlarms.length} 個警報嗎？`,
      '批量確認警報',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchAcknowledging.value = true

    const alarmSummaryIds = unacknowledgedAlarms.map(alarm => alarm.AlarmSummaryId)

    await alarmAPI.batchAcknowledgeAlarms({
      alarmSummaryIds
    })

    ElMessage.success('批量確認成功')
    selectedRows.value = []
    loadAlarmHistory()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量確認失敗:', error)
      ElMessage.error('批量確認失敗')
    }
  } finally {
    batchAcknowledging.value = false
  }
}

// 查看詳情
const handleViewDetail = (alarm: AlarmSummary) => {
  selectedAlarm.value = alarm
  detailDialogVisible.value = true
}

// 從詳情對話框確認警報
const handleAcknowledgeFromDetail = async () => {
  if (!selectedAlarm.value) return

  await handleAcknowledge(selectedAlarm.value)
  detailDialogVisible.value = false
}

// 取得警報等級類型
const getAlarmLevelType = (level: number): string => {
  switch (level) {
    case 1: return 'danger'   // 嚴重
    case 2: return 'warning'  // 警告
    case 3: return 'info'     // 資訊
    default: return 'info'
  }
}

// 取得警報等級文字
const getAlarmLevelText = (level: number): string => {
  switch (level) {
    case 1: return '嚴重'
    case 2: return '警告'
    case 3: return '資訊'
    default: return '未知'
  }
}

// 取得統計標籤類型
const getStatisticsTagType = (type: string): string => {
  switch (type) {
    case 'total': return 'info'
    case 'critical': return 'danger'
    case 'warning': return 'warning'
    case 'info': return 'success'
    default: return 'info'
  }
}

// 組件掛載
onMounted(() => {
  // 設定預設時間範圍（最近7天）
  const endTime = new Date()
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - 7)

  searchForm.dateRange = [
    startTime.toISOString().slice(0, 19).replace('T', ' '),
    endTime.toISOString().slice(0, 19).replace('T', ' ')
  ]

  loadAlarmHistory()
  loadStatistics()
})
</script>

<style scoped>
.alarm-history-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.batch-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: white;
  border-radius: 8px;
  min-width: 300px;
}

.batch-buttons {
  display: flex;
  gap: 10px;
  margin-left: 20px;
}

.alarm-detail {
  max-height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .alarm-history-container {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    flex-wrap: wrap;
  }

  .batch-content {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    min-width: 250px;
  }

  .batch-buttons {
    margin-left: 0;
    justify-content: center;
  }
}

/* 表格樣式優化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table__row:hover) {
  background-color: #f0f9ff;
}

/* 標籤樣式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 按鈕樣式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

/* 表單樣式 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-date-editor) {
  border-radius: 6px;
}

:deep(.el-select) {
  border-radius: 6px;
}

:deep(.el-input) {
  border-radius: 6px;
}

/* 卡片樣式 */
:deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

:deep(.el-card__header) {
  border-bottom: 1px solid #e4e7ed;
  padding: 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 對話框樣式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e4e7ed;
  padding: 20px 20px 15px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #e4e7ed;
  padding: 15px 20px 20px;
}

/* 描述列表樣式 */
:deep(.el-descriptions) {
  border-radius: 8px;
}

:deep(.el-descriptions__header) {
  margin-bottom: 16px;
}

:deep(.el-descriptions__body) {
  background-color: #fafafa;
}

/* 分頁樣式 */
:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
}

/* 載入樣式 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}

/* 空狀態樣式 */
:deep(.el-empty) {
  padding: 60px 0;
}

:deep(.el-empty__image) {
  width: 120px;
  height: 120px;
}

:deep(.el-empty__description) {
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}
</style>
