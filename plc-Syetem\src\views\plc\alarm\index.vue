<template>
  <div class="alarm-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>警報監控系統</h2>
      <p>即時監控系統警報狀態，快速響應異常情況</p>
    </div>

    <!-- 統計卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card critical">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ alarmStats.criticalAlarms }}</div>
                <div class="stat-label">嚴重警報</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ alarmStats.warningAlarms }}</div>
                <div class="stat-label">警告警報</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card unack">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Bell /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ alarmStats.unacknowledgedAlarms }}</div>
                <div class="stat-label">未確認警報</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ alarmStats.totalAlarms }}</div>
                <div class="stat-label">總警報數</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具欄 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="refreshAlarms"
          :loading="loading"
        >
          刷新
        </el-button>
        <el-button 
          type="success" 
          :icon="Check" 
          @click="batchAcknowledge"
          :disabled="selectedAlarms.length === 0"
        >
          批量確認 ({{ selectedAlarms.length }})
        </el-button>
        <el-button 
          type="info" 
          :icon="Setting" 
          @click="showSettings = true"
        >
          設定
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜尋標籤名稱..."
          :prefix-icon="Search"
          clearable
          style="width: 250px"
          @input="handleSearch"
        />
      </div>
    </div>

    <!-- 警報列表 -->
    <el-card class="alarm-table-card">
      <el-table
        ref="alarmTableRef"
        v-loading="loading"
        :data="alarmList"
        @selection-change="handleSelectionChange"
        height="500"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="AlarmTime" label="警報時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.AlarmTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="TagName" label="標籤名稱" width="150" />
        
        <el-table-column prop="AlarmMessage" label="警報訊息" min-width="200" />
        
        <el-table-column prop="AlarmLevel" label="警報等級" width="100">
          <template #default="{ row }">
            <el-tag :type="getAlarmLevelType(row.AlarmLevel)">
              {{ getAlarmLevelText(row.AlarmLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="CurrentValue" label="當前值" width="100">
          <template #default="{ row }">
            {{ row.CurrentValue ?? '-' }} {{ row.Unit || '' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="IsAcknowledged" label="確認狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="row.IsAcknowledged ? 'success' : 'danger'">
              {{ row.IsAcknowledged ? '已確認' : '未確認' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="AcknowledgedBy" label="確認人員" width="120">
          <template #default="{ row }">
            {{ row.AcknowledgedBy || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="!row.IsAcknowledged"
              type="primary"
              size="small"
              @click="acknowledgeAlarm(row)"
            >
              確認
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewAlarmDetail(row)"
            >
              詳情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageIndex"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 警報設定對話框 -->
    <el-dialog
      v-model="showSettings"
      title="警報設定"
      width="500px"
    >
      <el-form :model="alarmSettings" label-width="120px">
        <el-form-item label="啟用聲音提醒">
          <el-switch v-model="alarmSettings.enableSound" />
        </el-form-item>
        <el-form-item label="啟用彈窗提醒">
          <el-switch v-model="alarmSettings.enablePopup" />
        </el-form-item>
        <el-form-item label="自動刷新間隔">
          <el-select v-model="alarmSettings.autoRefreshInterval">
            <el-option label="5秒" :value="5000" />
            <el-option label="10秒" :value="10000" />
            <el-option label="30秒" :value="30000" />
            <el-option label="1分鐘" :value="60000" />
            <el-option label="關閉" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="最大顯示數量">
          <el-input-number 
            v-model="alarmSettings.maxDisplayCount" 
            :min="10" 
            :max="1000" 
            :step="10"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Warning, 
  InfoFilled, 
  Bell, 
  DataBoard, 
  Refresh, 
  Check, 
  Setting, 
  Search 
} from '@element-plus/icons-vue'
import { alarmAPI, type AlarmSummary } from '@/api/plc/alarm'
import { plcSignalRService } from '@/utils/plc/signalr'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 響應式數據
const loading = ref(false)
const alarmList = ref<AlarmSummary[]>([])
const selectedAlarms = ref<AlarmSummary[]>([])
const searchKeyword = ref('')
const showSettings = ref(false)

// 統計數據
const alarmStats = reactive({
  totalAlarms: 0,
  unacknowledgedAlarms: 0,
  criticalAlarms: 0,
  warningAlarms: 0,
  infoAlarms: 0
})

// 分頁數據
const pagination = reactive({
  pageIndex: 1,
  pageSize: 20,
  total: 0
})

// 警報設定
const alarmSettings = reactive({
  enableSound: true,
  enablePopup: true,
  autoRefreshInterval: 10000,
  maxDisplayCount: 100
})

// 自動刷新定時器
let refreshTimer: NodeJS.Timeout | null = null

/**
 * 載入警報列表
 */
const loadAlarmList = async () => {
  try {
    loading.value = true
    
    const params = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      tagName: searchKeyword.value || undefined,
      customerId: authStore.userInfo.customerId
    }
    
    const response = await alarmAPI.getAlarmList(params)
    
    alarmList.value = response.items || []
    pagination.total = response.totalCount || 0
    
  } catch (error: any) {
    console.error('載入警報列表失敗:', error)
    ElMessage.error(error.message || '載入警報列表失敗')
  } finally {
    loading.value = false
  }
}

/**
 * 載入警報統計
 */
const loadAlarmStats = async () => {
  try {
    const stats = await alarmAPI.getAlarmStatistics(authStore.userInfo.customerId)
    Object.assign(alarmStats, stats)
  } catch (error: any) {
    console.error('載入警報統計失敗:', error)
  }
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 獲取警報等級類型
 */
const getAlarmLevelType = (level: number): string => {
  switch (level) {
    case 1: return 'danger'   // 嚴重
    case 2: return 'warning'  // 警告
    case 3: return 'info'     // 資訊
    default: return 'info'
  }
}

/**
 * 獲取警報等級文字
 */
const getAlarmLevelText = (level: number): string => {
  switch (level) {
    case 1: return '嚴重'
    case 2: return '警告'
    case 3: return '資訊'
    default: return '未知'
  }
}

/**
 * 刷新警報
 */
const refreshAlarms = async () => {
  await Promise.all([
    loadAlarmList(),
    loadAlarmStats()
  ])
}

/**
 * 處理搜尋
 */
const handleSearch = () => {
  pagination.pageIndex = 1
  loadAlarmList()
}

/**
 * 處理選擇變更
 */
const handleSelectionChange = (selection: AlarmSummary[]) => {
  selectedAlarms.value = selection
}

/**
 * 處理頁面大小變更
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageIndex = 1
  loadAlarmList()
}

/**
 * 處理當前頁變更
 */
const handleCurrentChange = (page: number) => {
  pagination.pageIndex = page
  loadAlarmList()
}

/**
 * 確認單個警報
 */
const acknowledgeAlarm = async (alarm: AlarmSummary) => {
  try {
    await alarmAPI.acknowledgeAlarm({
      AlarmSummaryId: alarm.AlarmSummaryId
    })
    
    ElMessage.success('警報確認成功')
    await refreshAlarms()
  } catch (error: any) {
    console.error('確認警報失敗:', error)
    ElMessage.error(error.message || '確認警報失敗')
  }
}

/**
 * 批量確認警報
 */
const batchAcknowledge = async () => {
  if (selectedAlarms.value.length === 0) {
    ElMessage.warning('請選擇要確認的警報')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `確定要確認選中的 ${selectedAlarms.value.length} 個警報嗎？`,
      '批量確認警報',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const alarmIds = selectedAlarms.value.map(alarm => alarm.AlarmSummaryId)
    
    await alarmAPI.batchAcknowledgeAlarms({
      alarmSummaryIds: alarmIds
    })
    
    ElMessage.success('批量確認成功')
    selectedAlarms.value = []
    await refreshAlarms()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量確認失敗:', error)
      ElMessage.error(error.message || '批量確認失敗')
    }
  }
}

/**
 * 查看警報詳情
 */
const viewAlarmDetail = (alarm: AlarmSummary) => {
  // TODO: 實現警報詳情對話框
  ElMessage.info('警報詳情功能開發中...')
}

/**
 * 保存設定
 */
const saveSettings = async () => {
  try {
    await alarmAPI.updateAlarmSettings({
      customerId: authStore.userInfo.customerId,
      ...alarmSettings
    })
    
    ElMessage.success('設定保存成功')
    showSettings.value = false
    
    // 重新設置自動刷新
    setupAutoRefresh()
  } catch (error: any) {
    console.error('保存設定失敗:', error)
    ElMessage.error(error.message || '保存設定失敗')
  }
}

/**
 * 設置自動刷新
 */
const setupAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  
  if (alarmSettings.autoRefreshInterval > 0) {
    refreshTimer = setInterval(() => {
      refreshAlarms()
    }, alarmSettings.autoRefreshInterval)
  }
}

/**
 * 載入警報設定
 */
const loadAlarmSettings = async () => {
  try {
    const settings = await alarmAPI.getAlarmSettings(authStore.userInfo.customerId)
    Object.assign(alarmSettings, settings)
    setupAutoRefresh()
  } catch (error: any) {
    console.error('載入警報設定失敗:', error)
  }
}

/**
 * 設置 SignalR 事件監聽
 */
const setupSignalRListeners = () => {
  // 監聽即時警報數據
  plcSignalRService.on('alarm:data', (data: any) => {
    console.log('收到即時警報:', data)
    // 更新警報列表
    refreshAlarms()
    
    // 播放聲音提醒
    if (alarmSettings.enableSound) {
      // TODO: 播放警報聲音
    }
    
    // 顯示彈窗提醒
    if (alarmSettings.enablePopup) {
      ElMessage({
        message: `新警報: ${data.alarm.TagName} - ${data.alarm.AlarmMessage}`,
        type: 'warning',
        duration: 5000
      })
    }
  })
  
  // 監聽警報確認事件
  plcSignalRService.on('alarm:acknowledged', (data: any) => {
    console.log('警報已確認:', data)
    refreshAlarms()
  })
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadAlarmList(),
    loadAlarmStats(),
    loadAlarmSettings()
  ])
  
  // 設置 SignalR 監聽
  setupSignalRListeners()
  
  // 連接警報 Hub
  try {
    await plcSignalRService.connectHub('alarm')
  } catch (error) {
    console.error('連接警報 Hub 失敗:', error)
  }
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  
  // 移除 SignalR 監聽
  plcSignalRService.off('alarm:data')
  plcSignalRService.off('alarm:acknowledged')
})
</script>

<style scoped>
.alarm-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-card.critical .stat-icon {
  color: #f56c6c;
}

.stat-card.warning .stat-icon {
  color: #e6a23c;
}

.stat-card.unack .stat-icon {
  color: #409eff;
}

.stat-card.total .stat-icon {
  color: #67c23a;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-card.critical .stat-value {
  color: #f56c6c;
}

.stat-card.warning .stat-value {
  color: #e6a23c;
}

.stat-card.unack .stat-value {
  color: #409eff;
}

.stat-card.total .stat-value {
  color: #67c23a;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.alarm-table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-table .el-table__row.current-row) {
  background-color: #f5f7fa;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
