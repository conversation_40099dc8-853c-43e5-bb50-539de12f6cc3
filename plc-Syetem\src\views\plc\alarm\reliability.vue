<template>
  <div class="alarm-reliability-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>警報可靠性分析</span>
        </div>
      </template>
      
      <div class="content">
        <el-empty description="警報可靠性分析功能開發中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 警報可靠性分析頁面
</script>

<style scoped>
.alarm-reliability-container {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
