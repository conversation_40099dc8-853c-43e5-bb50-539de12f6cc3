<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>PLC 系統登入</h2>
        <p>請輸入您的帳號和密碼</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="account">
          <el-input
            v-model="loginForm.account"
            placeholder="請輸入帳號"
            size="large"
            clearable
            :prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="請輸入密碼"
            size="large"
            clearable
            show-password
            :prefix-icon="Lock"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            class="login-button"
            @click="handleLogin"
          >
            {{ loading ? '登入中...' : '登入' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>PLC 控制系統 v1.0</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElForm } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { usePLCAuthStore } from '@/store/modules/plc-auth'
import { isPLCLoggedIn } from '@/utils/plc/auth'

const router = useRouter()
const authStore = usePLCAuthStore()

// 表單引用
const loginFormRef = ref<InstanceType<typeof ElForm>>()

// 載入狀態
const loading = ref(false)

// 登入表單 - 預設測試帳號
const loginForm = reactive({
  account: '<EMAIL>',
  password: '111111'
})

// 表單驗證規則
const loginRules = {
  account: [
    { required: true, message: '請輸入帳號', trigger: 'blur' },
    { min: 3, max: 50, message: '帳號長度應為 3-50 個字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' },
    { min: 1, max: 100, message: '密碼長度應為 1-100 個字符', trigger: 'blur' }
  ]
}

// 獲取客戶ID
const getCustomerId = (): string => {
  const storedCustomerId = localStorage.getItem('PLC_CUSTOMER_ID')
  if (storedCustomerId) {
    return storedCustomerId
  }
  const envCustomerId = import.meta.env.VITE_CUSTOMER_ID
  if (envCustomerId) {
    return envCustomerId
  }
  return "fdff1878-a54a-44ee-b82c-a62bdc5cdb55"
}

/**
 * 處理登入 - 直接調用舊系統API
 */
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 驗證表單
    await loginFormRef.value.validate()

    loading.value = true

    const customerId = getCustomerId()

    console.log('=== PLC 系統登入 ===')
    console.log('登入資料:', { Account: loginForm.account, IdName: customerId })
    console.log('使用的API端點:', 'http://192.168.1.152:8345/api/Staff/StaffLogin')

    // 完全按照舊前端的方式：使用 application/x-www-form-urlencoded
    const formData = new URLSearchParams()
    formData.append('Account', loginForm.account)
    formData.append('Password', loginForm.password)
    formData.append('IdName', customerId)

    console.log('發送的表單數據:', formData.toString())

    const response = await fetch('http://192.168.1.152:8345/api/Staff/StaffLogin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    })

    console.log('HTTP 狀態:', response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    console.log('後端回應:', result)

    // 檢查登入是否成功 - 按照舊前端的邏輯
    if (!result.Detail) {
      const errorMessage = result.Message || "登入失敗，請檢查登入資料"
      console.error("登入失敗:", errorMessage)
      throw new Error(errorMessage)
    }

    const {
      AccessToken,
      RefreshToken,
      StaffName,
      PermissionCode,
      RoleId,
      UniformNumber,
      CustomerID,
      CustomerName,
      EnableState,
    } = result.Detail

    // 完全按照舊前端的方式保存token
    localStorage.setItem('access_token', AccessToken)
    localStorage.setItem('refresh_token', RefreshToken)
    localStorage.setItem('brand_id', customerId)
    localStorage.setItem('customer_id', CustomerID)

    console.log('✅ 登入成功！')
    console.log('Token已保存:', AccessToken.substring(0, 20) + '...')
    console.log('客戶ID:', CustomerID)
    console.log('員工姓名:', StaffName)

    ElMessage.success(`登入成功！歡迎 ${StaffName}`)

    // 跳轉到標籤管理頁面
    console.log('準備跳轉到標籤管理頁面...')

    try {
      await router.push('/plc-tags/tag')
      console.log('✅ 頁面跳轉成功')
    } catch (routerError: any) {
      console.error('❌ 路由跳轉失敗:', routerError)
      // 如果跳轉失敗，嘗試跳轉到首頁
      try {
        await router.push('/')
        console.log('✅ 跳轉到首頁成功')
      } catch (homeError: any) {
        console.error('❌ 跳轉到首頁也失敗:', homeError)
        // 強制重新載入頁面
        window.location.href = '/plc-tags/tag'
      }
    }

  } catch (error: any) {
    console.error('❌ 登入失敗:', error)
    ElMessage.error(`登入失敗: ${error.message}`)
  } finally {
    loading.value = false
  }
}

/**
 * 檢查登入狀態
 */
onMounted(() => {
  // 如果已經登入，直接跳轉到首頁
  if (isPLCLoggedIn()) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
  font-weight: 600;
}

.login-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
}

:deep(.el-input__wrapper) {
  padding: 12px 15px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
