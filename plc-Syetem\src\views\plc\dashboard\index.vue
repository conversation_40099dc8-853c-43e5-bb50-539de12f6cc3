<template>
  <div class="dashboard-container">
    <!-- 歡迎區域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>歡迎回來，{{ userInfo.staffName || userInfo.account }}</h2>
            <p>PLC 控制系統運行正常，所有服務已就緒</p>
          </div>
          <div class="welcome-time">
            <div class="current-time">{{ currentTime }}</div>
            <div class="current-date">{{ currentDate }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 系統狀態概覽 -->
    <div class="system-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon online">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-title">系統狀態</div>
                <div class="status-value online">運行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-title">連接設備</div>
                <div class="status-value">{{ systemStats.connectedDevices }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-title">活躍警報</div>
                <div class="status-value warning">{{ systemStats.activeAlarms }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-title">在線用戶</div>
                <div class="status-value">{{ systemStats.onlineUsers }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>快速操作</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="4" v-for="action in quickActions" :key="action.name">
            <div class="action-item" @click="handleQuickAction(action)">
              <div class="action-icon" :style="{ backgroundColor: action.color }">
                <el-icon><component :is="action.icon" /></el-icon>
              </div>
              <div class="action-text">{{ action.name }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 最近警報 -->
    <div class="recent-alarms">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近警報</span>
            <el-button type="text" @click="$router.push('/plc-alarm/monitor')">
              查看全部
            </el-button>
          </div>
        </template>
        
        <el-table :data="recentAlarms" style="width: 100%" max-height="300">
          <el-table-column prop="AlarmTime" label="時間" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.AlarmTime) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="TagName" label="標籤" width="150" />
          
          <el-table-column prop="AlarmMessage" label="訊息" min-width="200" />
          
          <el-table-column prop="AlarmLevel" label="等級" width="100">
            <template #default="{ row }">
              <el-tag :type="getAlarmLevelType(row.AlarmLevel)">
                {{ getAlarmLevelText(row.AlarmLevel) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="IsAcknowledged" label="狀態" width="100">
            <template #default="{ row }">
              <el-tag :type="row.IsAcknowledged ? 'success' : 'danger'">
                {{ row.IsAcknowledged ? '已確認' : '未確認' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- SignalR 連接狀態 -->
    <div class="signalr-status">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>服務連接狀態</span>
            <el-button 
              type="text" 
              @click="reconnectAllHubs"
              :loading="reconnecting"
            >
              重新連接
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="4" v-for="(state, hubName) in hubStates" :key="hubName">
            <div class="hub-status">
              <div class="hub-icon" :class="getHubStatusClass(state)">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="hub-name">{{ getHubDisplayName(hubName) }}</div>
              <div class="hub-state">{{ getHubStateText(state) }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Connection, 
  Monitor, 
  Warning, 
  User,
  Bell,
  DataBoard,
  Setting,
  Document,
  VideoCamera,
  Cpu
} from '@element-plus/icons-vue'
import { usePLCAuthStore } from '@/store/modules/plc-auth'
import { plcSignalRService, HubConnectionState } from '@/utils/plc/signalr'
import { alarmAPI, type AlarmSummary } from '@/api/plc/alarm'

const router = useRouter()
const authStore = usePLCAuthStore()

// 響應式數據
const currentTime = ref('')
const currentDate = ref('')
const reconnecting = ref(false)
const recentAlarms = ref<AlarmSummary[]>([])
const hubStates = ref<Record<string, HubConnectionState>>({})

// 系統統計
const systemStats = reactive({
  connectedDevices: 12,
  activeAlarms: 0,
  onlineUsers: 3
})

// 用戶資訊
const userInfo = authStore.userInfo

// 快速操作
const quickActions = [
  { name: '警報監控', icon: 'Warning', color: '#f56c6c', route: '/plc-alarm/monitor' },
  { name: '即時數據', icon: 'DataBoard', color: '#409eff', route: '/plc-database/realtime' },
  { name: 'GUI監控', icon: 'Monitor', color: '#67c23a', route: '/plc-gui/monitor' },
  { name: 'CCTV', icon: 'VideoCamera', color: '#e6a23c', route: '/plc-cctv/monitor' },
  { name: '系統設定', icon: 'Setting', color: '#909399', route: '/plc-system/settings' },
  { name: '用戶管理', icon: 'User', color: '#606266', route: '/plc-user/list' }
]

/**
 * 更新時間
 */
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-TW')
  currentDate.value = now.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

/**
 * 載入最近警報
 */
const loadRecentAlarms = async () => {
  try {
    const response = await alarmAPI.getAlarmList({
      pageIndex: 1,
      pageSize: 5,
      customerId: userInfo.customerId
    })
    
    recentAlarms.value = response.items || []
    systemStats.activeAlarms = response.items?.filter(alarm => !alarm.IsAcknowledged).length || 0
  } catch (error) {
    console.error('載入最近警報失敗:', error)
  }
}

/**
 * 更新 Hub 狀態
 */
const updateHubStates = () => {
  hubStates.value = plcSignalRService.getAllHubStates()
}

/**
 * 重新連接所有 Hub
 */
const reconnectAllHubs = async () => {
  try {
    reconnecting.value = true
    await plcSignalRService.connectAllHubs()
    updateHubStates()
  } catch (error) {
    console.error('重新連接失敗:', error)
  } finally {
    reconnecting.value = false
  }
}

/**
 * 處理快速操作
 */
const handleQuickAction = (action: any) => {
  if (action.route) {
    router.push(action.route)
  }
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 獲取警報等級類型
 */
const getAlarmLevelType = (level: number): string => {
  switch (level) {
    case 1: return 'danger'
    case 2: return 'warning'
    case 3: return 'info'
    default: return 'info'
  }
}

/**
 * 獲取警報等級文字
 */
const getAlarmLevelText = (level: number): string => {
  switch (level) {
    case 1: return '嚴重'
    case 2: return '警告'
    case 3: return '資訊'
    default: return '未知'
  }
}

/**
 * 獲取 Hub 狀態樣式類
 */
const getHubStatusClass = (state: HubConnectionState): string => {
  switch (state) {
    case HubConnectionState.Connected: return 'connected'
    case HubConnectionState.Connecting: return 'connecting'
    case HubConnectionState.Reconnecting: return 'reconnecting'
    case HubConnectionState.Disconnecting: return 'disconnecting'
    default: return 'disconnected'
  }
}

/**
 * 獲取 Hub 顯示名稱
 */
const getHubDisplayName = (hubName: string): string => {
  const nameMap: Record<string, string> = {
    alarm: '警報',
    pageTag: '頁面標籤',
    desigoCC: 'DesigoCC',
    tagModel: '標籤模型',
    obix: 'Obix',
    cctv: 'CCTV'
  }
  return nameMap[hubName] || hubName
}

/**
 * 獲取 Hub 狀態文字
 */
const getHubStateText = (state: HubConnectionState): string => {
  switch (state) {
    case HubConnectionState.Connected: return '已連接'
    case HubConnectionState.Connecting: return '連接中'
    case HubConnectionState.Reconnecting: return '重連中'
    case HubConnectionState.Disconnecting: return '斷開中'
    default: return '已斷開'
  }
}

// 定時器
let timeTimer: NodeJS.Timeout | null = null
let statusTimer: NodeJS.Timeout | null = null

// 生命週期
onMounted(async () => {
  // 更新時間
  updateTime()
  timeTimer = setInterval(updateTime, 1000)
  
  // 載入數據
  await loadRecentAlarms()
  
  // 更新 Hub 狀態
  updateHubStates()
  statusTimer = setInterval(updateHubStates, 5000)
  
  // 嘗試連接所有 Hub
  try {
    await plcSignalRService.connectAllHubs()
    updateHubStates()
  } catch (error) {
    console.error('連接 Hub 失敗:', error)
  }
})

onUnmounted(() => {
  if (timeTimer) {
    clearInterval(timeTimer)
  }
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
}

.current-time {
  font-size: 28px;
  font-weight: bold;
  text-align: right;
}

.current-date {
  font-size: 14px;
  opacity: 0.9;
  text-align: right;
}

.system-overview {
  margin-bottom: 20px;
}

.status-card {
  height: 100px;
}

.status-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  font-size: 32px;
  margin-right: 16px;
  color: #409eff;
}

.status-icon.online {
  color: #67c23a;
}

.status-icon.warning {
  color: #e6a23c;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.status-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.status-value.online {
  color: #67c23a;
}

.status-value.warning {
  color: #e6a23c;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s;
}

.action-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-bottom: 8px;
}

.action-text {
  font-size: 14px;
  color: #303133;
  text-align: center;
}

.recent-alarms {
  margin-bottom: 20px;
}

.signalr-status {
  margin-bottom: 20px;
}

.hub-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  text-align: center;
}

.hub-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin-bottom: 8px;
}

.hub-icon.connected {
  background-color: #67c23a;
}

.hub-icon.connecting,
.hub-icon.reconnecting {
  background-color: #e6a23c;
}

.hub-icon.disconnected,
.hub-icon.disconnecting {
  background-color: #f56c6c;
}

.hub-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.hub-state {
  font-size: 12px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.welcome-card .el-card__body) {
  background: transparent;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
