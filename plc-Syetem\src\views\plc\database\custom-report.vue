<template>
  <div class="custom-report-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>匯出報表</h2>
      <p>自定義報表匯出功能，支援多種格式和時間範圍</p>
    </div>

    <!-- 報表設定 -->
    <el-card class="report-config-card">
      <template #header>
        <div class="card-header">
          <span>報表設定</span>
        </div>
      </template>

      <el-form
        ref="reportFormRef"
        :model="reportForm"
        :rules="reportRules"
        label-width="120px"
        class="report-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="報表模板" prop="template">
              <el-select
                v-model="reportForm.template"
                placeholder="請選擇報表模板"
                style="width: 100%"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in reportTemplates"
                  :key="template.id"
                  :label="template.name"
                  :value="template.id"
                >
                  <div>
                    <span>{{ template.name }}</span>
                    <div style="font-size: 12px; color: #8492a6;">
                      {{ template.description }}
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="報表名稱" prop="name">
              <el-input
                v-model="reportForm.name"
                placeholder="請輸入報表名稱"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="時間範圍" prop="dateRange">
              <el-date-picker
                v-model="reportForm.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="開始時間"
                end-placeholder="結束時間"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="匯出格式" prop="format">
              <el-select v-model="reportForm.format" style="width: 100%">
                <el-option label="Excel (.xlsx)" value="xlsx" />
                <el-option label="CSV (.csv)" value="csv" />
                <el-option label="PDF (.pdf)" value="pdf" />
                <el-option label="JSON (.json)" value="json" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="選擇標籤" prop="selectedTags">
          <el-select
            v-model="reportForm.selectedTags"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="請選擇要包含在報表中的標籤"
            :remote-method="searchTags"
            :loading="tagSearchLoading"
            style="width: 100%"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            >
              <span style="float: left">{{ tag.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ tag.description }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="報表選項">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-checkbox v-model="reportForm.includeChart">包含圖表</el-checkbox>
            </el-col>
            <el-col :span="8">
              <el-checkbox v-model="reportForm.includeStatistics">包含統計資訊</el-checkbox>
            </el-col>
            <el-col :span="8">
              <el-checkbox v-model="reportForm.includeAlarms">包含警報記錄</el-checkbox>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="數據取樣">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-select v-model="reportForm.samplingInterval" style="width: 100%">
                <el-option label="原始數據" value="raw" />
                <el-option label="1分鐘" value="1m" />
                <el-option label="5分鐘" value="5m" />
                <el-option label="15分鐘" value="15m" />
                <el-option label="30分鐘" value="30m" />
                <el-option label="1小時" value="1h" />
                <el-option label="1天" value="1d" />
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select v-model="reportForm.aggregationType" style="width: 100%">
                <el-option label="平均值" value="avg" />
                <el-option label="最大值" value="max" />
                <el-option label="最小值" value="min" />
                <el-option label="總和" value="sum" />
                <el-option label="計數" value="count" />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="generating"
            :disabled="!reportForm.template || !reportForm.selectedTags.length || !reportForm.dateRange"
            @click="generateReport"
          >
            {{ generating ? '生成中...' : '生成報表' }}
          </el-button>
          <el-button
            type="success"
            :disabled="!reportForm.template"
            @click="previewReport"
          >
            預覽報表
          </el-button>
          <el-button
            type="info"
            @click="saveTemplate"
          >
            保存為模板
          </el-button>
          <el-button
            type="warning"
            @click="resetForm"
          >
            重置表單
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 快速時間選擇 -->
    <el-card class="quick-time-card">
      <template #header>
        <div class="card-header">
          <span>快速時間選擇</span>
        </div>
      </template>
      <div class="quick-time-buttons">
        <el-button
          v-for="period in quickTimePeriods"
          :key="period.key"
          @click="setQuickTime(period)"
        >
          {{ period.label }}
        </el-button>
      </div>
    </el-card>

    <!-- 報表歷史 -->
    <el-card class="report-history-card">
      <template #header>
        <div class="card-header">
          <span>報表歷史</span>
          <div class="header-actions">
            <el-button
              type="primary"
              @click="refreshReportHistory"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="historyLoading"
        :data="reportHistory"
        stripe
        border
      >
        <el-table-column prop="name" label="報表名稱" width="200" />
        <el-table-column prop="template" label="模板" width="150" />
        <el-table-column prop="format" label="格式" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.format.toUpperCase() }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="檔案大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="建立時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              :disabled="row.status !== 'completed'"
              @click="downloadReport(row)"
            >
              下載
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="viewReportDetail(row)"
            >
              詳情
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteReport(row)"
            >
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 報表預覽對話框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="報表預覽"
      width="80%"
      :before-close="closePreview"
    >
      <div v-loading="previewLoading" class="preview-content">
        <div v-if="previewData" class="preview-data">
          <!-- 這裡可以根據報表類型顯示不同的預覽內容 -->
          <el-table :data="previewData.slice(0, 10)" stripe border>
            <el-table-column
              v-for="column in previewColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
            />
          </el-table>
          <div v-if="previewData.length > 10" class="preview-note">
            <el-alert
              title="預覽僅顯示前10筆數據，完整報表請下載查看"
              type="info"
              :closable="false"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showPreviewDialog = false">關閉</el-button>
        <el-button type="primary" @click="generateReportFromPreview">
          確認生成報表
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { customReportAPI, type ReportTemplate, type TagInfo, type ReportHistoryItem } from '@/api/plc/database'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const reportFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const generating = ref(false)
const historyLoading = ref(false)
const tagSearchLoading = ref(false)
const previewLoading = ref(false)
const showPreviewDialog = ref(false)

// 報表表單
const reportForm = reactive({
  template: '',
  name: '',
  dateRange: [] as string[],
  format: 'xlsx',
  selectedTags: [] as string[],
  includeChart: true,
  includeStatistics: true,
  includeAlarms: false,
  samplingInterval: '1h',
  aggregationType: 'avg'
})

// 表單驗證規則
const reportRules = {
  template: [
    { required: true, message: '請選擇報表模板', trigger: 'change' }
  ],
  name: [
    { required: true, message: '請輸入報表名稱', trigger: 'blur' }
  ],
  dateRange: [
    { required: true, message: '請選擇時間範圍', trigger: 'change' }
  ],
  selectedTags: [
    { required: true, message: '請選擇至少一個標籤', trigger: 'change' }
  ]
}

// 數據
const reportTemplates = ref<ReportTemplate[]>([])
const availableTags = ref<TagInfo[]>([])
const reportHistory = ref<ReportHistoryItem[]>([])
const previewData = ref<any[]>([])
const previewColumns = ref<any[]>([])

// 快速時間選擇選項
const quickTimePeriods = ref([
  { key: 'last1h', label: '最近1小時', hours: 1 },
  { key: 'last6h', label: '最近6小時', hours: 6 },
  { key: 'last24h', label: '最近24小時', hours: 24 },
  { key: 'last7d', label: '最近7天', days: 7 },
  { key: 'last30d', label: '最近30天', days: 30 },
  { key: 'thisMonth', label: '本月', type: 'month' },
  { key: 'lastMonth', label: '上月', type: 'lastMonth' }
])
</script>
