<template>
  <div class="history-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>歷史報表</h2>
      <p>查詢、排程和下載歷史數據報表</p>
    </div>

    <!-- 主要內容 -->
    <el-card class="main-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 查詢標籤頁 -->
        <el-tab-pane label="查詢" name="search">
          <div class="tab-content">
            <!-- 查詢條件 -->
            <el-form
              ref="searchFormRef"
              :model="searchForm"
              :rules="searchRules"
              label-width="120px"
              class="search-form"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="選擇標籤" prop="selectedTags">
                    <el-select
                      v-model="searchForm.selectedTags"
                      multiple
                      filterable
                      remote
                      reserve-keyword
                      placeholder="請選擇要查詢的標籤"
                      :remote-method="searchTags"
                      :loading="tagSearchLoading"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="tag in availableTags"
                        :key="tag.id"
                        :label="tag.name"
                        :value="tag.id"
                      >
                        <span style="float: left">{{ tag.name }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">
                          {{ tag.description }}
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="時間範圍" prop="dateRange">
                    <el-date-picker
                      v-model="searchForm.dateRange"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="開始時間"
                      end-placeholder="結束時間"
                      format="YYYY-MM-DD HH:mm:ss"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="數據類型">
                    <el-select v-model="searchForm.dataType" style="width: 100%">
                      <el-option label="原始數據" value="raw" />
                      <el-option label="平均值" value="average" />
                      <el-option label="最大值" value="max" />
                      <el-option label="最小值" value="min" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="取樣間隔">
                    <el-select v-model="searchForm.interval" style="width: 100%">
                      <el-option label="1分鐘" value="1m" />
                      <el-option label="5分鐘" value="5m" />
                      <el-option label="15分鐘" value="15m" />
                      <el-option label="30分鐘" value="30m" />
                      <el-option label="1小時" value="1h" />
                      <el-option label="1天" value="1d" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item>
                <el-button
                  type="primary"
                  :loading="searchLoading"
                  :disabled="!searchForm.selectedTags.length || !searchForm.dateRange"
                  @click="handleSearch"
                >
                  {{ searchLoading ? '查詢中...' : '開始查詢' }}
                </el-button>
                <el-button
                  type="success"
                  :disabled="!hasSearchData"
                  @click="exportSearchData"
                >
                  匯出結果
                </el-button>
                <el-button
                  type="warning"
                  :disabled="!hasSearchData"
                  @click="clearSearchData"
                >
                  清除結果
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 查詢結果 -->
            <div v-if="hasSearchData" class="search-results">
              <el-card>
                <template #header>
                  <div class="card-header">
                    <span>查詢結果 ({{ searchData.length }} 筆)</span>
                    <div class="header-actions">
                      <el-input
                        v-model="searchKeyword"
                        placeholder="搜尋標籤名稱..."
                        :prefix-icon="Search"
                        clearable
                        style="width: 250px"
                        @input="handleSearchFilter"
                      />
                    </div>
                  </div>
                </template>

                <el-table
                  v-loading="searchLoading"
                  :data="filteredSearchData"
                  height="400"
                  stripe
                  border
                >
                  <el-table-column prop="TagName" label="標籤名稱" width="200" fixed="left" />
                  <el-table-column prop="Value" label="數值" width="120" align="right">
                    <template #default="{ row }">
                      {{ formatValue(row.Value) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="Unit" label="單位" width="80" />
                  <el-table-column prop="Timestamp" label="時間戳" width="180">
                    <template #default="{ row }">
                      {{ formatDateTime(row.Timestamp) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="Quality" label="品質" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getQualityType(row.Quality)">
                        {{ getQualityText(row.Quality) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="Description" label="描述" min-width="200" />
                </el-table>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 排程標籤頁 -->
        <el-tab-pane label="排程" name="schedule">
          <div class="tab-content">
            <!-- 排程列表 -->
            <div class="schedule-header">
              <h3>報表排程管理</h3>
              <el-button type="primary" @click="showAddScheduleDialog = true">
                新增排程
              </el-button>
            </div>

            <el-table
              v-loading="scheduleLoading"
              :data="scheduleList"
              stripe
              border
            >
              <el-table-column prop="name" label="排程名稱" width="200" />
              <el-table-column prop="description" label="描述" min-width="200" />
              <el-table-column prop="frequency" label="頻率" width="120">
                <template #default="{ row }">
                  {{ getFrequencyText(row.frequency) }}
                </template>
              </el-table-column>
              <el-table-column prop="nextRun" label="下次執行" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.nextRun) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="狀態" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                    {{ row.status === 'active' ? '啟用' : '停用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editSchedule(row)"
                  >
                    編輯
                  </el-button>
                  <el-button
                    :type="row.status === 'active' ? 'warning' : 'success'"
                    size="small"
                    @click="toggleScheduleStatus(row)"
                  >
                    {{ row.status === 'active' ? '停用' : '啟用' }}
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteSchedule(row)"
                  >
                    刪除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 下載標籤頁 -->
        <el-tab-pane label="下載" name="download">
          <div class="tab-content">
            <!-- 下載歷史 -->
            <div class="download-header">
              <h3>下載歷史</h3>
              <el-button type="primary" @click="refreshDownloadList">
                刷新列表
              </el-button>
            </div>

            <el-table
              v-loading="downloadLoading"
              :data="downloadList"
              stripe
              border
            >
              <el-table-column prop="fileName" label="檔案名稱" width="300" />
              <el-table-column prop="fileSize" label="檔案大小" width="120">
                <template #default="{ row }">
                  {{ formatFileSize(row.fileSize) }}
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="建立時間" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="狀態" width="100">
                <template #default="{ row }">
                  <el-tag :type="getDownloadStatusType(row.status)">
                    {{ getDownloadStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="200" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="row.status !== 'completed'"
                    @click="downloadFile(row)"
                  >
                    下載
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteDownloadFile(row)"
                  >
                    刪除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 新增排程對話框 -->
    <el-dialog
      v-model="showAddScheduleDialog"
      title="新增報表排程"
      width="600px"
    >
      <el-form
        ref="scheduleFormRef"
        :model="scheduleForm"
        :rules="scheduleRules"
        label-width="120px"
      >
        <el-form-item label="排程名稱" prop="name">
          <el-input v-model="scheduleForm.name" placeholder="請輸入排程名稱" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="scheduleForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入排程描述"
          />
        </el-form-item>
        <el-form-item label="執行頻率" prop="frequency">
          <el-select v-model="scheduleForm.frequency" style="width: 100%">
            <el-option label="每日" value="daily" />
            <el-option label="每週" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>
        <el-form-item label="執行時間" prop="executeTime">
          <el-time-picker
            v-model="scheduleForm.executeTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="選擇執行時間"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddScheduleDialog = false">取消</el-button>
        <el-button type="primary" @click="saveSchedule">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { historyDataAPI, type HistoryDataItem, type TagInfo, type ScheduleItem, type DownloadItem } from '@/api/plc/database'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const searchFormRef = ref<InstanceType<typeof ElForm>>()
const scheduleFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const activeTab = ref('search')
const searchLoading = ref(false)
const scheduleLoading = ref(false)
const downloadLoading = ref(false)
const tagSearchLoading = ref(false)
const searchKeyword = ref('')
const showAddScheduleDialog = ref(false)

// 查詢表單
const searchForm = reactive({
  selectedTags: [] as string[],
  dateRange: [] as string[],
  dataType: 'raw',
  interval: '1m'
})

// 排程表單
const scheduleForm = reactive({
  name: '',
  description: '',
  frequency: 'daily',
  executeTime: ''
})

// 表單驗證規則
const searchRules = {
  selectedTags: [
    { required: true, message: '請選擇至少一個標籤', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '請選擇時間範圍', trigger: 'change' }
  ]
}

const scheduleRules = {
  name: [
    { required: true, message: '請輸入排程名稱', trigger: 'blur' }
  ],
  frequency: [
    { required: true, message: '請選擇執行頻率', trigger: 'change' }
  ],
  executeTime: [
    { required: true, message: '請選擇執行時間', trigger: 'change' }
  ]
}

// 數據
const searchData = ref<HistoryDataItem[]>([])
const filteredSearchData = ref<HistoryDataItem[]>([])
const availableTags = ref<TagInfo[]>([])
const scheduleList = ref<ScheduleItem[]>([])
const downloadList = ref<DownloadItem[]>([])

// 計算屬性
const hasSearchData = computed(() => searchData.value.length > 0)
</script>
