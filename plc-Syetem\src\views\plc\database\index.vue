<template>
  <div class="database-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>數據中心</h2>
      <p>統一管理系統數據，包含即時資料、歷史報表、運轉時數和自定義報表</p>
    </div>

    <!-- 功能模組 -->
    <el-card class="modules-card">
      <template #header>
        <div class="card-header">
          <span>功能模組</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="6" v-for="item in databaseModules" :key="item.key">
          <el-card
            class="module-card"
            shadow="hover"
            @click="navigateToModule(item.path)"
          >
            <div class="module-content">
              <el-icon :size="40" class="module-icon">
                <component :is="item.icon" />
              </el-icon>
              <h3>{{ item.title }}</h3>
              <p>{{ item.description }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 快速統計 -->
    <el-card class="stats-card">
      <template #header>
        <div class="card-header">
          <span>系統概覽</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ systemStats.totalTags }}</div>
            <div class="stat-label">總標籤數</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ systemStats.activeTags }}</div>
            <div class="stat-label">活躍標籤</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ systemStats.dataPoints }}</div>
            <div class="stat-label">數據點數</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ systemStats.lastUpdate }}</div>
            <div class="stat-label">最後更新</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>




<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  DataAnalysis,
  Clock,
  Document,
  Monitor
} from '@element-plus/icons-vue'

const router = useRouter()

// 數據中心模組
const databaseModules = ref([
  {
    key: "realtime",
    title: "即時資料",
    description: "查看即時數據和監控資訊",
    icon: Monitor,
    path: "/plc/database/realtime"
  },
  {
    key: "history",
    title: "歷史報表",
    description: "查詢、排程和下載歷史數據",
    icon: Clock,
    path: "/plc/database/history"
  },
  {
    key: "runtime",
    title: "運轉時數",
    description: "設備運轉時間統計分析",
    icon: DataAnalysis,
    path: "/plc/database/runtime"
  },
  {
    key: "customReport",
    title: "匯出報表",
    description: "自定義報表匯出功能",
    icon: Document,
    path: "/plc/database/custom-report"
  }
])

// 系統統計數據
const systemStats = reactive({
  totalTags: 1248,
  activeTags: 1156,
  dataPoints: '2.3M',
  lastUpdate: '2分鐘前'
})

/**
 * 導航到模組
 */
const navigateToModule = (path: string) => {
  router.push(path)
}

// 生命週期
onMounted(async () => {
  console.log('數據中心頁面已載入')
  // TODO: 載入系統統計數據
})
</script>


</script>

<style scoped>
.database-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.modules-card {
  margin-bottom: 20px;
}

.stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.module-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 180px;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-content {
  text-align: center;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.module-icon {
  color: #409eff;
  margin-bottom: 15px;
}

.module-content h3 {
  margin: 15px 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.module-content p {
  color: #606266;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
