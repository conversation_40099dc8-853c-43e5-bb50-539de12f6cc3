<template>
  <div class="database-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>即時資料管理</h2>
      <p>查詢和監控系統即時數據，支援多標籤選擇和即時更新</p>
    </div>

    <!-- 查詢條件 -->
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span>查詢條件</span>
          <div class="header-actions">
            <el-button
              type="info"
              :icon="Setting"
              @click="showSettings = true"
            >
              設定
            </el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="queryFormRef"
        :model="queryForm"
        :rules="queryRules"
        label-width="120px"
        class="query-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="選擇標籤" prop="selectedTags">
              <el-select
                v-model="queryForm.selectedTags"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="請選擇要查詢的標籤"
                :remote-method="searchTags"
                :loading="tagSearchLoading"
                style="width: 100%"
                @change="handleTagChange"
              >
                <el-option
                  v-for="tag in availableTags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                >
                  <span style="float: left">{{ tag.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ tag.description }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="更新間隔">
              <el-select v-model="queryForm.refreshInterval" style="width: 100%">
                <el-option label="不自動更新" :value="0" />
                <el-option label="5秒" :value="5000" />
                <el-option label="10秒" :value="10000" />
                <el-option label="30秒" :value="30000" />
                <el-option label="1分鐘" :value="60000" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            :disabled="queryForm.selectedTags.length === 0"
            @click="handleQuery"
          >
            {{ loading ? '查詢中...' : '開始查詢' }}
          </el-button>
          <el-button
            type="success"
            :icon="Refresh"
            :disabled="!hasData"
            @click="refreshData"
          >
            手動刷新
          </el-button>
          <el-button
            type="warning"
            :disabled="!hasData"
            @click="clearData"
          >
            清除數據
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 連接狀態 -->
    <el-card v-if="hasData" class="status-card">
      <div class="status-info">
        <div class="status-item">
          <el-icon class="status-icon" :class="connectionStatusClass">
            <Connection />
          </el-icon>
          <span>連接狀態: {{ connectionStatusText }}</span>
        </div>
        <div class="status-item">
          <el-icon class="status-icon">
            <Clock />
          </el-icon>
          <span>最後更新: {{ lastUpdateTime }}</span>
        </div>
        <div class="status-item">
          <el-icon class="status-icon">
            <DataBoard />
          </el-icon>
          <span>數據筆數: {{ realtimeData.length }}</span>
        </div>
      </div>
    </el-card>

    <!-- 數據表格 -->
    <el-card v-if="hasData" class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>即時數據</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜尋標籤名稱..."
              :prefix-icon="Search"
              clearable
              style="width: 250px; margin-right: 12px"
              @input="handleSearch"
            />
            <el-button
              type="primary"
              :icon="Download"
              @click="exportData"
            >
              匯出
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="filteredData"
        height="500"
        stripe
        border
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="Name" label="標籤名稱" width="200" fixed="left">
          <template #default="{ row }">
            <div class="tag-name">
              <el-tag
                :type="getTagStatusType(row.Status)"
                size="small"
                style="margin-right: 8px"
              >
                {{ getTagStatusText(row.Status) }}
              </el-tag>
              {{ row.Name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="Value" label="當前值" width="120" align="right">
          <template #default="{ row }">
            <span class="value-text" :class="getValueClass(row.Status)">
              {{ formatValue(row.Value) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="Unit" label="單位" width="80" />

        <el-table-column prop="Description" label="描述" min-width="200" />

        <el-table-column prop="UpdateTime" label="更新時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.UpdateTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="Quality" label="品質" width="100">
          <template #default="{ row }">
            <el-tag :type="getQualityType(row.Quality)">
              {{ getQualityText(row.Quality) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewTagDetail(row)"
            >
              詳情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 設定對話框 -->
    <el-dialog
      v-model="showSettings"
      title="即時資料設定"
      width="500px"
    >
      <el-form :model="settings" label-width="120px">
        <el-form-item label="啟用即時更新">
          <el-switch v-model="settings.enableRealtime" />
        </el-form-item>
        <el-form-item label="顯示異常數據">
          <el-switch v-model="settings.showAbnormalData" />
        </el-form-item>
        <el-form-item label="數據保留時間">
          <el-select v-model="settings.dataRetentionTime">
            <el-option label="1小時" :value="3600000" />
            <el-option label="6小時" :value="21600000" />
            <el-option label="24小時" :value="86400000" />
            <el-option label="永久保留" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import {
  Setting,
  Refresh,
  Search,
  Download,
  Connection,
  Clock,
  DataBoard
} from '@element-plus/icons-vue'
import { realtimeDataAPI, type RealtimeDataItem, type TagInfo } from '@/api/plc/database'
import { plcSignalRService } from '@/utils/plc/signalr'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const queryFormRef = ref<InstanceType<typeof ElForm>>()
const dataTableRef = ref()

// 響應式數據
const loading = ref(false)
const tagSearchLoading = ref(false)
const showSettings = ref(false)
const searchKeyword = ref('')
const lastUpdateTime = ref('')
const isConnected = ref(false)

// 查詢表單
const queryForm = reactive({
  selectedTags: [] as string[],
  refreshInterval: 10000
})

// 表單驗證規則
const queryRules = {
  selectedTags: [
    { required: true, message: '請選擇至少一個標籤', trigger: 'change' }
  ]
}

// 數據
const realtimeData = ref<RealtimeDataItem[]>([])
const availableTags = ref<TagInfo[]>([])
const filteredData = ref<RealtimeDataItem[]>([])

// 設定
const settings = reactive({
  enableRealtime: true,
  showAbnormalData: true,
  dataRetentionTime: 21600000 // 6小時
})

// 自動刷新定時器
let refreshTimer: NodeJS.Timeout | null = null

// 計算屬性
const hasData = computed(() => realtimeData.value.length > 0)
const connectionStatusText = computed(() => isConnected.value ? '已連線' : '未連線')
const connectionStatusClass = computed(() => ({
  'status-connected': isConnected.value,
  'status-disconnected': !isConnected.value
}))

/**
 * 搜索標籤
 */
const searchTags = async (query: string) => {
  if (!query) {
    availableTags.value = []
    return
  }

  try {
    tagSearchLoading.value = true
    const response = await realtimeDataAPI.searchTags({
      keyword: query,
      customerId: authStore.userInfo.customerId
    })
    availableTags.value = response.items || []
  } catch (error: any) {
    console.error('搜索標籤失敗:', error)
    ElMessage.error(error.message || '搜索標籤失敗')
  } finally {
    tagSearchLoading.value = false
  }
}

/**
 * 處理標籤變更
 */
const handleTagChange = (tags: string[]) => {
  console.log('選中的標籤:', tags)
}

/**
 * 處理查詢
 */
const handleQuery = async () => {
  if (!queryFormRef.value) return

  try {
    // 驗證表單
    await queryFormRef.value.validate()

    loading.value = true

    // 調用API獲取即時數據
    const response = await realtimeDataAPI.getRealTimeData({
      TagIdList: queryForm.selectedTags,
      customerId: authStore.userInfo.customerId
    })

    realtimeData.value = response.ValueList || []
    lastUpdateTime.value = new Date().toLocaleString('zh-TW')

    // 過濾數據
    handleSearch()

    // 設置自動刷新
    setupAutoRefresh()

    // 連接SignalR
    await connectSignalR()

    ElMessage.success('查詢成功')

  } catch (error: any) {
    console.error('查詢失敗:', error)
    ElMessage.error(error.message || '查詢失敗')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新數據
 */
const refreshData = async () => {
  if (queryForm.selectedTags.length === 0) return

  try {
    loading.value = true

    const response = await realtimeDataAPI.getRealTimeData({
      TagIdList: queryForm.selectedTags,
      customerId: authStore.userInfo.customerId
    })

    realtimeData.value = response.ValueList || []
    lastUpdateTime.value = new Date().toLocaleString('zh-TW')

    handleSearch()

  } catch (error: any) {
    console.error('刷新數據失敗:', error)
    ElMessage.error(error.message || '刷新數據失敗')
  } finally {
    loading.value = false
  }
}

/**
 * 清除數據
 */
const clearData = async () => {
  try {
    await ElMessageBox.confirm(
      '確定要清除所有數據嗎？',
      '清除數據',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    realtimeData.value = []
    filteredData.value = []
    queryForm.selectedTags = []

    // 停止自動刷新
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }

    // 斷開SignalR連接
    await disconnectSignalR()

    ElMessage.success('數據已清除')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除數據失敗:', error)
    }
  }
}

/**
 * 處理搜索
 */
const handleSearch = () => {
  if (!searchKeyword.value) {
    filteredData.value = realtimeData.value
    return
  }

  filteredData.value = realtimeData.value.filter(item =>
    item.Name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (item.Description && item.Description.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )
}

/**
 * 設置自動刷新
 */
const setupAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }

  if (queryForm.refreshInterval > 0) {
    refreshTimer = setInterval(() => {
      refreshData()
    }, queryForm.refreshInterval)
  }
}

/**
 * 連接SignalR
 */
const connectSignalR = async () => {
  try {
    // 連接DesigoCC Hub用於即時數據
    await plcSignalRService.connectHub('desigocc')

    // 監聽即時數據事件
    plcSignalRService.on('desigocc:realtime', (data: any) => {
      console.log('收到即時數據:', data)
      handleRealtimeData(data)
    })

    isConnected.value = true

  } catch (error) {
    console.error('連接SignalR失敗:', error)
    isConnected.value = false
  }
}

/**
 * 斷開SignalR連接
 */
const disconnectSignalR = async () => {
  try {
    plcSignalRService.off('desigocc:realtime')
    await plcSignalRService.disconnectHub('desigocc')
    isConnected.value = false
  } catch (error) {
    console.error('斷開SignalR連接失敗:', error)
  }
}

/**
 * 處理即時數據
 */
const handleRealtimeData = (data: any) => {
  if (!data || !data.tagValueList) return

  // 更新現有數據
  data.tagValueList.forEach((newItem: any) => {
    const existingIndex = realtimeData.value.findIndex(item => item.TagId === newItem.TagId)
    if (existingIndex !== -1) {
      realtimeData.value[existingIndex] = { ...realtimeData.value[existingIndex], ...newItem }
    }
  })

  lastUpdateTime.value = new Date().toLocaleString('zh-TW')
  handleSearch()
}

/**
 * 格式化數值
 */
const formatValue = (value: any): string => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'number') {
    return value.toFixed(2)
  }
  return String(value)
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 獲取行樣式類名
 */
const getRowClassName = ({ row }: { row: RealtimeDataItem }): string => {
  switch (row.Status) {
    case 1: return 'row-normal'
    case 2: return 'row-warning'
    case 3: return 'row-error'
    default: return ''
  }
}

/**
 * 獲取標籤狀態類型
 */
const getTagStatusType = (status: number): string => {
  switch (status) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

/**
 * 獲取標籤狀態文字
 */
const getTagStatusText = (status: number): string => {
  switch (status) {
    case 1: return '正常'
    case 2: return '警告'
    case 3: return '異常'
    default: return '未知'
  }
}

/**
 * 獲取數值樣式類名
 */
const getValueClass = (status: number): string => {
  switch (status) {
    case 1: return 'value-normal'
    case 2: return 'value-warning'
    case 3: return 'value-error'
    default: return ''
  }
}

/**
 * 獲取品質類型
 */
const getQualityType = (quality: number): string => {
  switch (quality) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

/**
 * 獲取品質文字
 */
const getQualityText = (quality: number): string => {
  switch (quality) {
    case 1: return '良好'
    case 2: return '一般'
    case 3: return '差'
    default: return '未知'
  }
}

/**
 * 查看標籤詳情
 */
const viewTagDetail = (row: RealtimeDataItem) => {
  // TODO: 實現標籤詳情對話框
  ElMessage.info('標籤詳情功能開發中...')
}

/**
 * 匯出數據
 */
const exportData = () => {
  // TODO: 實現數據匯出功能
  ElMessage.info('數據匯出功能開發中...')
}

/**
 * 保存設定
 */
const saveSettings = async () => {
  try {
    // TODO: 調用API保存設定
    ElMessage.success('設定保存成功')
    showSettings.value = false
  } catch (error: any) {
    console.error('保存設定失敗:', error)
    ElMessage.error(error.message || '保存設定失敗')
  }
}

// 生命週期
onMounted(async () => {
  // 初始化設定
  console.log('即時資料管理頁面已載入')
})

onUnmounted(() => {
  // 清理定時器
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }

  // 斷開SignalR連接
  disconnectSignalR()
})
</script>

<style scoped>
.database-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.query-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.query-form {
  margin-top: 20px;
}

.status-card {
  margin-bottom: 20px;
}

.status-info {
  display: flex;
  gap: 30px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.status-connected {
  color: #67c23a;
}

.status-icon.status-disconnected {
  color: #f56c6c;
}

.data-table-card {
  margin-bottom: 20px;
}

.tag-name {
  display: flex;
  align-items: center;
}

.value-text {
  font-weight: bold;
}

.value-text.value-normal {
  color: #67c23a;
}

.value-text.value-warning {
  color: #e6a23c;
}

.value-text.value-error {
  color: #f56c6c;
}

:deep(.el-table .row-normal) {
  background-color: #f0f9ff;
}

:deep(.el-table .row-warning) {
  background-color: #fdf6ec;
}

:deep(.el-table .row-error) {
  background-color: #fef0f0;
}

:deep(.el-table .el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-select .el-input) {
  height: 40px;
}

:deep(.el-button) {
  height: 40px;
}
</style>
