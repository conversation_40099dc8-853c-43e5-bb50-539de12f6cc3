<template>
  <div class="runtime-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>運轉時數</h2>
      <p>設備運轉時間統計分析，支援常用搜尋和數據匯出</p>
    </div>

    <!-- 常用搜尋 -->
    <el-card v-if="commonSearchList.length > 0" class="common-search-card">
      <template #header>
        <div class="card-header">
          <span>常用搜尋</span>
        </div>
      </template>
      <div class="common-search-list">
        <el-tag
          v-for="item in commonSearchList"
          :key="item.id"
          class="common-search-tag"
          closable
          @click="useCommonSearch(item)"
          @close="deleteCommonSearch(item)"
        >
          {{ item.name }}
        </el-tag>
      </div>
    </el-card>

    <!-- 查詢條件 -->
    <el-card class="query-card">
      <template #header>
        <div class="card-header">
          <span>查詢條件</span>
        </div>
      </template>

      <el-form
        ref="queryFormRef"
        :model="queryForm"
        :rules="queryRules"
        label-width="120px"
        class="query-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="選擇標籤" prop="selectedTags">
              <el-select
                v-model="queryForm.selectedTags"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="請選擇要查詢的標籤"
                :remote-method="searchTags"
                :loading="tagSearchLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in availableTags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                >
                  <span style="float: left">{{ tag.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ tag.description }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="時間範圍" prop="dateRange">
              <el-date-picker
                v-model="queryForm.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="開始時間"
                end-placeholder="結束時間"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="統計方式">
              <el-select v-model="queryForm.statisticType" style="width: 100%">
                <el-option label="總運轉時數" value="total" />
                <el-option label="日平均運轉時數" value="daily_avg" />
                <el-option label="週平均運轉時數" value="weekly_avg" />
                <el-option label="月平均運轉時數" value="monthly_avg" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="運轉閾值">
              <el-input-number
                v-model="queryForm.threshold"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
                placeholder="設定運轉判斷閾值"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            :disabled="!queryForm.selectedTags.length || !queryForm.dateRange"
            @click="handleQuery"
          >
            {{ loading ? '查詢中...' : '開始查詢' }}
          </el-button>
          <el-button
            type="success"
            :disabled="!hasData"
            @click="exportData"
          >
            匯出數據
          </el-button>
          <el-button
            type="info"
            :disabled="!queryForm.selectedTags.length"
            @click="showAddSearchModal = true"
          >
            加入常用搜尋
          </el-button>
          <el-button
            type="warning"
            :disabled="!hasData"
            @click="clearData"
          >
            清除數據
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 統計摘要 -->
    <el-card v-if="hasData" class="summary-card">
      <template #header>
        <div class="card-header">
          <span>統計摘要</span>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-value">{{ summaryData.totalDevices }}</div>
            <div class="summary-label">設備總數</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-value">{{ summaryData.totalRuntime }}</div>
            <div class="summary-label">總運轉時數</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-value">{{ summaryData.avgRuntime }}</div>
            <div class="summary-label">平均運轉時數</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-value">{{ summaryData.efficiency }}%</div>
            <div class="summary-label">運轉效率</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 數據表格 -->
    <el-card v-if="hasData" class="data-table-card">
      <template #header>
        <div class="card-header">
          <span>運轉時數統計</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜尋設備名稱..."
              :prefix-icon="Search"
              clearable
              style="width: 250px"
              @input="handleSearch"
            />
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="filteredData"
        height="500"
        stripe
        border
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="TagName" label="設備名稱" width="200" fixed="left" />
        <el-table-column prop="TotalRuntime" label="總運轉時數" width="150" align="right">
          <template #default="{ row }">
            {{ formatRuntime(row.TotalRuntime) }}
          </template>
        </el-table-column>
        <el-table-column prop="AvgRuntime" label="平均運轉時數" width="150" align="right">
          <template #default="{ row }">
            {{ formatRuntime(row.AvgRuntime) }}
          </template>
        </el-table-column>
        <el-table-column prop="Efficiency" label="運轉效率" width="120" align="right">
          <template #default="{ row }">
            <span :class="getEfficiencyClass(row.Efficiency)">
              {{ row.Efficiency.toFixed(2) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="StartTime" label="開始時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.StartTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="EndTime" label="結束時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.EndTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="Status" label="狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.Status)">
              {{ getStatusText(row.Status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="Description" label="描述" min-width="200" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(row)"
            >
              詳情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增常用搜尋對話框 -->
    <el-dialog
      v-model="showAddSearchModal"
      title="新增常用搜尋"
      width="400px"
    >
      <el-form :model="addSearchForm" label-width="80px">
        <el-form-item label="名稱" required>
          <el-input
            v-model="addSearchForm.name"
            placeholder="請輸入搜尋名稱"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddSearchModal = false">取消</el-button>
        <el-button type="primary" @click="addCommonSearch">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { runtimeDataAPI, type RuntimeDataItem, type TagInfo, type CommonSearchItem } from '@/api/plc/database'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const queryFormRef = ref<InstanceType<typeof ElForm>>()
const dataTableRef = ref()

// 響應式數據
const loading = ref(false)
const tagSearchLoading = ref(false)
const searchKeyword = ref('')
const showAddSearchModal = ref(false)

// 查詢表單
const queryForm = reactive({
  selectedTags: [] as string[],
  dateRange: [] as string[],
  statisticType: 'total',
  threshold: 50
})

// 新增搜尋表單
const addSearchForm = reactive({
  name: ''
})

// 表單驗證規則
const queryRules = {
  selectedTags: [
    { required: true, message: '請選擇至少一個標籤', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '請選擇時間範圍', trigger: 'change' }
  ]
}

// 數據
const runtimeData = ref<RuntimeDataItem[]>([])
const filteredData = ref<RuntimeDataItem[]>([])
const availableTags = ref<TagInfo[]>([])
const commonSearchList = ref<CommonSearchItem[]>([])

// 統計摘要數據
const summaryData = reactive({
  totalDevices: 0,
  totalRuntime: '0小時',
  avgRuntime: '0小時',
  efficiency: 0
})

// 計算屬性
const hasData = computed(() => runtimeData.value.length > 0)
</script>
