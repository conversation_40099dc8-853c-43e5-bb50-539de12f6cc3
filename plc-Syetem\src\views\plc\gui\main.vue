<template>
  <div class="gui-main-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>圖形監控 - {{ $route.params.id }}</span>
        </div>
      </template>
      
      <div class="content">
        <el-empty description="圖形監控功能開發中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'

const route = useRoute()
// 圖形監控頁面，接收 id 參數
</script>

<style scoped>
.gui-main-container {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
