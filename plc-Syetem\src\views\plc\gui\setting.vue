<template>
  <div class="gui-setting-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>GUI設定</span>
        </div>
      </template>

      <div class="content">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>可用的GUI監控頁面</span>
              </template>
              <el-table :data="guiList" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="名稱" />
                <el-table-column prop="category" label="類型" />
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button
                      type="primary"
                      size="small"
                      @click="openGUI(scope.row.id)"
                    >
                      開啟
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>

          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <span>GUI設定選項</span>
              </template>
              <el-form label-width="120px">
                <el-form-item label="自動刷新">
                  <el-switch v-model="autoRefresh" />
                </el-form-item>
                <el-form-item label="刷新間隔">
                  <el-input-number
                    v-model="refreshInterval"
                    :min="1"
                    :max="60"
                    :disabled="!autoRefresh"
                  />
                  <span style="margin-left: 8px">秒</span>
                </el-form-item>
                <el-form-item label="全螢幕模式">
                  <el-switch v-model="fullScreen" />
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// GUI設定選項
const autoRefresh = ref(false)
const refreshInterval = ref(5)
const fullScreen = ref(false)

// 示例GUI列表
const guiList = ref([
  { id: 1, name: '消防系統監控', category: '圖形監控' },
  { id: 2, name: 'CCTV監控系統', category: '視頻監控' },
  { id: 3, name: '電力系統監控', category: '圖形監控' },
  { id: 4, name: '空調系統監控', category: '圖形監控' },
  { id: 5, name: '照明系統監控', category: '圖形監控' }
])

// 開啟GUI監控頁面
const openGUI = (id: number) => {
  router.push({ name: 'PLCGUIMain', params: { id: id.toString() } })
}
</script>

<style scoped>
.gui-setting-container {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
