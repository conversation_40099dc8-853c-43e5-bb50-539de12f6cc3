<template>
  <div class="group-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <div class="header-left">
        <h1>群組管理</h1>
        <p>管理通知群組、成員設定和群組權限</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增群組
        </el-button>
      </div>
    </div>

    <!-- 搜尋區域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="群組名稱">
          <el-input
            v-model="searchForm.name"
            placeholder="請輸入群組名稱"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="通知方式">
          <el-select
            v-model="searchForm.method"
            placeholder="請選擇通知方式"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="SMS" value="SMS" />
            <el-option label="LINE" value="LINE" />
            <el-option label="Email" value="Email" />
          </el-select>
        </el-form-item>
        <el-form-item label="狀態">
          <el-select
            v-model="searchForm.status"
            placeholder="請選擇狀態"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="啟用" value="active" />
            <el-option label="停用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜尋
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 群組列表 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>群組列表</span>
          <div class="header-actions">
            <el-button size="small" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              重新整理
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="群組名稱" min-width="150">
          <template #default="{ row }">
            <div class="group-name">
              <el-icon class="group-icon"><UserFilled /></el-icon>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="method" label="通知方式" width="120">
          <template #default="{ row }">
            <el-tag :type="getMethodTagType(row.method)">
              {{ row.method }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="memberCount" label="成員數量" width="100" align="center" />
        <el-table-column prop="startTime" label="開始時間" width="180" />
        <el-table-column prop="endTime" label="結束時間" width="180" />
        <el-table-column prop="status" label="狀態" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="'active'"
              :inactive-value="'inactive'"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              編輯
            </el-button>
            <el-button size="small" type="info" @click="handleViewMembers(row)">
              <el-icon><View /></el-icon>
              成員
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              <el-icon><Delete /></el-icon>
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯群組對話框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="群組名稱" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="請輸入群組名稱"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="通知方式" prop="method">
          <el-select v-model="formData.method" placeholder="請選擇通知方式" style="width: 100%">
            <el-option label="SMS" value="SMS" />
            <el-option label="LINE" value="LINE" />
            <el-option label="Email" value="Email" />
          </el-select>
        </el-form-item>
        <el-form-item label="開始時間" prop="startTime">
          <el-date-picker
            v-model="formData.startTime"
            type="datetime"
            placeholder="請選擇開始時間"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="結束時間" prop="endTime">
          <el-date-picker
            v-model="formData.endTime"
            type="datetime"
            placeholder="請選擇結束時間"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="持續時間" prop="duration">
          <el-input-number
            v-model="formData.duration"
            :min="1"
            :max="24"
            placeholder="小時"
            style="width: 100%"
          />
          <span style="margin-left: 8px; color: #909399;">小時</span>
        </el-form-item>
        <el-form-item label="群組成員" prop="members">
          <div class="members-container">
            <div class="members-header">
              <el-button size="small" type="primary" @click="showAddMemberDialog">
                <el-icon><Plus /></el-icon>
                新增成員
              </el-button>
            </div>
            <div class="members-list">
              <div
                v-for="(member, index) in formData.members"
                :key="index"
                class="member-item"
              >
                <div class="member-info">
                  <span class="member-name">{{ member.name }}</span>
                  <span class="member-contact">{{ member.contact }}</span>
                </div>
                <el-button
                  size="small"
                  type="danger"
                  text
                  @click="removeMember(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">確定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增成員對話框 -->
    <el-dialog
      v-model="memberDialogVisible"
      title="新增成員"
      width="400px"
    >
      <el-form
        ref="memberFormRef"
        :model="memberForm"
        :rules="memberRules"
        label-width="80px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="memberForm.name" placeholder="請輸入姓名" />
        </el-form-item>
        <el-form-item label="聯絡方式" prop="contact">
          <el-input v-model="memberForm.contact" placeholder="請輸入電話或Email" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="memberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddMember">確定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Edit,
  Delete,
  View,
  UserFilled
} from '@element-plus/icons-vue'

// 響應式數據
const loading = ref(false)
const dialogVisible = ref(false)
const memberDialogVisible = ref(false)
const dialogTitle = ref('新增群組')
const selectedRows = ref([])

// 搜尋表單
const searchForm = reactive({
  name: '',
  method: '',
  status: ''
})

// 分頁
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格數據
const tableData = ref([])

// 表單引用
const formRef = ref<FormInstance>()
const memberFormRef = ref<FormInstance>()

// 表單數據
const formData = reactive({
  id: '',
  name: '',
  method: '',
  startTime: '',
  endTime: '',
  duration: 1,
  members: []
})

// 成員表單
const memberForm = reactive({
  name: '',
  contact: ''
})

// 表單驗證規則
const formRules: FormRules = {
  name: [
    { required: true, message: '請輸入群組名稱', trigger: 'blur' }
  ],
  method: [
    { required: true, message: '請選擇通知方式', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '請選擇開始時間', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '請選擇結束時間', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '請輸入持續時間', trigger: 'blur' }
  ]
}

const memberRules: FormRules = {
  name: [
    { required: true, message: '請輸入姓名', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '請輸入聯絡方式', trigger: 'blur' }
  ]
}

// 方法
const getMethodTagType = (method: string) => {
  const typeMap = {
    'SMS': 'warning',
    'LINE': 'success',
    'Email': 'info'
  }
  return typeMap[method] || 'info'
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    method: '',
    status: ''
  })
  handleSearch()
}

const refreshData = () => {
  loadData()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleStatusChange = async (row: any) => {
  try {
    // 調用 API 更新狀態
    ElMessage.success('狀態更新成功')
  } catch (error) {
    ElMessage.error('狀態更新失敗')
    // 恢復原狀態
    row.status = row.status === 'active' ? 'inactive' : 'active'
  }
}

const showAddDialog = () => {
  dialogTitle.value = '新增群組'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  dialogTitle.value = '編輯群組'
  Object.assign(formData, { ...row })
  dialogVisible.value = true
}

const handleViewMembers = (row: any) => {
  ElMessage.info('查看成員功能開發中...')
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除群組 "${row.name}" 嗎？`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 調用 API 刪除
    ElMessage.success('刪除成功')
    loadData()
  } catch (error) {
    // 用戶取消刪除
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    method: '',
    startTime: '',
    endTime: '',
    duration: 1,
    members: []
  })
  formRef.value?.clearValidate()
}

const showAddMemberDialog = () => {
  Object.assign(memberForm, {
    name: '',
    contact: ''
  })
  memberDialogVisible.value = true
}

const handleAddMember = async () => {
  try {
    await memberFormRef.value?.validate()
    formData.members.push({ ...memberForm })
    memberDialogVisible.value = false
    ElMessage.success('成員新增成功')
  } catch (error) {
    // 驗證失敗
  }
}

const removeMember = (index: number) => {
  formData.members.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 調用 API 保存
    ElMessage.success(dialogTitle.value === '新增群組' ? '新增成功' : '更新成功')
    dialogVisible.value = false
    loadData()
  } catch (error) {
    // 驗證失敗
  }
}

const loadData = async () => {
  loading.value = true
  try {
    // 調用真實API載入通知群組列表
    const response = await plcDataService.get('/Message/GetMessageGroupDetailList', {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    if (response && response.data) {
      // 轉換後端數據格式為前端格式
      tableData.value = response.data.map((group: any) => ({
        id: group.GroupId || group.Id,
        name: group.GroupName || group.Name,
        method: group.NotifyMethod || group.Method,
        memberCount: group.MemberCount || 0,
        startTime: group.StartTime,
        endTime: group.EndTime,
        status: group.IsActive ? 'active' : 'inactive'
      }))

      pagination.total = response.total || tableData.value.length
      ElMessage.success(`成功載入 ${tableData.value.length} 個通知群組`)
    } else {
      tableData.value = []
      pagination.total = 0
      ElMessage.warning('未找到通知群組數據')
    }
  } catch (error) {
    ElMessage.error('載入數據失敗')
  } finally {
    loading.value = false
  }
}

// 生命週期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.group-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
}

.table-card {
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.group-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-icon {
  color: #667eea;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.members-container {
  width: 100%;
}

.members-header {
  margin-bottom: 12px;
}

.members-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.member-item:last-child {
  border-bottom: none;
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-name {
  font-weight: 500;
  color: #374151;
}

.member-contact {
  font-size: 12px;
  color: #6b7280;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    align-self: flex-start;
  }
}
</style>
