<template>
  <div class="notify-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h1>通知系統</h1>
      <p>管理系統通知群組、訊息發送和通知設定</p>
    </div>

    <!-- 系統統計 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon group-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.groupCount }}</div>
            <div class="stat-label">通知群組</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon message-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.messageCount }}</div>
            <div class="stat-label">今日訊息</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon member-icon">
            <el-icon><Avatar /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.memberCount }}</div>
            <div class="stat-label">群組成員</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon service-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.serviceCount }}</div>
            <div class="stat-label">通知服務</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 模組導航 -->
    <div class="module-grid">
      <el-card
        class="module-card"
        shadow="hover"
        @click="navigateToModule('group')"
      >
        <div class="module-content">
          <div class="module-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="module-info">
            <h3>群組管理</h3>
            <p>管理通知群組、成員設定和群組權限</p>
          </div>
          <div class="module-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card
        class="module-card"
        shadow="hover"
        @click="navigateToModule('message')"
      >
        <div class="module-content">
          <div class="module-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="module-info">
            <h3>訊息管理</h3>
            <p>發送通知訊息、查看歷史記錄和訊息統計</p>
          </div>
          <div class="module-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card
        class="module-card"
        shadow="hover"
        @click="navigateToModule('setting')"
      >
        <div class="module-content">
          <div class="module-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <div class="module-info">
            <h3>通知設定</h3>
            <p>設定 LINE、Email、SMS 等通知服務參數</p>
          </div>
          <div class="module-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快速操作</span>
        </template>
        <div class="action-buttons">
          <el-button type="primary" @click="quickSendMessage">
            <el-icon><Promotion /></el-icon>
            快速發送
          </el-button>
          <el-button type="success" @click="quickAddGroup">
            <el-icon><Plus /></el-icon>
            新增群組
          </el-button>
          <el-button @click="viewMessageHistory">
            <el-icon><View /></el-icon>
            查看歷史
          </el-button>
          <el-button @click="testNotification">
            <el-icon><Bell /></el-icon>
            測試通知
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 系統狀態 -->
    <div class="system-status">
      <el-card>
        <template #header>
          <span>服務狀態</span>
        </template>
        <div class="status-grid">
          <div class="status-item">
            <span class="status-label">LINE 服務</span>
            <el-tag :type="systemStatus.lineService ? 'success' : 'danger'">
              {{ systemStatus.lineService ? '正常' : '異常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">Email 服務</span>
            <el-tag :type="systemStatus.emailService ? 'success' : 'danger'">
              {{ systemStatus.emailService ? '正常' : '異常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">SMS 服務</span>
            <el-tag :type="systemStatus.smsService ? 'success' : 'danger'">
              {{ systemStatus.smsService ? '正常' : '異常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">最後更新</span>
            <span class="status-time">{{ systemStatus.lastUpdate }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  UserFilled,
  ChatDotRound,
  Avatar,
  Setting,
  ArrowRight,
  Plus,
  Promotion,
  View,
  Bell
} from '@element-plus/icons-vue'

const router = useRouter()

// 系統統計數據
const systemStats = reactive({
  groupCount: 0,
  messageCount: 0,
  memberCount: 0,
  serviceCount: 0
})

// 系統狀態
const systemStatus = reactive({
  lineService: true,
  emailService: true,
  smsService: false,
  lastUpdate: '2024-01-20 10:30:00'
})

// 導航到子模組
const navigateToModule = (module: string) => {
  router.push(`/plc-notify/${module}`)
}

// 快速操作
const quickSendMessage = () => {
  router.push('/plc-notify/message?action=send')
}

const quickAddGroup = () => {
  router.push('/plc-notify/group?action=add')
}

const viewMessageHistory = () => {
  router.push('/plc-notify/message?tab=history')
}

const testNotification = () => {
  ElMessage.info('通知測試功能開發中...')
}

// 載入系統統計
const loadSystemStats = async () => {
  try {
    // 模擬載入統計數據
    systemStats.groupCount = 8
    systemStats.messageCount = 45
    systemStats.memberCount = 32
    systemStats.serviceCount = 3
  } catch (error) {
    console.error('載入系統統計失敗:', error)
  }
}

// 生命週期
onMounted(async () => {
  await loadSystemStats()
})
</script>

<style scoped>
.notify-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.group-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.message-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.member-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.service-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.module-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.module-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.module-info {
  flex: 1;
}

.module-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.module-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.module-arrow {
  color: #9ca3af;
  font-size: 16px;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.system-status .status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  font-weight: 500;
  color: #374151;
}

.status-time {
  color: #6b7280;
  font-size: 14px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .module-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    justify-content: center;
  }

  .module-stats {
    justify-content: center;
  }
}
</style>
