<template>
  <div class="message-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <div class="header-left">
        <h1>訊息管理</h1>
        <p>發送通知訊息、查看歷史記錄和訊息統計</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showSendDialog">
          <el-icon><Promotion /></el-icon>
          發送訊息
        </el-button>
      </div>
    </div>

    <!-- 標籤頁 -->
    <el-tabs v-model="activeTab" class="message-tabs">
      <!-- 發送訊息 -->
      <el-tab-pane label="發送訊息" name="send">
        <el-card class="send-card" shadow="never">
          <el-form
            ref="sendFormRef"
            :model="sendForm"
            :rules="sendRules"
            label-width="100px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="通知群組" prop="groupId">
                  <el-select
                    v-model="sendForm.groupId"
                    placeholder="請選擇通知群組"
                    style="width: 100%"
                    @change="handleGroupChange"
                  >
                    <el-option
                      v-for="group in groupList"
                      :key="group.id"
                      :label="group.name"
                      :value="group.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="通知方式" prop="method">
                  <el-select
                    v-model="sendForm.method"
                    placeholder="請選擇通知方式"
                    style="width: 100%"
                  >
                    <el-option label="SMS" value="SMS" />
                    <el-option label="LINE" value="LINE" />
                    <el-option label="Email" value="Email" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="訊息標題" prop="title">
              <el-input
                v-model="sendForm.title"
                placeholder="請輸入訊息標題"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="訊息內容" prop="content">
              <el-input
                v-model="sendForm.content"
                type="textarea"
                :rows="6"
                placeholder="請輸入訊息內容"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="發送時間" prop="sendTime">
                  <el-radio-group v-model="sendForm.sendType">
                    <el-radio label="now">立即發送</el-radio>
                    <el-radio label="scheduled">定時發送</el-radio>
                  </el-radio-group>
                  <el-date-picker
                    v-if="sendForm.sendType === 'scheduled'"
                    v-model="sendForm.sendTime"
                    type="datetime"
                    placeholder="請選擇發送時間"
                    style="width: 100%; margin-top: 8px"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="優先級" prop="priority">
                  <el-select v-model="sendForm.priority" placeholder="請選擇優先級" style="width: 100%">
                    <el-option label="低" value="low" />
                    <el-option label="中" value="medium" />
                    <el-option label="高" value="high" />
                    <el-option label="緊急" value="urgent" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="primary" @click="handleSendMessage" :loading="sending">
                <el-icon><Promotion /></el-icon>
                {{ sendForm.sendType === 'now' ? '立即發送' : '定時發送' }}
              </el-button>
              <el-button @click="resetSendForm">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 訊息歷史 -->
      <el-tab-pane label="訊息歷史" name="history">
        <el-card class="history-card" shadow="never">
          <!-- 搜尋區域 -->
          <div class="search-section">
            <el-form :model="historySearch" inline>
              <el-form-item label="群組">
                <el-select
                  v-model="historySearch.groupId"
                  placeholder="請選擇群組"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="group in groupList"
                    :key="group.id"
                    :label="group.name"
                    :value="group.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="方式">
                <el-select
                  v-model="historySearch.method"
                  placeholder="請選擇方式"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="全部" value="" />
                  <el-option label="SMS" value="SMS" />
                  <el-option label="LINE" value="LINE" />
                  <el-option label="Email" value="Email" />
                </el-select>
              </el-form-item>
              <el-form-item label="狀態">
                <el-select
                  v-model="historySearch.status"
                  placeholder="請選擇狀態"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="全部" value="" />
                  <el-option label="成功" value="success" />
                  <el-option label="失敗" value="failed" />
                  <el-option label="等待中" value="pending" />
                </el-select>
              </el-form-item>
              <el-form-item label="時間">
                <el-date-picker
                  v-model="historySearch.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="開始日期"
                  end-placeholder="結束日期"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleHistorySearch">
                  <el-icon><Search /></el-icon>
                  搜尋
                </el-button>
                <el-button @click="handleHistoryReset">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 歷史記錄表格 -->
          <el-table
            v-loading="historyLoading"
            :data="historyData"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="title" label="標題" min-width="150" />
            <el-table-column prop="groupName" label="群組" width="120" />
            <el-table-column prop="method" label="方式" width="80">
              <template #default="{ row }">
                <el-tag :type="getMethodTagType(row.method)">
                  {{ row.method }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="recipientCount" label="接收人數" width="100" align="center" />
            <el-table-column prop="sendTime" label="發送時間" width="180" />
            <el-table-column prop="status" label="狀態" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="優先級" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="getPriorityTagType(row.priority)" size="small">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="handleViewDetail(row)">
                  <el-icon><View /></el-icon>
                  詳情
                </el-button>
                <el-button
                  v-if="row.status === 'failed'"
                  size="small"
                  type="warning"
                  @click="handleResend(row)"
                >
                  <el-icon><Refresh /></el-icon>
                  重發
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分頁 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="historyPagination.currentPage"
              v-model:page-size="historyPagination.pageSize"
              :total="historyPagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleHistorySizeChange"
              @current-change="handleHistoryCurrentChange"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 統計報表 -->
      <el-tab-pane label="統計報表" name="statistics">
        <el-card class="statistics-card" shadow="never">
          <div class="stats-overview">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">{{ statistics.totalSent }}</div>
                  <div class="stat-label">總發送數</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number success">{{ statistics.successCount }}</div>
                  <div class="stat-label">成功數</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number danger">{{ statistics.failedCount }}</div>
                  <div class="stat-label">失敗數</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number warning">{{ statistics.pendingCount }}</div>
                  <div class="stat-label">等待中</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="chart-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="chart-container">
                  <h3>發送方式統計</h3>
                  <div class="chart-placeholder">
                    圖表區域 - 發送方式分布
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="chart-container">
                  <h3>發送趨勢</h3>
                  <div class="chart-placeholder">
                    圖表區域 - 時間趨勢
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 訊息詳情對話框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="訊息詳情"
      width="600px"
    >
      <div v-if="selectedMessage" class="message-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="標題">{{ selectedMessage.title }}</el-descriptions-item>
          <el-descriptions-item label="群組">{{ selectedMessage.groupName }}</el-descriptions-item>
          <el-descriptions-item label="通知方式">
            <el-tag :type="getMethodTagType(selectedMessage.method)">
              {{ selectedMessage.method }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="優先級">
            <el-tag :type="getPriorityTagType(selectedMessage.priority)">
              {{ getPriorityText(selectedMessage.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="發送時間">{{ selectedMessage.sendTime }}</el-descriptions-item>
          <el-descriptions-item label="狀態">
            <el-tag :type="getStatusTagType(selectedMessage.status)">
              {{ getStatusText(selectedMessage.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="接收人數" :span="2">{{ selectedMessage.recipientCount }}</el-descriptions-item>
          <el-descriptions-item label="內容" :span="2">
            <div class="message-content">{{ selectedMessage.content }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">關閉</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Promotion,
  Search,
  Refresh,
  View
} from '@element-plus/icons-vue'

// 響應式數據
const activeTab = ref('send')
const sending = ref(false)
const historyLoading = ref(false)
const detailDialogVisible = ref(false)
const selectedMessage = ref(null)

// 表單引用
const sendFormRef = ref<FormInstance>()

// 發送表單
const sendForm = reactive({
  groupId: '',
  method: '',
  title: '',
  content: '',
  sendType: 'now',
  sendTime: '',
  priority: 'medium'
})

// 群組列表
const groupList = ref([])

// 歷史搜尋
const historySearch = reactive({
  groupId: '',
  method: '',
  status: '',
  dateRange: []
})

// 歷史分頁
const historyPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 歷史數據
const historyData = ref([])

// 統計數據
const statistics = reactive({
  totalSent: 0,
  successCount: 0,
  failedCount: 0,
  pendingCount: 0
})

// 表單驗證規則
const sendRules: FormRules = {
  groupId: [
    { required: true, message: '請選擇通知群組', trigger: 'change' }
  ],
  method: [
    { required: true, message: '請選擇通知方式', trigger: 'change' }
  ],
  title: [
    { required: true, message: '請輸入訊息標題', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '請輸入訊息內容', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '請選擇優先級', trigger: 'change' }
  ]
}

// 方法
const getMethodTagType = (method: string) => {
  const typeMap = {
    'SMS': 'warning',
    'LINE': 'success',
    'Email': 'info'
  }
  return typeMap[method] || 'info'
}

const getStatusTagType = (status: string) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'pending': 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap = {
    'success': '成功',
    'failed': '失敗',
    'pending': '等待中'
  }
  return textMap[status] || status
}

const getPriorityTagType = (priority: string) => {
  const typeMap = {
    'low': 'info',
    'medium': '',
    'high': 'warning',
    'urgent': 'danger'
  }
  return typeMap[priority] || ''
}

const getPriorityText = (priority: string) => {
  const textMap = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '緊急'
  }
  return textMap[priority] || priority
}

const handleGroupChange = (groupId: string) => {
  // 根據群組設定預設通知方式
  const group = groupList.value.find(g => g.id === groupId)
  if (group) {
    sendForm.method = group.method
  }
}

const handleSendMessage = async () => {
  try {
    await sendFormRef.value?.validate()
    
    sending.value = true
    // 模擬發送
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('訊息發送成功')
    resetSendForm()
  } catch (error) {
    ElMessage.error('訊息發送失敗')
  } finally {
    sending.value = false
  }
}

const resetSendForm = () => {
  Object.assign(sendForm, {
    groupId: '',
    method: '',
    title: '',
    content: '',
    sendType: 'now',
    sendTime: '',
    priority: 'medium'
  })
  sendFormRef.value?.clearValidate()
}

const handleHistorySearch = () => {
  historyPagination.currentPage = 1
  loadHistoryData()
}

const handleHistoryReset = () => {
  Object.assign(historySearch, {
    groupId: '',
    method: '',
    status: '',
    dateRange: []
  })
  handleHistorySearch()
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.pageSize = size
  loadHistoryData()
}

const handleHistoryCurrentChange = (page: number) => {
  historyPagination.currentPage = page
  loadHistoryData()
}

const handleViewDetail = (row: any) => {
  selectedMessage.value = row
  detailDialogVisible.value = true
}

const handleResend = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      '確定要重新發送此訊息嗎？',
      '確認重發',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('重發成功')
    loadHistoryData()
  } catch (error) {
    // 用戶取消
  }
}

const showSendDialog = () => {
  activeTab.value = 'send'
}

const loadGroupList = async () => {
  try {
    // 模擬載入群組列表
    groupList.value = [
      { id: '1', name: '緊急通知群組', method: 'SMS' },
      { id: '2', name: 'LINE 通知群組', method: 'LINE' },
      { id: '3', name: 'Email 通知群組', method: 'Email' }
    ]
  } catch (error) {
    ElMessage.error('載入群組列表失敗')
  }
}

const loadHistoryData = async () => {
  historyLoading.value = true
  try {
    // 模擬載入歷史數據
    await new Promise(resolve => setTimeout(resolve, 500))
    
    historyData.value = [
      {
        id: '1',
        title: '系統維護通知',
        groupName: '緊急通知群組',
        method: 'SMS',
        recipientCount: 5,
        sendTime: '2024-01-20 10:30:00',
        status: 'success',
        priority: 'high',
        content: '系統將於今晚 22:00 進行維護，預計維護時間 2 小時。'
      },
      {
        id: '2',
        title: '設備異常警報',
        groupName: 'LINE 通知群組',
        method: 'LINE',
        recipientCount: 8,
        sendTime: '2024-01-20 09:15:00',
        status: 'success',
        priority: 'urgent',
        content: '設備 A01 溫度異常，請立即檢查。'
      },
      {
        id: '3',
        title: '月報發送',
        groupName: 'Email 通知群組',
        method: 'Email',
        recipientCount: 12,
        sendTime: '2024-01-20 08:00:00',
        status: 'failed',
        priority: 'low',
        content: '本月系統運行報告已生成，請查收。'
      }
    ]
    
    historyPagination.total = 3
  } catch (error) {
    ElMessage.error('載入歷史數據失敗')
  } finally {
    historyLoading.value = false
  }
}

const loadStatistics = async () => {
  try {
    // 模擬載入統計數據
    Object.assign(statistics, {
      totalSent: 156,
      successCount: 142,
      failedCount: 8,
      pendingCount: 6
    })
  } catch (error) {
    ElMessage.error('載入統計數據失敗')
  }
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadGroupList(),
    loadHistoryData(),
    loadStatistics()
  ])
})
</script>

<style scoped>
.message-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.message-tabs {
  margin-top: 20px;
}

.send-card,
.history-card,
.statistics-card {
  border: 1px solid #e5e7eb;
}

.search-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 6px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.stats-overview {
  margin-bottom: 30px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.stat-number.success {
  color: #10b981;
}

.stat-number.danger {
  color: #ef4444;
}

.stat-number.warning {
  color: #f59e0b;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.chart-section {
  margin-top: 30px;
}

.chart-container {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #fff;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border-radius: 4px;
  color: #6b7280;
  font-size: 14px;
}

.message-detail {
  padding: 16px 0;
}

.message-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 4px;
  line-height: 1.6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    align-self: flex-start;
  }

  .stats-overview .el-col {
    margin-bottom: 16px;
  }

  .chart-section .el-col {
    margin-bottom: 20px;
  }
}
</style>
