<template>
  <div class="setting-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <div class="header-left">
        <h1>通知設定</h1>
        <p>設定 LINE、Email、SMS 等通知服務參數</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleSaveAll">
          <el-icon><Check /></el-icon>
          儲存所有設定
        </el-button>
      </div>
    </div>

    <!-- 標籤頁 -->
    <el-tabs v-model="activeTab" class="setting-tabs">
      <!-- LINE 設定 -->
      <el-tab-pane label="LINE 設定" name="line">
        <el-card class="setting-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>LINE Notify 服務設定</span>
              <el-switch
                v-model="lineSettings.enabled"
                active-text="啟用"
                inactive-text="停用"
                @change="handleLineToggle"
              />
            </div>
          </template>

          <el-form
            ref="lineFormRef"
            :model="lineSettings"
            :rules="lineRules"
            label-width="120px"
            :disabled="!lineSettings.enabled"
          >
            <el-form-item label="Access Token" prop="accessToken">
              <el-input
                v-model="lineSettings.accessToken"
                placeholder="請輸入 LINE Notify Access Token"
                type="password"
                show-password
                maxlength="100"
              />
              <div class="form-tip">
                請至 LINE Notify 官網申請 Access Token
              </div>
            </el-form-item>

            <el-form-item label="預設群組" prop="defaultGroup">
              <el-input
                v-model="lineSettings.defaultGroup"
                placeholder="請輸入預設群組名稱"
                maxlength="50"
              />
            </el-form-item>

            <el-form-item label="連線逾時" prop="timeout">
              <el-input-number
                v-model="lineSettings.timeout"
                :min="5"
                :max="60"
                :step="5"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #909399;">秒</span>
            </el-form-item>

            <el-form-item label="重試次數" prop="retryCount">
              <el-input-number
                v-model="lineSettings.retryCount"
                :min="0"
                :max="5"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleTestLine" :loading="testing.line">
                <el-icon><Connection /></el-icon>
                測試連線
              </el-button>
              <el-button @click="handleSaveLine">
                <el-icon><Check /></el-icon>
                儲存設定
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- Email 設定 -->
      <el-tab-pane label="Email 設定" name="email">
        <el-card class="setting-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>SMTP 郵件服務設定</span>
              <el-switch
                v-model="emailSettings.enabled"
                active-text="啟用"
                inactive-text="停用"
                @change="handleEmailToggle"
              />
            </div>
          </template>

          <el-form
            ref="emailFormRef"
            :model="emailSettings"
            :rules="emailRules"
            label-width="120px"
            :disabled="!emailSettings.enabled"
          >
            <el-form-item label="SMTP 伺服器" prop="smtpServer">
              <el-input
                v-model="emailSettings.smtpServer"
                placeholder="請輸入 SMTP 伺服器地址"
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="連接埠" prop="smtpPort">
              <el-input-number
                v-model="emailSettings.smtpPort"
                :min="1"
                :max="65535"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item label="加密方式" prop="encryption">
              <el-select v-model="emailSettings.encryption" placeholder="請選擇加密方式" style="width: 200px">
                <el-option label="無" value="none" />
                <el-option label="SSL" value="ssl" />
                <el-option label="TLS" value="tls" />
              </el-select>
            </el-form-item>

            <el-form-item label="使用者名稱" prop="username">
              <el-input
                v-model="emailSettings.username"
                placeholder="請輸入使用者名稱"
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="密碼" prop="password">
              <el-input
                v-model="emailSettings.password"
                placeholder="請輸入密碼"
                type="password"
                show-password
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="寄件者名稱" prop="senderName">
              <el-input
                v-model="emailSettings.senderName"
                placeholder="請輸入寄件者名稱"
                maxlength="50"
              />
            </el-form-item>

            <el-form-item label="寄件者信箱" prop="senderEmail">
              <el-input
                v-model="emailSettings.senderEmail"
                placeholder="請輸入寄件者信箱"
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="連線逾時" prop="timeout">
              <el-input-number
                v-model="emailSettings.timeout"
                :min="10"
                :max="120"
                :step="10"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #909399;">秒</span>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleTestEmail" :loading="testing.email">
                <el-icon><Connection /></el-icon>
                測試連線
              </el-button>
              <el-button @click="handleSaveEmail">
                <el-icon><Check /></el-icon>
                儲存設定
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- SMS 設定 -->
      <el-tab-pane label="SMS 設定" name="sms">
        <el-card class="setting-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>SMS 簡訊服務設定</span>
              <el-switch
                v-model="smsSettings.enabled"
                active-text="啟用"
                inactive-text="停用"
                @change="handleSmsToggle"
              />
            </div>
          </template>

          <el-form
            ref="smsFormRef"
            :model="smsSettings"
            :rules="smsRules"
            label-width="120px"
            :disabled="!smsSettings.enabled"
          >
            <el-form-item label="服務提供商" prop="provider">
              <el-select v-model="smsSettings.provider" placeholder="請選擇服務提供商" style="width: 200px">
                <el-option label="中華電信" value="cht" />
                <el-option label="台灣大哥大" value="twm" />
                <el-option label="遠傳電信" value="fet" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>

            <el-form-item label="API 網址" prop="apiUrl">
              <el-input
                v-model="smsSettings.apiUrl"
                placeholder="請輸入 API 網址"
                maxlength="200"
              />
            </el-form-item>

            <el-form-item label="帳號" prop="account">
              <el-input
                v-model="smsSettings.account"
                placeholder="請輸入帳號"
                maxlength="50"
              />
            </el-form-item>

            <el-form-item label="密碼" prop="password">
              <el-input
                v-model="smsSettings.password"
                placeholder="請輸入密碼"
                type="password"
                show-password
                maxlength="100"
              />
            </el-form-item>

            <el-form-item label="發送者號碼" prop="senderNumber">
              <el-input
                v-model="smsSettings.senderNumber"
                placeholder="請輸入發送者號碼"
                maxlength="20"
              />
            </el-form-item>

            <el-form-item label="連線逾時" prop="timeout">
              <el-input-number
                v-model="smsSettings.timeout"
                :min="10"
                :max="120"
                :step="10"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #909399;">秒</span>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleTestSms" :loading="testing.sms">
                <el-icon><Connection /></el-icon>
                測試連線
              </el-button>
              <el-button @click="handleSaveSms">
                <el-icon><Check /></el-icon>
                儲存設定
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 一般設定 -->
      <el-tab-pane label="一般設定" name="general">
        <el-card class="setting-card" shadow="never">
          <template #header>
            <span>一般通知設定</span>
          </template>

          <el-form
            ref="generalFormRef"
            :model="generalSettings"
            :rules="generalRules"
            label-width="120px"
          >
            <el-form-item label="預設優先級" prop="defaultPriority">
              <el-select v-model="generalSettings.defaultPriority" placeholder="請選擇預設優先級" style="width: 200px">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="緊急" value="urgent" />
              </el-select>
            </el-form-item>

            <el-form-item label="訊息保留天數" prop="messageRetentionDays">
              <el-input-number
                v-model="generalSettings.messageRetentionDays"
                :min="1"
                :max="365"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #909399;">天</span>
            </el-form-item>

            <el-form-item label="最大重試次數" prop="maxRetryCount">
              <el-input-number
                v-model="generalSettings.maxRetryCount"
                :min="0"
                :max="10"
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item label="重試間隔" prop="retryInterval">
              <el-input-number
                v-model="generalSettings.retryInterval"
                :min="1"
                :max="60"
                style="width: 200px"
              />
              <span style="margin-left: 8px; color: #909399;">分鐘</span>
            </el-form-item>

            <el-form-item label="啟用日誌記錄" prop="enableLogging">
              <el-switch
                v-model="generalSettings.enableLogging"
                active-text="啟用"
                inactive-text="停用"
              />
            </el-form-item>

            <el-form-item label="日誌等級" prop="logLevel" v-if="generalSettings.enableLogging">
              <el-select v-model="generalSettings.logLevel" placeholder="請選擇日誌等級" style="width: 200px">
                <el-option label="錯誤" value="error" />
                <el-option label="警告" value="warning" />
                <el-option label="資訊" value="info" />
                <el-option label="除錯" value="debug" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSaveGeneral">
                <el-icon><Check /></el-icon>
                儲存設定
              </el-button>
              <el-button @click="handleResetGeneral">
                <el-icon><Refresh /></el-icon>
                重置為預設值
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Check,
  Connection,
  Refresh
} from '@element-plus/icons-vue'

// 響應式數據
const activeTab = ref('line')

// 測試狀態
const testing = reactive({
  line: false,
  email: false,
  sms: false
})

// 表單引用
const lineFormRef = ref<FormInstance>()
const emailFormRef = ref<FormInstance>()
const smsFormRef = ref<FormInstance>()
const generalFormRef = ref<FormInstance>()

// LINE 設定
const lineSettings = reactive({
  enabled: true,
  accessToken: '',
  defaultGroup: '',
  timeout: 30,
  retryCount: 3
})

// Email 設定
const emailSettings = reactive({
  enabled: true,
  smtpServer: '',
  smtpPort: 587,
  encryption: 'tls',
  username: '',
  password: '',
  senderName: '',
  senderEmail: '',
  timeout: 60
})

// SMS 設定
const smsSettings = reactive({
  enabled: false,
  provider: '',
  apiUrl: '',
  account: '',
  password: '',
  senderNumber: '',
  timeout: 30
})

// 一般設定
const generalSettings = reactive({
  defaultPriority: 'medium',
  messageRetentionDays: 30,
  maxRetryCount: 3,
  retryInterval: 5,
  enableLogging: true,
  logLevel: 'info'
})

// 驗證規則
const lineRules: FormRules = {
  accessToken: [
    { required: true, message: '請輸入 Access Token', trigger: 'blur' }
  ]
}

const emailRules: FormRules = {
  smtpServer: [
    { required: true, message: '請輸入 SMTP 伺服器', trigger: 'blur' }
  ],
  smtpPort: [
    { required: true, message: '請輸入連接埠', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '請輸入使用者名稱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' }
  ],
  senderEmail: [
    { required: true, message: '請輸入寄件者信箱', trigger: 'blur' },
    { type: 'email', message: '請輸入正確的信箱格式', trigger: 'blur' }
  ]
}

const smsRules: FormRules = {
  provider: [
    { required: true, message: '請選擇服務提供商', trigger: 'change' }
  ],
  apiUrl: [
    { required: true, message: '請輸入 API 網址', trigger: 'blur' }
  ],
  account: [
    { required: true, message: '請輸入帳號', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' }
  ]
}

const generalRules: FormRules = {
  defaultPriority: [
    { required: true, message: '請選擇預設優先級', trigger: 'change' }
  ],
  messageRetentionDays: [
    { required: true, message: '請輸入訊息保留天數', trigger: 'blur' }
  ],
  maxRetryCount: [
    { required: true, message: '請輸入最大重試次數', trigger: 'blur' }
  ],
  retryInterval: [
    { required: true, message: '請輸入重試間隔', trigger: 'blur' }
  ]
}

// 方法
const handleLineToggle = (enabled: boolean) => {
  if (!enabled) {
    ElMessage.warning('LINE 服務已停用')
  }
}

const handleEmailToggle = (enabled: boolean) => {
  if (!enabled) {
    ElMessage.warning('Email 服務已停用')
  }
}

const handleSmsToggle = (enabled: boolean) => {
  if (!enabled) {
    ElMessage.warning('SMS 服務已停用')
  }
}

const handleTestLine = async () => {
  try {
    await lineFormRef.value?.validate()
    
    testing.line = true
    // 模擬測試連線
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('LINE 連線測試成功')
  } catch (error) {
    ElMessage.error('LINE 連線測試失敗')
  } finally {
    testing.line = false
  }
}

const handleTestEmail = async () => {
  try {
    await emailFormRef.value?.validate()
    
    testing.email = true
    // 模擬測試連線
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('Email 連線測試成功')
  } catch (error) {
    ElMessage.error('Email 連線測試失敗')
  } finally {
    testing.email = false
  }
}

const handleTestSms = async () => {
  try {
    await smsFormRef.value?.validate()
    
    testing.sms = true
    // 模擬測試連線
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('SMS 連線測試成功')
  } catch (error) {
    ElMessage.error('SMS 連線測試失敗')
  } finally {
    testing.sms = false
  }
}

const handleSaveLine = async () => {
  try {
    await lineFormRef.value?.validate()
    // 調用 API 保存設定
    ElMessage.success('LINE 設定儲存成功')
  } catch (error) {
    ElMessage.error('LINE 設定儲存失敗')
  }
}

const handleSaveEmail = async () => {
  try {
    await emailFormRef.value?.validate()
    // 調用 API 保存設定
    ElMessage.success('Email 設定儲存成功')
  } catch (error) {
    ElMessage.error('Email 設定儲存失敗')
  }
}

const handleSaveSms = async () => {
  try {
    await smsFormRef.value?.validate()
    // 調用 API 保存設定
    ElMessage.success('SMS 設定儲存成功')
  } catch (error) {
    ElMessage.error('SMS 設定儲存失敗')
  }
}

const handleSaveGeneral = async () => {
  try {
    await generalFormRef.value?.validate()
    // 調用 API 保存設定
    ElMessage.success('一般設定儲存成功')
  } catch (error) {
    ElMessage.error('一般設定儲存失敗')
  }
}

const handleResetGeneral = async () => {
  try {
    await ElMessageBox.confirm(
      '確定要重置為預設值嗎？',
      '確認重置',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    Object.assign(generalSettings, {
      defaultPriority: 'medium',
      messageRetentionDays: 30,
      maxRetryCount: 3,
      retryInterval: 5,
      enableLogging: true,
      logLevel: 'info'
    })
    
    ElMessage.success('已重置為預設值')
  } catch (error) {
    // 用戶取消
  }
}

const handleSaveAll = async () => {
  try {
    // 驗證所有表單
    const validations = []
    if (lineSettings.enabled) {
      validations.push(lineFormRef.value?.validate())
    }
    if (emailSettings.enabled) {
      validations.push(emailFormRef.value?.validate())
    }
    if (smsSettings.enabled) {
      validations.push(smsFormRef.value?.validate())
    }
    validations.push(generalFormRef.value?.validate())
    
    await Promise.all(validations)
    
    // 調用 API 保存所有設定
    ElMessage.success('所有設定儲存成功')
  } catch (error) {
    ElMessage.error('設定儲存失敗，請檢查表單內容')
  }
}

const loadSettings = async () => {
  try {
    // 模擬載入設定
    // 實際應用中這裡會調用 API 載入設定
  } catch (error) {
    ElMessage.error('載入設定失敗')
  }
}

// 生命週期
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.setting-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.header-left p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.setting-tabs {
  margin-top: 20px;
}

.setting-card {
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-right {
    align-self: flex-start;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
