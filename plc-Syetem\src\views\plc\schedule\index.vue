<template>
  <div class="schedule-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>排程管理</span>
        </div>
      </template>
      
      <div class="content">
        <el-empty description="排程管理功能開發中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 排程管理頁面
</script>

<style scoped>
.schedule-container {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
