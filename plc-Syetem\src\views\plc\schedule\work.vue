<template>
  <div class="schedule-work-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>工作排程</span>
        </div>
      </template>
      
      <div class="content">
        <el-empty description="工作排程功能開發中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 工作排程頁面
</script>

<style scoped>
.schedule-work-container {
  padding: 20px;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
}

.content {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
