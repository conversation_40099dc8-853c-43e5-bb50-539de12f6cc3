<template>
  <div class="bill-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>電費計算</h2>
      <p>電力費用計算與管理，包含契約設定、電表管理、電價設定等功能</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="bill-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 契約設定 -->
        <el-tab-pane label="契約設定" name="contract">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>電力契約設定</span>
                  <el-button type="primary" @click="showContractDialog = true">
                    新增契約
                  </el-button>
                </div>
              </template>

              <el-table
                v-loading="contractLoading"
                :data="contractList"
                stripe
                border
              >
                <el-table-column prop="name" label="契約名稱" width="200" />
                <el-table-column prop="contractType" label="契約類型" width="150">
                  <template #default="{ row }">
                    <el-tag :type="getContractTypeColor(row.contractType)">
                      {{ getContractTypeText(row.contractType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="capacity" label="契約容量(kW)" width="150" align="right" />
                <el-table-column prop="startDate" label="開始日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.startDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="endDate" label="結束日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.endDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editContract(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleContractStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteContract(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 電表管理 -->
        <el-tab-pane label="電表管理" name="meter">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>電表設定</span>
                  <el-button type="primary" @click="showMeterDialog = true">
                    新增電表
                  </el-button>
                </div>
              </template>

              <el-table
                v-loading="meterLoading"
                :data="meterList"
                stripe
                border
              >
                <el-table-column prop="name" label="電表名稱" width="200" />
                <el-table-column prop="meterNo" label="電表編號" width="150" />
                <el-table-column prop="location" label="安裝位置" width="200" />
                <el-table-column prop="meterType" label="電表類型" width="120">
                  <template #default="{ row }">
                    <el-tag>{{ getMeterTypeText(row.meterType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="multiplier" label="倍率" width="100" align="right" />
                <el-table-column prop="installDate" label="安裝日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.installDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '正常' : '異常' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editMeter(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="info"
                      size="small"
                      @click="viewMeterData(row)"
                    >
                      數據
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteMeter(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 電價設定 -->
        <el-tab-pane label="電價設定" name="rate">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>電價費率設定</span>
                  <el-button type="primary" @click="showRateDialog = true">
                    新增費率
                  </el-button>
                </div>
              </template>

              <el-table
                v-loading="rateLoading"
                :data="rateList"
                stripe
                border
              >
                <el-table-column prop="name" label="費率名稱" width="200" />
                <el-table-column prop="rateType" label="費率類型" width="150">
                  <template #default="{ row }">
                    <el-tag>{{ getRateTypeText(row.rateType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="peakRate" label="尖峰電價(元/度)" width="150" align="right">
                  <template #default="{ row }">
                    {{ row.peakRate?.toFixed(4) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="offPeakRate" label="離峰電價(元/度)" width="150" align="right">
                  <template #default="{ row }">
                    {{ row.offPeakRate?.toFixed(4) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="demandCharge" label="需量費(元/kW)" width="150" align="right">
                  <template #default="{ row }">
                    {{ row.demandCharge?.toFixed(2) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveDate" label="生效日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.effectiveDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editRate(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleRateStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteRate(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 費用查詢 -->
        <el-tab-pane label="費用查詢" name="calculate">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>電費計算查詢</span>
                </div>
              </template>

              <el-form
                ref="calculateFormRef"
                :model="calculateForm"
                :rules="calculateRules"
                label-width="120px"
                class="calculate-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="選擇電表" prop="meterId">
                      <el-select
                        v-model="calculateForm.meterId"
                        placeholder="請選擇電表"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="meter in meterList"
                          :key="meter.id"
                          :label="meter.name"
                          :value="meter.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="計算期間" prop="dateRange">
                      <el-date-picker
                        v-model="calculateForm.dateRange"
                        type="monthrange"
                        range-separator="至"
                        start-placeholder="開始月份"
                        end-placeholder="結束月份"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="calculating"
                    @click="calculateBill"
                  >
                    {{ calculating ? '計算中...' : '開始計算' }}
                  </el-button>
                  <el-button
                    type="success"
                    :disabled="!billResult"
                    @click="exportBillResult"
                  >
                    匯出結果
                  </el-button>
                </el-form-item>
              </el-form>

              <!-- 計算結果 -->
              <div v-if="billResult" class="bill-result">
                <el-card shadow="never">
                  <template #header>
                    <span>計算結果</span>
                  </template>
                  
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.totalUsage }}</div>
                        <div class="result-label">總用電量(度)</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.totalAmount }}</div>
                        <div class="result-label">總電費(元)</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.demandCharge }}</div>
                        <div class="result-label">需量費(元)</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.tax }}</div>
                        <div class="result-label">營業稅(元)</div>
                      </div>
                    </el-col>
                  </el-row>

                  <el-table
                    :data="billResult.details"
                    stripe
                    border
                    style="margin-top: 20px"
                  >
                    <el-table-column prop="month" label="月份" width="100" />
                    <el-table-column prop="usage" label="用電量(度)" width="120" align="right" />
                    <el-table-column prop="peakUsage" label="尖峰用電(度)" width="120" align="right" />
                    <el-table-column prop="offPeakUsage" label="離峰用電(度)" width="120" align="right" />
                    <el-table-column prop="demandKw" label="需量(kW)" width="100" align="right" />
                    <el-table-column prop="amount" label="電費(元)" width="120" align="right" />
                  </el-table>
                </el-card>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 報表匯出 -->
        <el-tab-pane label="報表匯出" name="export">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>電費報表匯出</span>
                </div>
              </template>

              <el-form
                :model="exportForm"
                label-width="120px"
                class="export-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="報表類型">
                      <el-select v-model="exportForm.reportType" style="width: 100%">
                        <el-option label="月度電費報表" value="monthly" />
                        <el-option label="年度電費報表" value="yearly" />
                        <el-option label="電表用量報表" value="meter_usage" />
                        <el-option label="費率分析報表" value="rate_analysis" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="匯出格式">
                      <el-select v-model="exportForm.format" style="width: 100%">
                        <el-option label="Excel (.xlsx)" value="xlsx" />
                        <el-option label="PDF (.pdf)" value="pdf" />
                        <el-option label="CSV (.csv)" value="csv" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="時間範圍">
                  <el-date-picker
                    v-model="exportForm.dateRange"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="開始月份"
                    end-placeholder="結束月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    style="width: 300px"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="exporting"
                    @click="exportReport"
                  >
                    {{ exporting ? '匯出中...' : '匯出報表' }}
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 契約設定對話框 -->
    <el-dialog
      v-model="showContractDialog"
      :title="contractForm.id ? '編輯契約' : '新增契約'"
      width="600px"
    >
      <el-form
        ref="contractFormRef"
        :model="contractForm"
        :rules="contractRules"
        label-width="120px"
      >
        <el-form-item label="契約名稱" prop="name">
          <el-input v-model="contractForm.name" placeholder="請輸入契約名稱" />
        </el-form-item>
        <el-form-item label="契約類型" prop="contractType">
          <el-select v-model="contractForm.contractType" style="width: 100%">
            <el-option label="高壓用戶" value="high_voltage" />
            <el-option label="低壓用戶" value="low_voltage" />
            <el-option label="特高壓用戶" value="extra_high_voltage" />
          </el-select>
        </el-form-item>
        <el-form-item label="契約容量" prop="capacity">
          <el-input-number
            v-model="contractForm.capacity"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
          <span style="margin-left: 8px">kW</span>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="開始日期" prop="startDate">
              <el-date-picker
                v-model="contractForm.startDate"
                type="date"
                placeholder="選擇開始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="結束日期" prop="endDate">
              <el-date-picker
                v-model="contractForm.endDate"
                type="date"
                placeholder="選擇結束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="showContractDialog = false">取消</el-button>
        <el-button type="primary" @click="saveContract">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { billAPI, type ContractItem, type MeterItem, type RateItem } from '@/api/plc/system'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const contractFormRef = ref<InstanceType<typeof ElForm>>()
const calculateFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const activeTab = ref('contract')
const contractLoading = ref(false)
const meterLoading = ref(false)
const rateLoading = ref(false)
const calculating = ref(false)
const exporting = ref(false)

// 對話框狀態
const showContractDialog = ref(false)
const showMeterDialog = ref(false)
const showRateDialog = ref(false)

// 數據列表
const contractList = ref<ContractItem[]>([])
const meterList = ref<MeterItem[]>([])
const rateList = ref<RateItem[]>([])

// 契約表單
const contractForm = reactive({
  id: '',
  name: '',
  contractType: '',
  capacity: 0,
  startDate: '',
  endDate: '',
  status: 'active'
})

// 計算表單
const calculateForm = reactive({
  meterId: '',
  dateRange: [] as string[]
})

// 匯出表單
const exportForm = reactive({
  reportType: 'monthly',
  format: 'xlsx',
  dateRange: [] as string[]
})

// 計算結果
const billResult = ref<any>(null)

// 表單驗證規則
const contractRules = {
  name: [
    { required: true, message: '請輸入契約名稱', trigger: 'blur' }
  ],
  contractType: [
    { required: true, message: '請選擇契約類型', trigger: 'change' }
  ],
  capacity: [
    { required: true, message: '請輸入契約容量', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '請選擇開始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '請選擇結束日期', trigger: 'change' }
  ]
}

const calculateRules = {
  meterId: [
    { required: true, message: '請選擇電表', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '請選擇計算期間', trigger: 'change' }
  ]
}
</script>
