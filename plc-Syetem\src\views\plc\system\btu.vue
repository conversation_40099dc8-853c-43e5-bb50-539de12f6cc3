<template>
  <div class="btu-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>BTU 計算</h2>
      <p>BTU (British Thermal Unit) 熱量計算與管理系統</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="btu-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- BTU表管理 -->
        <el-tab-pane label="BTU表管理" name="meter">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>BTU表設定</span>
                  <el-button type="primary" @click="showMeterDialog = true">
                    新增BTU表
                  </el-button>
                </div>
              </template>

              <el-table
                v-loading="meterLoading"
                :data="meterList"
                stripe
                border
              >
                <el-table-column prop="name" label="BTU表名稱" width="200" />
                <el-table-column prop="meterNo" label="表號" width="150" />
                <el-table-column prop="location" label="安裝位置" width="200" />
                <el-table-column prop="meterType" label="表類型" width="120">
                  <template #default="{ row }">
                    <el-tag>{{ getMeterTypeText(row.meterType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="單位" width="100" />
                <el-table-column prop="multiplier" label="倍率" width="100" align="right" />
                <el-table-column prop="installDate" label="安裝日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.installDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '正常' : '異常' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editMeter(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="info"
                      size="small"
                      @click="viewMeterData(row)"
                    >
                      數據
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteMeter(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- BTU查詢 -->
        <el-tab-pane label="BTU查詢" name="calculate">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>BTU計算查詢</span>
                </div>
              </template>

              <el-form
                ref="calculateFormRef"
                :model="calculateForm"
                :rules="calculateRules"
                label-width="120px"
                class="calculate-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="選擇BTU表" prop="meterId">
                      <el-select
                        v-model="calculateForm.meterId"
                        placeholder="請選擇BTU表"
                        style="width: 100%"
                        multiple
                      >
                        <el-option
                          v-for="meter in meterList"
                          :key="meter.id"
                          :label="meter.name"
                          :value="meter.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="查詢期間" prop="dateRange">
                      <el-date-picker
                        v-model="calculateForm.dateRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="開始時間"
                        end-placeholder="結束時間"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="統計方式">
                      <el-select v-model="calculateForm.statisticType" style="width: 100%">
                        <el-option label="小時統計" value="hourly" />
                        <el-option label="日統計" value="daily" />
                        <el-option label="月統計" value="monthly" />
                        <el-option label="年統計" value="yearly" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="計算方式">
                      <el-select v-model="calculateForm.calculationType" style="width: 100%">
                        <el-option label="累計值" value="cumulative" />
                        <el-option label="平均值" value="average" />
                        <el-option label="最大值" value="maximum" />
                        <el-option label="最小值" value="minimum" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="calculating"
                    @click="calculateBTU"
                  >
                    {{ calculating ? '計算中...' : '開始計算' }}
                  </el-button>
                  <el-button
                    type="success"
                    :disabled="!btuResult"
                    @click="exportBTUResult"
                  >
                    匯出結果
                  </el-button>
                  <el-button
                    type="info"
                    :disabled="!btuResult"
                    @click="showChart = true"
                  >
                    圖表顯示
                  </el-button>
                </el-form-item>
              </el-form>

              <!-- 計算結果 -->
              <div v-if="btuResult" class="btu-result">
                <el-card shadow="never">
                  <template #header>
                    <span>BTU計算結果</span>
                  </template>
                  
                  <!-- 統計摘要 -->
                  <el-row :gutter="20" class="summary-row">
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ btuResult.totalBTU }}</div>
                        <div class="result-label">總BTU值</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ btuResult.avgBTU }}</div>
                        <div class="result-label">平均BTU值</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ btuResult.maxBTU }}</div>
                        <div class="result-label">最大BTU值</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ btuResult.minBTU }}</div>
                        <div class="result-label">最小BTU值</div>
                      </div>
                    </el-col>
                  </el-row>

                  <!-- 詳細數據表格 -->
                  <el-table
                    :data="btuResult.details"
                    stripe
                    border
                    style="margin-top: 20px"
                    max-height="400"
                  >
                    <el-table-column prop="meterName" label="BTU表名稱" width="150" fixed="left" />
                    <el-table-column prop="timestamp" label="時間" width="180">
                      <template #default="{ row }">
                        {{ formatDateTime(row.timestamp) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="btuValue" label="BTU值" width="120" align="right">
                      <template #default="{ row }">
                        {{ formatNumber(row.btuValue) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="temperature" label="溫度(°C)" width="100" align="right">
                      <template #default="{ row }">
                        {{ row.temperature?.toFixed(2) || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="flow" label="流量" width="100" align="right">
                      <template #default="{ row }">
                        {{ row.flow?.toFixed(2) || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="pressure" label="壓力" width="100" align="right">
                      <template #default="{ row }">
                        {{ row.pressure?.toFixed(2) || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="efficiency" label="效率(%)" width="100" align="right">
                      <template #default="{ row }">
                        {{ row.efficiency?.toFixed(1) || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="狀態" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getStatusType(row.status)">
                          {{ getStatusText(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-card>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- BTU表設定對話框 -->
    <el-dialog
      v-model="showMeterDialog"
      :title="meterForm.id ? '編輯BTU表' : '新增BTU表'"
      width="600px"
    >
      <el-form
        ref="meterFormRef"
        :model="meterForm"
        :rules="meterRules"
        label-width="120px"
      >
        <el-form-item label="BTU表名稱" prop="name">
          <el-input v-model="meterForm.name" placeholder="請輸入BTU表名稱" />
        </el-form-item>
        <el-form-item label="表號" prop="meterNo">
          <el-input v-model="meterForm.meterNo" placeholder="請輸入表號" />
        </el-form-item>
        <el-form-item label="安裝位置" prop="location">
          <el-input v-model="meterForm.location" placeholder="請輸入安裝位置" />
        </el-form-item>
        <el-form-item label="表類型" prop="meterType">
          <el-select v-model="meterForm.meterType" style="width: 100%">
            <el-option label="熱量表" value="heat_meter" />
            <el-option label="冷量表" value="cooling_meter" />
            <el-option label="蒸汽表" value="steam_meter" />
            <el-option label="綜合表" value="combined_meter" />
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="單位" prop="unit">
              <el-select v-model="meterForm.unit" style="width: 100%">
                <el-option label="BTU" value="BTU" />
                <el-option label="kBTU" value="kBTU" />
                <el-option label="MBTU" value="MBTU" />
                <el-option label="kcal" value="kcal" />
                <el-option label="kJ" value="kJ" />
                <el-option label="MJ" value="MJ" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="倍率" prop="multiplier">
              <el-input-number
                v-model="meterForm.multiplier"
                :min="0.1"
                :max="10000"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="安裝日期" prop="installDate">
          <el-date-picker
            v-model="meterForm.installDate"
            type="date"
            placeholder="選擇安裝日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="備註">
          <el-input
            v-model="meterForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入備註"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showMeterDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMeter">確認</el-button>
      </template>
    </el-dialog>

    <!-- 圖表顯示對話框 -->
    <el-dialog
      v-model="showChart"
      title="BTU趨勢圖表"
      width="80%"
    >
      <div class="chart-container">
        <!-- 這裡可以整合圖表庫如 ECharts -->
        <el-empty description="圖表功能開發中..." />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { btuAPI, type BTUMeterItem } from '@/api/plc/system'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const meterFormRef = ref<InstanceType<typeof ElForm>>()
const calculateFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const activeTab = ref('meter')
const meterLoading = ref(false)
const calculating = ref(false)
const showMeterDialog = ref(false)
const showChart = ref(false)

// 數據列表
const meterList = ref<BTUMeterItem[]>([])

// BTU表表單
const meterForm = reactive({
  id: '',
  name: '',
  meterNo: '',
  location: '',
  meterType: '',
  unit: 'BTU',
  multiplier: 1,
  installDate: '',
  description: '',
  status: 'active'
})

// 計算表單
const calculateForm = reactive({
  meterId: [] as string[],
  dateRange: [] as string[],
  statisticType: 'daily',
  calculationType: 'cumulative'
})

// 計算結果
const btuResult = ref<any>(null)

// 表單驗證規則
const meterRules = {
  name: [
    { required: true, message: '請輸入BTU表名稱', trigger: 'blur' }
  ],
  meterNo: [
    { required: true, message: '請輸入表號', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '請輸入安裝位置', trigger: 'blur' }
  ],
  meterType: [
    { required: true, message: '請選擇表類型', trigger: 'change' }
  ],
  unit: [
    { required: true, message: '請選擇單位', trigger: 'change' }
  ],
  multiplier: [
    { required: true, message: '請輸入倍率', trigger: 'blur' }
  ],
  installDate: [
    { required: true, message: '請選擇安裝日期', trigger: 'change' }
  ]
}

const calculateRules = {
  meterId: [
    { required: true, message: '請選擇BTU表', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '請選擇查詢期間', trigger: 'change' }
  ]
}

/**
 * 獲取表類型文字
 */
const getMeterTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    heat_meter: '熱量表',
    cooling_meter: '冷量表',
    steam_meter: '蒸汽表',
    combined_meter: '綜合表'
  }
  return typeMap[type] || type
}

/**
 * 格式化日期
 */
const formatDate = (date: string): string => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-TW')
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 格式化數字
 */
const formatNumber = (num: number): string => {
  if (num === null || num === undefined) return '-'
  return num.toLocaleString('zh-TW', { maximumFractionDigits: 2 })
}

/**
 * 獲取狀態類型
 */
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    normal: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return statusMap[status] || 'info'
}

/**
 * 獲取狀態文字
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    normal: '正常',
    warning: '警告',
    error: '異常'
  }
  return statusMap[status] || '未知'
}

/**
 * 編輯BTU表
 */
const editMeter = (row: BTUMeterItem) => {
  Object.assign(meterForm, row)
  showMeterDialog.value = true
}

/**
 * 查看BTU表數據
 */
const viewMeterData = (row: BTUMeterItem) => {
  ElMessage.info('BTU表數據查看功能開發中...')
}

/**
 * 刪除BTU表
 */
const deleteMeter = async (row: BTUMeterItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除BTU表 "${row.name}" 嗎？`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 調用API刪除BTU表
    ElMessage.success('BTU表刪除成功')
    await loadMeterList()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('刪除BTU表失敗:', error)
      ElMessage.error('刪除BTU表失敗')
    }
  }
}

/**
 * 保存BTU表
 */
const saveMeter = async () => {
  if (!meterFormRef.value) return

  try {
    await meterFormRef.value.validate()

    // 調用API保存BTU表
    const saveData = {
      Name: meterForm.name,
      RegionId: meterForm.regionId || undefined,
      MeterType: meterForm.meterType || undefined,
      Properties: {
        meterNo: meterForm.meterNo,
        location: meterForm.location,
        multiplier: meterForm.multiplier,
        installDate: meterForm.installDate,
        description: meterForm.description
      },
      Tags: meterForm.tags || []
    }

    let response
    if (meterForm.id) {
      // 更新BTU表
      console.log('更新BTU表 API 請求:', meterForm.id, saveData)
      response = await btuAPI.updateMeter(meterForm.id, saveData)
      console.log('更新BTU表 API 響應:', response)
    } else {
      // 新增BTU表
      console.log('新增BTU表 API 請求:', saveData)
      response = await btuAPI.createMeter(saveData)
      console.log('新增BTU表 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(meterForm.id ? 'BTU表更新成功' : 'BTU表新增成功')
      showMeterDialog.value = false
      await loadMeterList()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存BTU表失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存BTU表失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

/**
 * 計算BTU
 */
const calculateBTU = async () => {
  if (!calculateFormRef.value) return

  try {
    await calculateFormRef.value.validate()

    calculating.value = true

    // TODO: 調用API計算BTU
    // 模擬數據
    btuResult.value = {
      totalBTU: '1,234,567',
      avgBTU: '12,345',
      maxBTU: '23,456',
      minBTU: '8,901',
      details: [
        {
          meterName: 'BTU表1',
          timestamp: new Date().toISOString(),
          btuValue: 12345,
          temperature: 25.5,
          flow: 100.2,
          pressure: 1.5,
          efficiency: 85.2,
          status: 'normal'
        }
      ]
    }

    ElMessage.success('BTU計算完成')

  } catch (error: any) {
    console.error('BTU計算失敗:', error)
    ElMessage.error(error.message || 'BTU計算失敗')
  } finally {
    calculating.value = false
  }
}

/**
 * 匯出BTU結果
 */
const exportBTUResult = () => {
  ElMessage.info('BTU結果匯出功能開發中...')
}

/**
 * 載入BTU表列表
 */
const loadMeterList = async () => {
  try {
    meterLoading.value = true

    // TODO: 調用API載入BTU表列表
    // 模擬數據
    meterList.value = [
      {
        id: '1',
        name: 'BTU表1',
        meterNo: 'BTU001',
        location: '冷卻塔1',
        meterType: 'heat_meter',
        unit: 'BTU',
        multiplier: 1,
        installDate: '2023-01-01',
        description: '主要冷卻系統',
        status: 'active'
      }
    ]

  } catch (error: any) {
    console.error('載入BTU表列表失敗:', error)
    ElMessage.error(error.message || '載入BTU表列表失敗')
  } finally {
    meterLoading.value = false
  }
}

// 生命週期
onMounted(async () => {
  await loadMeterList()
})
</script>

<style scoped>
.btu-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.btu-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.tab-content {
  padding: 20px 0;
}

.calculate-form {
  margin-bottom: 20px;
}

.btu-result {
  margin-top: 20px;
}

.summary-row {
  margin-bottom: 20px;
}

.result-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.result-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.result-label {
  font-size: 14px;
  color: #909399;
}

.chart-container {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
