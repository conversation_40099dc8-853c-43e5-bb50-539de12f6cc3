<template>
  <div class="uninstall-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>電力卸載</h2>
      <p>電力卸載程序管理，用於緊急情況下的電力負載控制</p>
    </div>

    <!-- 卸載程序列表 -->
    <el-card class="uninstall-card">
      <template #header>
        <div class="card-header">
          <span>卸載程序管理</span>
          <div class="header-actions">
            <el-button
              type="primary"
              @click="showAddDialog = true"
            >
              新增卸載程序
            </el-button>
            <el-button
              type="success"
              :disabled="selectedPrograms.length === 0"
              @click="batchExecute"
            >
              批次執行
            </el-button>
            <el-button
              type="danger"
              :disabled="selectedPrograms.length === 0"
              @click="batchDelete"
            >
              批次刪除
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜尋和篩選 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜尋程序名稱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="statusFilter"
              placeholder="狀態篩選"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="啟用" value="active" />
              <el-option label="停用" value="inactive" />
              <el-option label="執行中" value="running" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="priorityFilter"
              placeholder="優先級篩選"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="info" @click="refreshList">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 程序列表 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="filteredProgramList"
        stripe
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="name" label="程序名稱" width="200">
          <template #default="{ row }">
            <div class="program-name">
              <el-icon class="program-icon">
                <Lightning />
              </el-icon>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" />

        <el-table-column prop="priority" label="優先級" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="loadCapacity" label="卸載容量(kW)" width="150" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.loadCapacity) }}
          </template>
        </el-table-column>

        <el-table-column prop="executionTime" label="執行時間(秒)" width="120" align="right" />

        <el-table-column prop="status" label="狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastExecuted" label="最後執行" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.lastExecuted) }}
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="建立時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              :disabled="row.status === 'running'"
              @click="executeProgram(row)"
            >
              執行
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="editProgram(row)"
            >
              編輯
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="toggleProgramStatus(row)"
            >
              {{ row.status === 'active' ? '停用' : '啟用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteProgram(row)"
            >
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯程序對話框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="programForm.id ? '編輯卸載程序' : '新增卸載程序'"
      width="700px"
    >
      <el-form
        ref="programFormRef"
        :model="programForm"
        :rules="programRules"
        label-width="120px"
      >
        <el-form-item label="程序名稱" prop="name">
          <el-input v-model="programForm.name" placeholder="請輸入程序名稱" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="programForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入程序描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="優先級" prop="priority">
              <el-select v-model="programForm.priority" style="width: 100%">
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="卸載容量" prop="loadCapacity">
              <el-input-number
                v-model="programForm.loadCapacity"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span style="margin-left: 8px">kW</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="執行時間" prop="executionTime">
              <el-input-number
                v-model="programForm.executionTime"
                :min="1"
                :max="3600"
                style="width: 100%"
              />
              <span style="margin-left: 8px">秒</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="延遲時間" prop="delayTime">
              <el-input-number
                v-model="programForm.delayTime"
                :min="0"
                :max="300"
                style="width: 100%"
              />
              <span style="margin-left: 8px">秒</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="卸載階段">
          <el-table
            :data="programForm.stages"
            border
            style="width: 100%"
          >
            <el-table-column prop="order" label="順序" width="80" align="center" />
            <el-table-column prop="deviceName" label="設備名稱" width="150">
              <template #default="{ row, $index }">
                <el-input v-model="row.deviceName" placeholder="設備名稱" />
              </template>
            </el-table-column>
            <el-table-column prop="loadKw" label="負載(kW)" width="120">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.loadKw"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column prop="delaySeconds" label="延遲(秒)" width="100">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.delaySeconds"
                  :min="0"
                  :max="60"
                  style="width: 100%"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeStage($index)"
                >
                  刪除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div style="margin-top: 10px">
            <el-button type="primary" @click="addStage">新增階段</el-button>
          </div>
        </el-form-item>

        <el-form-item label="自動執行">
          <el-switch v-model="programForm.autoExecute" />
          <span style="margin-left: 10px; color: #909399; font-size: 12px">
            啟用後將在緊急情況下自動執行
          </span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProgram">確認</el-button>
      </template>
    </el-dialog>

    <!-- 執行確認對話框 -->
    <el-dialog
      v-model="showExecuteDialog"
      title="執行確認"
      width="500px"
    >
      <div class="execute-warning">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <div class="warning-content">
          <h3>警告：即將執行電力卸載程序</h3>
          <p>程序名稱：{{ executeTarget?.name }}</p>
          <p>卸載容量：{{ executeTarget?.loadCapacity }} kW</p>
          <p>執行時間：{{ executeTarget?.executionTime }} 秒</p>
          <p style="color: #f56c6c; font-weight: bold;">
            此操作將影響電力供應，請確認是否繼續？
          </p>
        </div>
      </div>

      <template #footer>
        <el-button @click="showExecuteDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmExecute">確認執行</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Lightning, 
  Warning 
} from '@element-plus/icons-vue'
import { uninstallAPI, type UninstallProgram } from '@/api/plc/system'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const programFormRef = ref<InstanceType<typeof ElForm>>()
const tableRef = ref()

// 響應式數據
const loading = ref(false)
const showAddDialog = ref(false)
const showExecuteDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 數據列表
const programList = ref<UninstallProgram[]>([])
const selectedPrograms = ref<UninstallProgram[]>([])
const executeTarget = ref<UninstallProgram | null>(null)

// 程序表單
const programForm = reactive({
  id: '',
  name: '',
  description: '',
  priority: 'medium',
  loadCapacity: 0,
  executionTime: 30,
  delayTime: 0,
  autoExecute: false,
  stages: [] as Array<{
    order: number
    deviceName: string
    loadKw: number
    delaySeconds: number
  }>,
  status: 'active'
})

// 表單驗證規則
const programRules = {
  name: [
    { required: true, message: '請輸入程序名稱', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '請輸入程序描述', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '請選擇優先級', trigger: 'change' }
  ],
  loadCapacity: [
    { required: true, message: '請輸入卸載容量', trigger: 'blur' }
  ],
  executionTime: [
    { required: true, message: '請輸入執行時間', trigger: 'blur' }
  ]
}

// 計算屬性
const filteredProgramList = computed(() => {
  let filtered = programList.value

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }

  // 優先級篩選
  if (priorityFilter.value) {
    filtered = filtered.filter(item => item.priority === priorityFilter.value)
  }

  return filtered
})
</script>
