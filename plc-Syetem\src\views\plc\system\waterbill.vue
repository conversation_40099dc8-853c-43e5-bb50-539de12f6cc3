<template>
  <div class="waterbill-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>水費計算</h2>
      <p>水費計算與管理，包含水表管理、水價設定、費用查詢等功能</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="waterbill-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 水表管理 -->
        <el-tab-pane label="水表管理" name="meter">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>水表設定</span>
                  <el-button type="primary" @click="showMeterDialog = true">
                    新增水表
                  </el-button>
                </div>
              </template>

              <el-table
                v-loading="meterLoading"
                :data="meterList"
                stripe
                border
              >
                <el-table-column prop="name" label="水表名稱" width="200" />
                <el-table-column prop="meterNo" label="水表編號" width="150" />
                <el-table-column prop="location" label="安裝位置" width="200" />
                <el-table-column prop="meterType" label="水表類型" width="120">
                  <template #default="{ row }">
                    <el-tag>{{ getMeterTypeText(row.meterType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="caliber" label="口徑(mm)" width="100" align="right" />
                <el-table-column prop="multiplier" label="倍率" width="100" align="right" />
                <el-table-column prop="installDate" label="安裝日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.installDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '正常' : '異常' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editMeter(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="info"
                      size="small"
                      @click="viewMeterData(row)"
                    >
                      數據
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteMeter(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 水價設定 -->
        <el-tab-pane label="水價設定" name="rate">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>水價費率設定</span>
                  <el-button type="primary" @click="showRateDialog = true">
                    新增費率
                  </el-button>
                </div>
              </template>

              <el-table
                v-loading="rateLoading"
                :data="rateList"
                stripe
                border
              >
                <el-table-column prop="name" label="費率名稱" width="200" />
                <el-table-column prop="rateType" label="費率類型" width="150">
                  <template #default="{ row }">
                    <el-tag>{{ getRateTypeText(row.rateType) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="tier1Rate" label="第一階(元/度)" width="120" align="right">
                  <template #default="{ row }">
                    {{ row.tier1Rate?.toFixed(2) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="tier1Limit" label="第一階上限" width="120" align="right" />
                <el-table-column prop="tier2Rate" label="第二階(元/度)" width="120" align="right">
                  <template #default="{ row }">
                    {{ row.tier2Rate?.toFixed(2) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="tier2Limit" label="第二階上限" width="120" align="right" />
                <el-table-column prop="tier3Rate" label="第三階(元/度)" width="120" align="right">
                  <template #default="{ row }">
                    {{ row.tier3Rate?.toFixed(2) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="basicFee" label="基本費(元)" width="100" align="right">
                  <template #default="{ row }">
                    {{ row.basicFee?.toFixed(0) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="effectiveDate" label="生效日期" width="120">
                  <template #default="{ row }">
                    {{ formatDate(row.effectiveDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editRate(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleRateStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteRate(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 費用查詢 -->
        <el-tab-pane label="費用查詢" name="calculate">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>水費計算查詢</span>
                </div>
              </template>

              <el-form
                ref="calculateFormRef"
                :model="calculateForm"
                :rules="calculateRules"
                label-width="120px"
                class="calculate-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="選擇水表" prop="meterId">
                      <el-select
                        v-model="calculateForm.meterId"
                        placeholder="請選擇水表"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="meter in meterList"
                          :key="meter.id"
                          :label="meter.name"
                          :value="meter.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="計算期間" prop="dateRange">
                      <el-date-picker
                        v-model="calculateForm.dateRange"
                        type="monthrange"
                        range-separator="至"
                        start-placeholder="開始月份"
                        end-placeholder="結束月份"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="calculating"
                    @click="calculateBill"
                  >
                    {{ calculating ? '計算中...' : '開始計算' }}
                  </el-button>
                  <el-button
                    type="success"
                    :disabled="!billResult"
                    @click="exportBillResult"
                  >
                    匯出結果
                  </el-button>
                </el-form-item>
              </el-form>

              <!-- 計算結果 -->
              <div v-if="billResult" class="bill-result">
                <el-card shadow="never">
                  <template #header>
                    <span>計算結果</span>
                  </template>
                  
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.totalUsage }}</div>
                        <div class="result-label">總用水量(度)</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.totalAmount }}</div>
                        <div class="result-label">總水費(元)</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.basicFee }}</div>
                        <div class="result-label">基本費(元)</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="result-item">
                        <div class="result-value">{{ billResult.tax }}</div>
                        <div class="result-label">營業稅(元)</div>
                      </div>
                    </el-col>
                  </el-row>

                  <el-table
                    :data="billResult.details"
                    stripe
                    border
                    style="margin-top: 20px"
                  >
                    <el-table-column prop="month" label="月份" width="100" />
                    <el-table-column prop="usage" label="用水量(度)" width="120" align="right" />
                    <el-table-column prop="tier1Usage" label="第一階用量" width="120" align="right" />
                    <el-table-column prop="tier2Usage" label="第二階用量" width="120" align="right" />
                    <el-table-column prop="tier3Usage" label="第三階用量" width="120" align="right" />
                    <el-table-column prop="amount" label="水費(元)" width="120" align="right" />
                  </el-table>
                </el-card>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 水表設定對話框 -->
    <el-dialog
      v-model="showMeterDialog"
      :title="meterForm.id ? '編輯水表' : '新增水表'"
      width="600px"
    >
      <el-form
        ref="meterFormRef"
        :model="meterForm"
        :rules="meterRules"
        label-width="120px"
      >
        <el-form-item label="水表名稱" prop="name">
          <el-input v-model="meterForm.name" placeholder="請輸入水表名稱" />
        </el-form-item>
        <el-form-item label="水表編號" prop="meterNo">
          <el-input v-model="meterForm.meterNo" placeholder="請輸入水表編號" />
        </el-form-item>
        <el-form-item label="安裝位置" prop="location">
          <el-input v-model="meterForm.location" placeholder="請輸入安裝位置" />
        </el-form-item>
        <el-form-item label="水表類型" prop="meterType">
          <el-select v-model="meterForm.meterType" style="width: 100%">
            <el-option label="機械式水表" value="mechanical" />
            <el-option label="電子式水表" value="electronic" />
            <el-option label="智慧水表" value="smart" />
            <el-option label="超音波水表" value="ultrasonic" />
          </el-select>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="口徑" prop="caliber">
              <el-input-number
                v-model="meterForm.caliber"
                :min="15"
                :max="300"
                style="width: 100%"
              />
              <span style="margin-left: 8px">mm</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="倍率" prop="multiplier">
              <el-input-number
                v-model="meterForm.multiplier"
                :min="0.1"
                :max="1000"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="安裝日期" prop="installDate">
          <el-date-picker
            v-model="meterForm.installDate"
            type="date"
            placeholder="選擇安裝日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="備註">
          <el-input
            v-model="meterForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入備註"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showMeterDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMeter">確認</el-button>
      </template>
    </el-dialog>

    <!-- 水價設定對話框 -->
    <el-dialog
      v-model="showRateDialog"
      :title="rateForm.id ? '編輯水價' : '新增水價'"
      width="700px"
    >
      <el-form
        ref="rateFormRef"
        :model="rateForm"
        :rules="rateRules"
        label-width="120px"
      >
        <el-form-item label="費率名稱" prop="name">
          <el-input v-model="rateForm.name" placeholder="請輸入費率名稱" />
        </el-form-item>
        <el-form-item label="費率類型" prop="rateType">
          <el-select v-model="rateForm.rateType" style="width: 100%">
            <el-option label="住宅用水" value="residential" />
            <el-option label="商業用水" value="commercial" />
            <el-option label="工業用水" value="industrial" />
            <el-option label="機關用水" value="government" />
          </el-select>
        </el-form-item>

        <!-- 階梯水價設定 -->
        <el-divider content-position="left">階梯水價設定</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="第一階水價" prop="tier1Rate">
              <el-input-number
                v-model="rateForm.tier1Rate"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span style="margin-left: 8px">元/度</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="第一階上限" prop="tier1Limit">
              <el-input-number
                v-model="rateForm.tier1Limit"
                :min="0"
                style="width: 100%"
              />
              <span style="margin-left: 8px">度</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="第二階水價" prop="tier2Rate">
              <el-input-number
                v-model="rateForm.tier2Rate"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span style="margin-left: 8px">元/度</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="第二階上限" prop="tier2Limit">
              <el-input-number
                v-model="rateForm.tier2Limit"
                :min="0"
                style="width: 100%"
              />
              <span style="margin-left: 8px">度</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="第三階水價" prop="tier3Rate">
              <el-input-number
                v-model="rateForm.tier3Rate"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
              <span style="margin-left: 8px">元/度</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="基本費" prop="basicFee">
              <el-input-number
                v-model="rateForm.basicFee"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
              <span style="margin-left: 8px">元</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            v-model="rateForm.effectiveDate"
            type="date"
            placeholder="選擇生效日期"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRate">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { waterbillAPI, type WaterMeterItem, type WaterRateItem } from '@/api/plc/system'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const meterFormRef = ref<InstanceType<typeof ElForm>>()
const rateFormRef = ref<InstanceType<typeof ElForm>>()
const calculateFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const activeTab = ref('meter')
const meterLoading = ref(false)
const rateLoading = ref(false)
const calculating = ref(false)

// 對話框狀態
const showMeterDialog = ref(false)
const showRateDialog = ref(false)

// 數據列表
const meterList = ref<WaterMeterItem[]>([])
const rateList = ref<WaterRateItem[]>([])

// 水表表單
const meterForm = reactive({
  id: '',
  name: '',
  meterNo: '',
  location: '',
  meterType: '',
  caliber: 20,
  multiplier: 1,
  installDate: '',
  description: '',
  status: 'active'
})

// 水價表單
const rateForm = reactive({
  id: '',
  name: '',
  rateType: '',
  tier1Rate: 0,
  tier1Limit: 0,
  tier2Rate: 0,
  tier2Limit: 0,
  tier3Rate: 0,
  basicFee: 0,
  effectiveDate: '',
  status: 'active'
})

// 計算表單
const calculateForm = reactive({
  meterId: '',
  dateRange: [] as string[]
})

// 計算結果
const billResult = ref<any>(null)

// 表單驗證規則
const meterRules = {
  name: [
    { required: true, message: '請輸入水表名稱', trigger: 'blur' }
  ],
  meterNo: [
    { required: true, message: '請輸入水表編號', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '請輸入安裝位置', trigger: 'blur' }
  ],
  meterType: [
    { required: true, message: '請選擇水表類型', trigger: 'change' }
  ],
  caliber: [
    { required: true, message: '請輸入口徑', trigger: 'blur' }
  ],
  multiplier: [
    { required: true, message: '請輸入倍率', trigger: 'blur' }
  ],
  installDate: [
    { required: true, message: '請選擇安裝日期', trigger: 'change' }
  ]
}

const rateRules = {
  name: [
    { required: true, message: '請輸入費率名稱', trigger: 'blur' }
  ],
  rateType: [
    { required: true, message: '請選擇費率類型', trigger: 'change' }
  ],
  tier1Rate: [
    { required: true, message: '請輸入第一階水價', trigger: 'blur' }
  ],
  tier1Limit: [
    { required: true, message: '請輸入第一階上限', trigger: 'blur' }
  ],
  effectiveDate: [
    { required: true, message: '請選擇生效日期', trigger: 'change' }
  ]
}

const calculateRules = {
  meterId: [
    { required: true, message: '請選擇水表', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '請選擇計算期間', trigger: 'change' }
  ]
}
</script>

<style scoped>
.waterbill-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.waterbill-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.tab-content {
  padding: 20px 0;
}

.calculate-form {
  margin-bottom: 20px;
}

.bill-result {
  margin-top: 20px;
}

.result-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.result-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.result-label {
  font-size: 14px;
  color: #909399;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
