<template>
  <div class="channel-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>通道管理</h2>
      <p>管理 PLC 系統的通訊通道，包含不同的驅動程式和資料獲取方式</p>
    </div>

    <!-- 通道列表 -->
    <el-card class="channel-card">
      <template #header>
        <div class="card-header">
          <span>通道列表</span>
          <el-button type="primary" @click="showAddDialog = true">
            新增通道
          </el-button>
        </div>
      </template>

      <!-- 搜尋和篩選 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜尋通道名稱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="statusFilter"
              placeholder="狀態篩選"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="啟用" value="active" />
              <el-option label="停用" value="inactive" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="driverFilter"
              placeholder="驅動程式篩選"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="TCP" value="TCP" />
              <el-option label="OBIX" value="OBIX" />
              <el-option label="Desigo CC" value="DesigoCC" />
              <el-option label="虛擬點" value="Virtual" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button type="info" @click="refreshList">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 通道表格 -->
      <el-table
        v-loading="loading"
        :data="filteredChannelList"
        stripe
        border
      >
        <el-table-column prop="name" label="通道名稱" width="200">
          <template #default="{ row }">
            <div class="channel-name">
              <el-icon class="channel-icon">
                <Connection />
              </el-icon>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="driverType" label="驅動程式" width="150">
          <template #default="{ row }">
            <el-tag :type="getDriverTypeColor(row.driverType)">
              {{ getDriverTypeText(row.driverType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="dataMode" label="資料獲取方式" width="150">
          <template #default="{ row }">
            <el-tag>{{ getDataModeText(row.dataMode) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="說明" min-width="200" />

        <el-table-column prop="status" label="狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '啟用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="建立時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editChannel(row)"
            >
              編輯
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="toggleChannelStatus(row)"
            >
              {{ row.status === 'active' ? '停用' : '啟用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteChannel(row)"
            >
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯通道對話框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="channelForm.id ? '編輯通道' : '新增通道'"
      width="600px"
    >
      <el-form
        ref="channelFormRef"
        :model="channelForm"
        :rules="channelRules"
        label-width="120px"
      >
        <el-form-item label="狀態" prop="status">
          <el-radio-group v-model="channelForm.status">
            <el-radio value="active">啟用</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="通道名稱" prop="name">
          <el-input v-model="channelForm.name" placeholder="請輸入通道名稱" />
        </el-form-item>

        <el-form-item label="驅動程式" prop="driverType">
          <el-select v-model="channelForm.driverType" style="width: 100%">
            <el-option
              v-for="option in driverOptions"
              :key="option.Code"
              :label="option.Name"
              :value="option.Code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="資料獲取方式" prop="dataMode">
          <el-select v-model="channelForm.dataMode" style="width: 100%">
            <el-option
              v-for="option in dataOptions"
              :key="option.Code"
              :label="option.Name"
              :value="option.Code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="說明" prop="description">
          <el-input
            v-model="channelForm.description"
            placeholder="請輸入說明"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveChannel">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Connection 
} from '@element-plus/icons-vue'
// 通道項目類型定義（匹配舊系統）
interface ChannelItem {
  id: string
  name: string
  driverType: string
  dataMode: string
  description: string
  status: 'active' | 'inactive'
  createTime?: string
  customerName?: string
  driverCode?: number
  fetchDataModeCode?: number
}
import { usePLCAuthStore } from '@/store/modules/plc-auth'
import { plcDataService } from '@/utils/plc/dataService'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const channelFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const loading = ref(false)
const showAddDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const driverFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 數據列表
const channelList = ref<ChannelItem[]>([])

// 選項列表（從後端動態載入）
const driverOptions = ref<Array<{Code: number, Name: string}>>([])
const dataOptions = ref<Array<{Code: number, Name: string}>>([])

// 通道表單
const channelForm = reactive({
  id: '',
  name: '',
  driverType: '',
  dataMode: '',
  description: '',
  status: 'active'
})

// 表單驗證規則
const channelRules = {
  name: [
    { required: true, message: '請輸入通道名稱', trigger: 'blur' }
  ],
  driverType: [
    { required: true, message: '請選擇驅動程式', trigger: 'change' }
  ],
  dataMode: [
    { required: true, message: '請選擇資料獲取方式', trigger: 'change' }
  ],
  status: [
    { required: true, message: '請選擇狀態', trigger: 'change' }
  ]
}

// 計算屬性
const filteredChannelList = computed(() => {
  let filtered = channelList.value

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }

  // 驅動程式篩選
  if (driverFilter.value) {
    filtered = filtered.filter(item => item.driverType === driverFilter.value)
  }

  return filtered
})

/**
 * 獲取驅動程式類型顏色
 */
const getDriverTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    TCP: 'primary',
    OBIX: 'success',
    DesigoCC: 'warning',
    Virtual: 'info'
  }
  return colorMap[type] || 'default'
}

/**
 * 獲取驅動程式類型文字
 */
const getDriverTypeText = (type: string): string => {
  // 直接返回從後端載入的名稱，因為已經是正確的顯示文字
  return type || '未知'
}

/**
 * 獲取資料獲取方式文字
 */
const getDataModeText = (mode: string): string => {
  // 直接返回從後端載入的名稱，因為已經是正確的顯示文字
  return mode || '未知'
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 搜尋處理
 */
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中處理
}

/**
 * 篩選處理
 */
const handleFilter = () => {
  // 篩選邏輯已在計算屬性中處理
}

/**
 * 分頁大小變更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadChannelList()
}

/**
 * 當前頁變更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadChannelList()
}

/**
 * 刷新列表
 */
const refreshList = () => {
  loadChannelList()
}

/**
 * 編輯通道
 */
const editChannel = (row: ChannelItem) => {
  // 將選中的通道數據載入到表單中
  Object.assign(channelForm, {
    id: row.id,
    name: row.name,
    driverType: row.driverCode || row.driverType, // 使用 Code 值
    dataMode: row.fetchDataModeCode || row.dataMode, // 使用 Code 值
    description: row.description,
    status: row.status
  })
  showAddDialog.value = true
}

/**
 * 切換通道狀態
 */
const toggleChannelStatus = async (row: ChannelItem) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    const statusText = newStatus === 'active' ? '啟用' : '停用'

    await ElMessageBox.confirm(
      `確定要${statusText}通道 "${row.name}" 嗎？`,
      '狀態變更確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用 API 更新通道狀態
    const response = await tagsAPI.setChannelStatus({
      TagChannelId: row.id,
      TargetStatus: newStatus === 'active'
    })

    console.log('通道狀態更新 API 響應:', response)

    // 檢查響應是否成功
    if (response && (response.success || response.ReturnCode === 1)) {
      row.status = newStatus
      ElMessage.success(`通道${statusText}成功`, {
        duration: 5000,
        showClose: true
      })
    } else {
      // 處理 API 返回的錯誤
      let errorMessage = `通道${statusText}失敗`
      if (response?.Message) {
        errorMessage += `: ${response.Message}`
      } else if (response?.message) {
        errorMessage += `: ${response.message}`
      } else if (response?.error) {
        errorMessage += `: ${response.error}`
      }

      ElMessage.error(errorMessage, {
        duration: 5000,
        showClose: true
      })
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('切換通道狀態失敗:', error)

      // 提取錯誤訊息
      let errorMessage = '切換通道狀態失敗'
      if (error?.response?.data?.Message) {
        errorMessage += `: ${error.response.data.Message}`
      } else if (error?.response?.data?.message) {
        errorMessage += `: ${error.response.data.message}`
      } else if (error?.message) {
        errorMessage += `: ${error.message}`
      } else if (typeof error === 'string') {
        errorMessage += `: ${error}`
      }

      ElMessage.error(errorMessage, {
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 刪除通道
 */
const deleteChannel = async (row: ChannelItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除通道 "${row.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除通道
    const deleteData = {
      TagChannelId: row.id
    }

    console.log('刪除通道 API 請求:', deleteData)
    const response = await tagsAPI.deleteTagChannel(deleteData)
    console.log('刪除通道 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('通道刪除成功')
      await loadChannelList()
    } else {
      const errorMessage = response?.Message || response?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除通道失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除通道失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存通道
 */
const saveChannel = async () => {
  if (!channelFormRef.value) return

  try {
    await channelFormRef.value.validate()

    // 調用API保存通道
    const saveData = {
      Name: channelForm.name,
      Description: channelForm.description || undefined,
      ChannelType: channelForm.driverType || undefined
    }

    let response
    if (channelForm.id) {
      // 更新通道
      const updateData = {
        Id: channelForm.id,
        ...saveData
      }
      console.log('更新通道 API 請求:', updateData)
      response = await tagsAPI.updateTagChannel(updateData)
      console.log('更新通道 API 響應:', response)
    } else {
      // 新增通道
      console.log('新增通道 API 請求:', saveData)
      response = await tagsAPI.createTagChannel(saveData)
      console.log('新增通道 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(channelForm.id ? '通道更新成功' : '通道新增成功')
      showAddDialog.value = false

      // 重置表單
      Object.assign(channelForm, {
        id: '',
        name: '',
        driverType: '',
        dataMode: '',
        description: '',
        status: 'active'
      })

      await loadChannelList()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存通道失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存通道失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

/**
 * 載入通道列表
 */
const loadChannelList = async () => {
  try {
    loading.value = true

    // 調用真實API載入通道列表
    const response = await plcDataService.get('/Tag/GetTagChannelListByCustomer', {
      pageIndex: currentPage.value,
      pageSize: pageSize.value
    })

    if (response && response.Detail) {
      // 載入選項列表
      if (response.Detail.TagChannelDriverList) {
        driverOptions.value = response.Detail.TagChannelDriverList
        console.log('載入驅動程式選項:', driverOptions.value)
      }

      if (response.Detail.FetchDataModeList) {
        dataOptions.value = response.Detail.FetchDataModeList
        console.log('載入資料獲取方式選項:', dataOptions.value)
      }

      // 轉換後端數據格式為前端格式
      if (response.Detail.TagChannelList) {
        channelList.value = response.Detail.TagChannelList.map((channel: any) => ({
          id: channel.TagChannelId || channel.Id,
          name: channel.TagChannelName || channel.Name,
          driverType: channel.DriverName || channel.DriverType,
          dataMode: channel.FetchDataModeName || channel.DataMode,
          description: channel.Description,
          status: channel.Status === 1 ? 'active' : 'inactive',
          createTime: channel.CreatedTime || new Date().toISOString(),
          customerName: channel.CustomerName || '',
          driverCode: channel.DriverCode || 0,
          fetchDataModeCode: channel.FetchDataModeCode || 0
        }))

        totalCount.value = channelList.value.length
        ElMessage.success(`成功載入 ${channelList.value.length} 個通道`)
      } else {
        channelList.value = []
        totalCount.value = 0
        ElMessage.warning('未找到通道數據')
      }
    } else {
      channelList.value = []
      totalCount.value = 0
      ElMessage.warning('未找到通道數據')
    }

  } catch (error: any) {
    console.error('載入通道列表失敗:', error)
    ElMessage.error(error.message || '載入通道列表失敗')
  } finally {
    loading.value = false
  }
}

// 生命週期
onMounted(async () => {
  await loadChannelList()
})
</script>

<style scoped>
.channel-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.channel-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.filter-section {
  margin-bottom: 20px;
}

.channel-name {
  display: flex;
  align-items: center;
}

.channel-icon {
  margin-right: 8px;
  color: #409eff;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
