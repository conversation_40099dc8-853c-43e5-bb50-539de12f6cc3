<template>
  <div class="device-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>裝置管理</h2>
      <p>管理 PLC 系統的裝置，包含裝置列表和裝置分類</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="device-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 裝置列表 -->
        <el-tab-pane label="裝置列表" name="list">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>裝置列表</span>
                  <el-button type="primary" @click="showDeviceDialog = true">
                    新增裝置
                  </el-button>
                </div>
              </template>

              <!-- 搜尋和篩選 -->
              <div class="filter-section">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜尋裝置名稱..."
                      clearable
                      @input="handleSearch"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                  </el-col>
                  <el-col :span="6">
                    <el-select
                      v-model="statusFilter"
                      placeholder="狀態篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="啟用" value="active" />
                      <el-option label="停用" value="inactive" />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-select
                      v-model="typeFilter"
                      placeholder="類型篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="PLC" value="PLC" />
                      <el-option label="感測器" value="Sensor" />
                      <el-option label="執行器" value="Actuator" />
                      <el-option label="其他" value="Other" />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="info" @click="refreshDeviceList">
                      <el-icon><Refresh /></el-icon>
                      刷新
                    </el-button>
                  </el-col>
                </el-row>
              </div>

              <!-- 裝置表格 -->
              <el-table
                v-loading="deviceLoading"
                :data="filteredDeviceList"
                stripe
                border
              >
                <el-table-column prop="name" label="裝置名稱" width="200">
                  <template #default="{ row }">
                    <div class="device-name">
                      <el-icon class="device-icon">
                        <Monitor />
                      </el-icon>
                      {{ row.name }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="deviceType" label="裝置類型" width="120">
                  <template #default="{ row }">
                    <el-tag :type="getDeviceTypeColor(row.deviceType)">
                      {{ getDeviceTypeText(row.deviceType) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="description" label="描述" min-width="200" />

                <el-table-column prop="ipAddress" label="IP位址" width="150" />

                <el-table-column prop="port" label="連接埠" width="100" align="right" />

                <el-table-column prop="protocol" label="協議" width="100" />

                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="createTime" label="建立時間" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.createTime) }}
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editDevice(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleDeviceStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteDevice(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分頁 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :total="totalCount"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 裝置分類 -->
        <el-tab-pane label="裝置分類" name="class">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>裝置分類</span>
                  <el-button type="primary" @click="showClassDialog = true">
                    新增分類
                  </el-button>
                </div>
              </template>

              <!-- 分類樹狀結構 -->
              <el-tree
                ref="classTreeRef"
                v-loading="classLoading"
                :data="deviceClassTree"
                :props="treeProps"
                node-key="id"
                default-expand-all
                :expand-on-click-node="false"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <span class="node-label">{{ node.label }}</span>
                    <div class="node-actions">
                      <el-button
                        type="primary"
                        size="small"
                        @click="editClass(data)"
                      >
                        編輯
                      </el-button>
                      <el-button
                        type="success"
                        size="small"
                        @click="addSubClass(data)"
                      >
                        新增子分類
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="deleteClass(data)"
                      >
                        刪除
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-tree>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 新增/編輯裝置對話框 -->
    <el-dialog
      v-model="showDeviceDialog"
      :title="deviceForm.id ? '編輯裝置' : '新增裝置'"
      width="700px"
    >
      <el-form
        ref="deviceFormRef"
        :model="deviceForm"
        :rules="deviceRules"
        label-width="120px"
      >
        <el-form-item label="裝置名稱" prop="name">
          <el-input v-model="deviceForm.name" placeholder="請輸入裝置名稱" />
        </el-form-item>

        <el-form-item label="裝置類型" prop="deviceType">
          <el-select v-model="deviceForm.deviceType" style="width: 100%">
            <el-option label="PLC" value="PLC" />
            <el-option label="感測器" value="Sensor" />
            <el-option label="執行器" value="Actuator" />
            <el-option label="其他" value="Other" />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="deviceForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入裝置描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="IP位址" prop="ipAddress">
              <el-input v-model="deviceForm.ipAddress" placeholder="*************" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="連接埠" prop="port">
              <el-input-number
                v-model="deviceForm.port"
                :min="1"
                :max="65535"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="協議" prop="protocol">
          <el-select v-model="deviceForm.protocol" style="width: 100%">
            <el-option label="Modbus TCP" value="ModbusTCP" />
            <el-option label="Modbus RTU" value="ModbusRTU" />
            <el-option label="OPC UA" value="OPCUA" />
            <el-option label="MQTT" value="MQTT" />
            <el-option label="HTTP" value="HTTP" />
          </el-select>
        </el-form-item>

        <el-form-item label="狀態">
          <el-radio-group v-model="deviceForm.status">
            <el-radio value="active">啟用</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showDeviceDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDevice">確認</el-button>
      </template>
    </el-dialog>

    <!-- 新增/編輯分類對話框 -->
    <el-dialog
      v-model="showClassDialog"
      :title="classForm.id ? '編輯分類' : '新增分類'"
      width="500px"
    >
      <el-form
        ref="classFormRef"
        :model="classForm"
        :rules="classRules"
        label-width="120px"
      >
        <el-form-item label="分類名稱" prop="name">
          <el-input v-model="classForm.name" placeholder="請輸入分類名稱" />
        </el-form-item>

        <el-form-item label="上級分類" prop="parentId">
          <el-tree-select
            v-model="classForm.parentId"
            :data="deviceClassTree"
            :props="treeProps"
            placeholder="請選擇上級分類（可選）"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="classForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入分類描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showClassDialog = false">取消</el-button>
        <el-button type="primary" @click="saveClass">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Monitor 
} from '@element-plus/icons-vue'
import { tagsAPI, type Device } from '@/api/plc/tags'

// 獲取客戶ID函數
const getCustomerId = (): string => {
  // 優先從localStorage獲取
  const storedCustomerId = localStorage.getItem('PLC_CUSTOMER_ID')
  if (storedCustomerId) {
    return storedCustomerId
  }

  // 從token中解析
  const authStore = usePLCAuthStore()
  if (authStore.token) {
    try {
      const payload = JSON.parse(atob(authStore.token.split('.')[1]))
      return payload.CustomerId || 'fdff1878-a54a-44e5-b5c7-123456789abc'
    } catch (error) {
      console.error('解析token失敗:', error)
    }
  }

  // 預設值
  return 'fdff1878-a54a-44e5-b5c7-123456789abc'
}
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// 本地類型定義
interface DeviceItem {
  id: string
  name: string
  type: string
  description?: string
  ipAddress?: string
  port?: number
  protocol?: string
  status: 'active' | 'inactive'
  createTime: string
}

interface DeviceClassItem {
  id: string
  name: string
  parentId?: string
  description?: string
  children?: DeviceClassItem[]
}

// Store
const authStore = usePLCAuthStore()

// 表單引用
const deviceFormRef = ref<InstanceType<typeof ElForm>>()
const classFormRef = ref<InstanceType<typeof ElForm>>()
const classTreeRef = ref()

// 響應式數據
const activeTab = ref('list')
const deviceLoading = ref(false)
const classLoading = ref(false)
const showDeviceDialog = ref(false)
const showClassDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 數據列表
const deviceList = ref<DeviceItem[]>([])
const deviceClassTree = ref<DeviceClassItem[]>([])

// 樹狀結構屬性
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 裝置表單
const deviceForm = reactive({
  id: '',
  name: '',
  deviceType: '',
  description: '',
  ipAddress: '',
  port: 502,
  protocol: '',
  status: 'active'
})

// 分類表單
const classForm = reactive({
  id: '',
  name: '',
  parentId: '',
  description: ''
})

// 表單驗證規則
const deviceRules = {
  name: [
    { required: true, message: '請輸入裝置名稱', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '請選擇裝置類型', trigger: 'change' }
  ],
  ipAddress: [
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '請輸入正確的IP位址格式', trigger: 'blur' }
  ],
  port: [
    { type: 'number', min: 1, max: 65535, message: '連接埠範圍為1-65535', trigger: 'blur' }
  ]
}

const classRules = {
  name: [
    { required: true, message: '請輸入分類名稱', trigger: 'blur' }
  ]
}

// 計算屬性
const filteredDeviceList = computed(() => {
  let filtered = deviceList.value

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }

  // 類型篩選
  if (typeFilter.value) {
    filtered = filtered.filter(item => item.deviceType === typeFilter.value)
  }

  return filtered
})

/**
 * 獲取裝置類型顏色
 */
const getDeviceTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    PLC: 'primary',
    Sensor: 'success',
    Actuator: 'warning',
    Other: 'info'
  }
  return colorMap[type] || 'default'
}

/**
 * 獲取裝置類型文字
 */
const getDeviceTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    PLC: 'PLC',
    Sensor: '感測器',
    Actuator: '執行器',
    Other: '其他'
  }
  return textMap[type] || type
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 搜尋處理
 */
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中處理
}

/**
 * 篩選處理
 */
const handleFilter = () => {
  // 篩選邏輯已在計算屬性中處理
}

/**
 * 分頁大小變更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadDeviceList()
}

/**
 * 當前頁變更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadDeviceList()
}

/**
 * 刷新裝置列表
 */
const refreshDeviceList = () => {
  loadDeviceList()
}

/**
 * 編輯裝置
 */
const editDevice = (row: DeviceItem) => {
  Object.assign(deviceForm, row)
  showDeviceDialog.value = true
}

/**
 * 切換裝置狀態
 */
const toggleDeviceStatus = async (row: DeviceItem) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    const statusText = newStatus === 'active' ? '啟用' : '停用'

    await ElMessageBox.confirm(
      `確定要${statusText}裝置 "${row.name}" 嗎？`,
      '狀態變更確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 調用API更新狀態
    row.status = newStatus
    ElMessage.success(`裝置${statusText}成功`)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('切換裝置狀態失敗:', error)
      ElMessage.error('切換裝置狀態失敗')
    }
  }
}

/**
 * 刪除裝置
 */
const deleteDevice = async (row: DeviceItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除裝置 "${row.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除裝置
    const deleteData = {
      DeviceId: row.id
    }

    console.log('刪除裝置 API 請求:', deleteData)
    const response = await tagsAPI.deleteDevice(deleteData)
    console.log('刪除裝置 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('裝置刪除成功')
      await loadDeviceList()
    } else {
      const errorMessage = response?.Message || response?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除裝置失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除裝置失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存裝置
 */
const saveDevice = async () => {
  if (!deviceFormRef.value) return

  try {
    await deviceFormRef.value.validate()

    // TODO: 調用API保存裝置
    ElMessage.success('裝置保存成功')
    showDeviceDialog.value = false

    // 重置表單
    Object.assign(deviceForm, {
      id: '',
      name: '',
      deviceType: '',
      description: '',
      ipAddress: '',
      port: 502,
      protocol: '',
      status: 'active'
    })

    await loadDeviceList()

  } catch (error: any) {
    console.error('保存裝置失敗:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

/**
 * 編輯分類
 */
const editClass = (data: DeviceClassItem) => {
  Object.assign(classForm, data)
  showClassDialog.value = true
}

/**
 * 新增子分類
 */
const addSubClass = (data: DeviceClassItem) => {
  Object.assign(classForm, {
    id: '',
    name: '',
    parentId: data.id,
    description: ''
  })
  showClassDialog.value = true
}

/**
 * 刪除分類
 */
const deleteClass = async (data: DeviceClassItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除分類 "${data.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除分類
    const deleteData = {
      id: data.id,
      customerId: getCustomerId()
    }

    console.log('刪除分類 API 請求:', deleteData)
    const response = await tagsAPI.deleteDeviceCategory(deleteData)
    console.log('刪除分類 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('分類刪除成功')
      await loadDeviceClassTree()
    } else {
      throw new Error(response?.Message || '刪除失敗')
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除分類失敗:', error)
      ElMessage.error(error.message || '刪除分類失敗')
    }
  }
}

/**
 * 保存分類
 */
const saveClass = async () => {
  if (!classFormRef.value) return

  try {
    await classFormRef.value.validate()

    // TODO: 調用API保存分類
    ElMessage.success('分類保存成功')
    showClassDialog.value = false

    // 重置表單
    Object.assign(classForm, {
      id: '',
      name: '',
      parentId: '',
      description: ''
    })

    await loadDeviceClassTree()

  } catch (error: any) {
    console.error('保存分類失敗:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

/**
 * 載入裝置列表
 */
const loadDeviceList = async () => {
  try {
    deviceLoading.value = true

    // 調用真實API載入裝置列表
    const response = await tagsAPI.getDevices()
    console.log('API 響應:', response)

    if (response && response.Detail && response.Detail.DeviceList) {
      // 轉換後端數據格式為前端格式
      // 根據 GetDeviceListByCustomerRespDetailDevice 結構
      deviceList.value = response.Detail.DeviceList.map((device: any) => ({
        id: device.DeviceId,
        name: device.DeviceName,
        type: device.TagChannel?.TypeName || 'PLC',
        description: device.Description,
        ipAddress: device.Ip,
        port: device.Port,
        protocol: device.TagChannel?.TypeName || '',
        status: device.Status === 1 ? 'active' : 'inactive',
        createTime: new Date().toISOString()
      }))

      totalCount.value = deviceList.value.length
      ElMessage.success(`成功載入 ${deviceList.value.length} 個裝置`)
    } else if (response && response.Detail && response.Detail.DeviceList && response.Detail.DeviceList.length === 0) {
      deviceList.value = []
      totalCount.value = 0
      ElMessage.info('目前沒有裝置數據')
    } else {
      deviceList.value = []
      totalCount.value = 0
      ElMessage.warning('未找到裝置數據')
    }

  } catch (error: any) {
    console.error('載入裝置列表失敗:', error)
    ElMessage.error(error.message || '載入裝置列表失敗')
  } finally {
    deviceLoading.value = false
  }
}

/**
 * 載入裝置分類樹
 */
const loadDeviceClassTree = async () => {
  try {
    classLoading.value = true

    // 調用真實API載入分類樹
    const response = await tagsAPI.getDeviceCategoryHierarchy()
    console.log('設備分類 API 響應:', response)

    if (response && response.Detail && response.Detail.DeviceCategoryHierarchyList) {
      // 轉換後端數據格式為前端格式
      const transformCategory = (category: any): DeviceClassItem => ({
        id: category.Id || category.id,
        name: category.Name || category.name || '未命名分類',
        description: category.Description || category.description || '',
        children: category.ChildList ? category.ChildList.map(transformCategory) : []
      })

      deviceClassTree.value = response.Detail.DeviceCategoryHierarchyList.map(transformCategory)
    } else {
      // 如果沒有分類數據，顯示預設分類
      deviceClassTree.value = [
        {
          id: 'default',
          name: '預設裝置分類',
          description: '系統預設分類',
          children: []
        }
      ]
    }

  } catch (error: any) {
    console.error('載入分類樹失敗:', error)
    ElMessage.error(error.message || '載入分類樹失敗')

    // 錯誤時顯示預設分類
    deviceClassTree.value = [
      {
        id: 'default',
        name: '預設裝置分類',
        description: '系統預設分類',
        children: []
      }
    ]
  } finally {
    classLoading.value = false
  }
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadDeviceList(),
    loadDeviceClassTree()
  ])
})
</script>

<style scoped>
.device-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.device-card {
  margin-bottom: 20px;
}

.tab-content {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.filter-section {
  margin-bottom: 20px;
}

.device-name {
  display: flex;
  align-items: center;
}

.device-icon {
  margin-right: 8px;
  color: #409eff;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-actions {
  display: flex;
  gap: 8px;
}

.node-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tree-node__content) {
  height: 40px;
}
</style>
