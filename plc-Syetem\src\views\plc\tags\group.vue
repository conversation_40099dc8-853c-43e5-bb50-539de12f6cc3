<template>
  <div class="group-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>群組管理</h2>
      <p>管理測點群組，包含群組列表和群組分類</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="group-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 群組列表 -->
        <el-tab-pane label="群組列表" name="list">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>群組列表</span>
                  <el-button type="primary" @click="showGroupDialog = true">
                    新增群組
                  </el-button>
                </div>
              </template>

              <!-- 搜尋和篩選 -->
              <div class="filter-section">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜尋群組名稱..."
                      clearable
                      @input="handleSearch"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                  </el-col>
                  <el-col :span="6">
                    <el-select
                      v-model="statusFilter"
                      placeholder="狀態篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="啟用" value="active" />
                      <el-option label="停用" value="inactive" />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-select
                      v-model="classFilter"
                      placeholder="分類篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option
                        v-for="cls in groupClassOptions"
                        :key="cls.id"
                        :label="cls.name"
                        :value="cls.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="info" @click="refreshGroupList">
                      <el-icon><Refresh /></el-icon>
                      刷新
                    </el-button>
                  </el-col>
                </el-row>
              </div>

              <!-- 群組表格 -->
              <el-table
                v-loading="groupLoading"
                :data="filteredGroupList"
                stripe
                border
              >
                <el-table-column prop="name" label="群組名稱" width="200">
                  <template #default="{ row }">
                    <div class="group-name">
                      <el-icon class="group-icon">
                        <Collection />
                      </el-icon>
                      {{ row.name }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="className" label="群組分類" width="150">
                  <template #default="{ row }">
                    <el-tag v-if="row.className">{{ row.className }}</el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="regionName" label="地區" width="150">
                  <template #default="{ row }">
                    <el-tag v-if="row.regionName" type="info">{{ row.regionName }}</el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="tagCount" label="測點數量" width="100" align="right">
                  <template #default="{ row }">
                    <el-tag type="success">{{ row.tagCount || 0 }}</el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="description" label="說明" min-width="200" />

                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="createTime" label="建立時間" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.createTime) }}
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editGroup(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleGroupStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteGroup(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分頁 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :total="totalCount"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 群組分類 -->
        <el-tab-pane label="群組分類" name="class">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>群組分類</span>
                  <el-button type="primary" @click="showClassDialog = true">
                    新增分類
                  </el-button>
                </div>
              </template>

              <!-- 分類樹狀結構 -->
              <el-tree
                ref="classTreeRef"
                v-loading="classLoading"
                :data="groupClassTree"
                :props="treeProps"
                node-key="id"
                default-expand-all
                :expand-on-click-node="false"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <span class="node-label">{{ node.label }}</span>
                    <div class="node-actions">
                      <el-button
                        type="primary"
                        size="small"
                        @click="editClass(data)"
                      >
                        編輯
                      </el-button>
                      <el-button
                        type="success"
                        size="small"
                        @click="addSubClass(data)"
                      >
                        新增子分類
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="deleteClass(data)"
                      >
                        刪除
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-tree>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 新增/編輯群組對話框 -->
    <el-dialog
      v-model="showGroupDialog"
      :title="groupForm.id ? '編輯群組' : '新增群組'"
      width="800px"
    >
      <el-tabs v-model="groupDialogTab" type="border-card">
        <!-- 群組設定 -->
        <el-tab-pane label="群組設定" name="setting">
          <el-form
            ref="groupFormRef"
            :model="groupForm"
            :rules="groupRules"
            label-width="120px"
          >
            <el-form-item label="群組名稱" prop="name">
              <el-input v-model="groupForm.name" placeholder="請輸入群組名稱" />
            </el-form-item>

            <el-form-item label="群組分類" prop="classId">
              <el-tree-select
                v-model="groupForm.classId"
                :data="groupClassTree"
                :props="treeProps"
                placeholder="請選擇群組分類"
                clearable
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="地區" prop="regionId">
              <el-tree-select
                v-model="groupForm.regionId"
                :data="regionTree"
                :props="treeProps"
                placeholder="請選擇地區"
                clearable
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="說明">
              <el-input
                v-model="groupForm.description"
                type="textarea"
                :rows="3"
                placeholder="請輸入群組說明"
              />
            </el-form-item>

            <el-form-item label="狀態">
              <el-radio-group v-model="groupForm.status">
                <el-radio value="active">啟用</el-radio>
                <el-radio value="inactive">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 內容設定 -->
        <el-tab-pane label="內容設定" name="content">
          <div class="content-setting">
            <div class="section-title">測點選擇</div>
            <div class="tag-selection">
              <!-- 可用測點列表 -->
              <div class="available-tags">
                <div class="list-header">
                  <span>可用測點</span>
                  <el-input
                    v-model="availableTagSearch"
                    placeholder="搜尋測點..."
                    size="small"
                    style="width: 200px"
                  />
                </div>
                <el-table
                  :data="filteredAvailableTags"
                  height="300"
                  @selection-change="handleAvailableTagSelection"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" label="測點名稱" />
                  <el-table-column prop="description" label="描述" />
                </el-table>
              </div>

              <!-- 操作按鈕 -->
              <div class="operation-buttons">
                <el-button
                  type="primary"
                  @click="addSelectedTags"
                  :disabled="selectedAvailableTags.length === 0"
                >
                  添加 →
                </el-button>
                <el-button
                  type="warning"
                  @click="removeSelectedTags"
                  :disabled="selectedGroupTags.length === 0"
                >
                  ← 移除
                </el-button>
              </div>

              <!-- 已選測點列表 -->
              <div class="selected-tags">
                <div class="list-header">
                  <span>已選測點 ({{ groupForm.tags.length }})</span>
                </div>
                <el-table
                  :data="groupForm.tags"
                  height="300"
                  @selection-change="handleGroupTagSelection"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" label="測點名稱" />
                  <el-table-column prop="description" label="描述" />
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <el-button @click="showGroupDialog = false">取消</el-button>
        <el-button type="primary" @click="saveGroup">確認</el-button>
      </template>
    </el-dialog>

    <!-- 新增/編輯分類對話框 -->
    <el-dialog
      v-model="showClassDialog"
      :title="classForm.id ? '編輯分類' : '新增分類'"
      width="500px"
    >
      <el-form
        ref="classFormRef"
        :model="classForm"
        :rules="classRules"
        label-width="120px"
      >
        <el-form-item label="分類名稱" prop="name">
          <el-input v-model="classForm.name" placeholder="請輸入分類名稱" />
        </el-form-item>

        <el-form-item label="上級分類" prop="parentId">
          <el-tree-select
            v-model="classForm.parentId"
            :data="groupClassTree"
            :props="treeProps"
            placeholder="請選擇上級分類（可選）"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="classForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入分類描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showClassDialog = false">取消</el-button>
        <el-button type="primary" @click="saveClass">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Collection 
} from '@element-plus/icons-vue'
import { groupAPI, type GroupItem, type GroupClassItem, type TagItem } from '@/api/plc/tags'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const groupFormRef = ref<InstanceType<typeof ElForm>>()
const classFormRef = ref<InstanceType<typeof ElForm>>()
const classTreeRef = ref()

// 響應式數據
const activeTab = ref('list')
const groupDialogTab = ref('setting')
const groupLoading = ref(false)
const classLoading = ref(false)
const showGroupDialog = ref(false)
const showClassDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const classFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 測點選擇相關
const availableTagSearch = ref('')
const selectedAvailableTags = ref<TagItem[]>([])
const selectedGroupTags = ref<TagItem[]>([])

// 數據列表
const groupList = ref<GroupItem[]>([])
const groupClassTree = ref<GroupClassItem[]>([])
const groupClassOptions = ref<GroupClassItem[]>([])
const regionTree = ref<any[]>([])
const availableTags = ref<TagItem[]>([])

// 樹狀結構屬性
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 群組表單
const groupForm = reactive({
  id: '',
  name: '',
  classId: '',
  className: '',
  regionId: '',
  regionName: '',
  description: '',
  status: 'active',
  tags: [] as TagItem[]
})

// 分類表單
const classForm = reactive({
  id: '',
  name: '',
  parentId: '',
  description: ''
})

// 表單驗證規則
const groupRules = {
  name: [
    { required: true, message: '請輸入群組名稱', trigger: 'blur' }
  ]
}

const classRules = {
  name: [
    { required: true, message: '請輸入分類名稱', trigger: 'blur' }
  ]
}

// 計算屬性
const filteredGroupList = computed(() => {
  let filtered = groupList.value

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }

  // 分類篩選
  if (classFilter.value) {
    filtered = filtered.filter(item => item.classId === classFilter.value)
  }

  return filtered
})

const filteredAvailableTags = computed(() => {
  let filtered = availableTags.value

  // 排除已選擇的測點
  const selectedTagIds = groupForm.tags.map(tag => tag.id)
  filtered = filtered.filter(tag => !selectedTagIds.includes(tag.id))

  // 搜尋篩選
  if (availableTagSearch.value) {
    filtered = filtered.filter(tag =>
      tag.name.toLowerCase().includes(availableTagSearch.value.toLowerCase()) ||
      tag.description?.toLowerCase().includes(availableTagSearch.value.toLowerCase())
    )
  }

  return filtered
})

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 搜尋處理
 */
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中處理
}

/**
 * 篩選處理
 */
const handleFilter = () => {
  // 篩選邏輯已在計算屬性中處理
}

/**
 * 分頁大小變更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadGroupList()
}

/**
 * 當前頁變更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadGroupList()
}

/**
 * 刷新群組列表
 */
const refreshGroupList = () => {
  loadGroupList()
}

/**
 * 編輯群組
 */
const editGroup = (row: GroupItem) => {
  Object.assign(groupForm, row)
  showGroupDialog.value = true
}

/**
 * 切換群組狀態
 */
const toggleGroupStatus = async (row: GroupItem) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    const statusText = newStatus === 'active' ? '啟用' : '停用'

    await ElMessageBox.confirm(
      `確定要${statusText}群組 "${row.name}" 嗎？`,
      '狀態變更確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 注意：後端資料庫 Group 表沒有 Status 欄位，這是前端 UI 狀態模擬
    // 如果未來後端支持群組狀態，可以調用相應的 API
    console.log('群組狀態切換 (前端模擬):', {
      groupId: row.id,
      groupName: row.name,
      oldStatus: row.status,
      newStatus: newStatus,
      note: '後端 Group 表無 Status 欄位，此為前端 UI 狀態模擬'
    })

    row.status = newStatus
    ElMessage.success(`群組${statusText}成功 (前端狀態更新)`, {
      duration: 5000,
      showClose: true
    })

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('切換群組狀態失敗:', error)

      // 提取錯誤訊息
      let errorMessage = '切換群組狀態失敗'
      if (error?.response?.data?.Message) {
        errorMessage += `: ${error.response.data.Message}`
      } else if (error?.response?.data?.message) {
        errorMessage += `: ${error.response.data.message}`
      } else if (error?.message) {
        errorMessage += `: ${error.message}`
      } else if (typeof error === 'string') {
        errorMessage += `: ${error}`
      }

      ElMessage.error(errorMessage, {
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 刪除群組
 */
const deleteGroup = async (row: GroupItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除群組 "${row.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除群組
    const deleteData = {
      GroupId: row.id
    }

    console.log('刪除群組 API 請求:', deleteData)
    const response = await tagsAPI.deleteGroup(deleteData)
    console.log('刪除群組 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('群組刪除成功')
      await loadGroupList()
    } else {
      const errorMessage = response?.Message || response?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除群組失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除群組失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 處理可用測點選擇
 */
const handleAvailableTagSelection = (selection: TagItem[]) => {
  selectedAvailableTags.value = selection
}

/**
 * 處理群組測點選擇
 */
const handleGroupTagSelection = (selection: TagItem[]) => {
  selectedGroupTags.value = selection
}

/**
 * 添加選中的測點
 */
const addSelectedTags = () => {
  groupForm.tags.push(...selectedAvailableTags.value)
  selectedAvailableTags.value = []
}

/**
 * 移除選中的測點
 */
const removeSelectedTags = () => {
  const removeIds = selectedGroupTags.value.map(tag => tag.id)
  groupForm.tags = groupForm.tags.filter(tag => !removeIds.includes(tag.id))
  selectedGroupTags.value = []
}

/**
 * 保存群組
 */
const saveGroup = async () => {
  if (!groupFormRef.value) return

  try {
    await groupFormRef.value.validate()

    // 調用API保存群組
    const saveData = {
      Name: groupForm.name,
      GroupCategoryId: groupForm.classId || undefined,
      Description: groupForm.description || undefined,
      TagIds: groupForm.tags?.map((tag: any) => tag.id) || []
    }

    let response
    if (groupForm.id) {
      // 更新群組
      const updateData = {
        Id: groupForm.id,
        ...saveData
      }
      console.log('更新群組 API 請求:', updateData)
      response = await tagsAPI.updateGroup(updateData)
      console.log('更新群組 API 響應:', response)
    } else {
      // 新增群組
      console.log('新增群組 API 請求:', saveData)
      response = await tagsAPI.createGroup(saveData)
      console.log('新增群組 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(groupForm.id ? '群組更新成功' : '群組新增成功')
      showGroupDialog.value = false

      // 重置表單
      Object.assign(groupForm, {
        id: '',
        name: '',
        classId: '',
        className: '',
        regionId: '',
        regionName: '',
        description: '',
        status: 'active',
        tags: []
      })

      await loadGroupList()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存群組失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存群組失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

/**
 * 編輯分類
 */
const editClass = (data: GroupClassItem) => {
  Object.assign(classForm, data)
  showClassDialog.value = true
}

/**
 * 新增子分類
 */
const addSubClass = (data: GroupClassItem) => {
  Object.assign(classForm, {
    id: '',
    name: '',
    parentId: data.id,
    description: ''
  })
  showClassDialog.value = true
}

/**
 * 刪除分類
 */
const deleteClass = async (data: GroupClassItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除分類 "${data.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 調用API刪除分類
    ElMessage.success('分類刪除成功')
    await loadGroupClassTree()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('刪除分類失敗:', error)
      ElMessage.error('刪除分類失敗')
    }
  }
}

/**
 * 保存分類
 */
const saveClass = async () => {
  if (!classFormRef.value) return

  try {
    await classFormRef.value.validate()

    // 調用API保存分類
    const saveData = {
      Name: classForm.name,
      ParentId: classForm.parentId || undefined,
      Description: classForm.description || undefined
    }

    let response
    if (classForm.id) {
      // 更新群組分類
      const updateData = {
        Id: classForm.id,
        ...saveData
      }
      console.log('更新群組分類 API 請求:', updateData)
      response = await tagsAPI.updateGroupCategory(updateData)
      console.log('更新群組分類 API 響應:', response)
    } else {
      // 新增群組分類
      console.log('新增群組分類 API 請求:', saveData)
      response = await tagsAPI.createGroupCategory(saveData)
      console.log('新增群組分類 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(classForm.id ? '分類更新成功' : '分類新增成功')
      showClassDialog.value = false

      // 重置表單
      Object.assign(classForm, {
        id: '',
        name: '',
        parentId: '',
        description: ''
      })

      await loadGroupClassTree()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存分類失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存分類失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

/**
 * 載入群組列表
 */
const loadGroupList = async () => {
  try {
    groupLoading.value = true

    // TODO: 調用API載入群組列表
    // 模擬數據
    groupList.value = [
      {
        id: '1',
        name: '電錶群組',
        classId: '1',
        className: '電力系統',
        regionId: '1',
        regionName: '一樓',
        description: '一樓電錶監控群組',
        status: 'active',
        tagCount: 15,
        tags: [],
        createTime: new Date().toISOString()
      },
      {
        id: '2',
        name: '消防群組',
        classId: '2',
        className: '消防系統',
        regionId: '2',
        regionName: '二樓',
        description: '二樓消防設備監控群組',
        status: 'active',
        tagCount: 8,
        tags: [],
        createTime: new Date().toISOString()
      }
    ]

    totalCount.value = groupList.value.length

  } catch (error: any) {
    console.error('載入群組列表失敗:', error)
    ElMessage.error(error.message || '載入群組列表失敗')
  } finally {
    groupLoading.value = false
  }
}

/**
 * 載入群組分類樹
 */
const loadGroupClassTree = async () => {
  try {
    classLoading.value = true

    // TODO: 調用API載入分類樹
    // 模擬數據
    const classData = [
      {
        id: '1',
        name: '電力系統',
        description: '電力相關設備',
        children: [
          {
            id: '11',
            name: '電錶',
            description: '電錶設備',
            children: []
          },
          {
            id: '12',
            name: '配電盤',
            description: '配電設備',
            children: []
          }
        ]
      },
      {
        id: '2',
        name: '消防系統',
        description: '消防相關設備',
        children: [
          {
            id: '21',
            name: '煙霧偵測器',
            description: '煙霧偵測設備',
            children: []
          },
          {
            id: '22',
            name: '灑水系統',
            description: '自動灑水設備',
            children: []
          }
        ]
      }
    ]

    groupClassTree.value = classData

    // 扁平化選項用於篩選
    const flattenOptions = (items: GroupClassItem[]): GroupClassItem[] => {
      let result: GroupClassItem[] = []
      items.forEach(item => {
        result.push(item)
        if (item.children && item.children.length > 0) {
          result = result.concat(flattenOptions(item.children))
        }
      })
      return result
    }

    groupClassOptions.value = flattenOptions(classData)

  } catch (error: any) {
    console.error('載入分類樹失敗:', error)
    ElMessage.error(error.message || '載入分類樹失敗')
  } finally {
    classLoading.value = false
  }
}

/**
 * 載入地區樹
 */
const loadRegionTree = async () => {
  try {
    // TODO: 調用API載入地區樹
    // 模擬數據
    regionTree.value = [
      {
        id: '1',
        name: '一樓',
        children: [
          { id: '11', name: '大廳', children: [] },
          { id: '12', name: '辦公室', children: [] }
        ]
      },
      {
        id: '2',
        name: '二樓',
        children: [
          { id: '21', name: '會議室', children: [] },
          { id: '22', name: '休息室', children: [] }
        ]
      }
    ]

  } catch (error: any) {
    console.error('載入地區樹失敗:', error)
    ElMessage.error(error.message || '載入地區樹失敗')
  }
}

/**
 * 載入可用測點
 */
const loadAvailableTags = async () => {
  try {
    // TODO: 調用API載入測點列表
    // 模擬數據
    availableTags.value = [
      {
        id: '1',
        name: 'TAG001',
        description: '溫度感測器1',
        tagType: 'Analog',
        dataType: 'Float',
        unit: '°C'
      },
      {
        id: '2',
        name: 'TAG002',
        description: '濕度感測器1',
        tagType: 'Analog',
        dataType: 'Float',
        unit: '%'
      },
      {
        id: '3',
        name: 'TAG003',
        description: '開關狀態1',
        tagType: 'Digital',
        dataType: 'Boolean',
        unit: ''
      }
    ]

  } catch (error: any) {
    console.error('載入測點列表失敗:', error)
    ElMessage.error(error.message || '載入測點列表失敗')
  }
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadGroupList(),
    loadGroupClassTree(),
    loadRegionTree(),
    loadAvailableTags()
  ])
})
</script>

<style scoped>
.group-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.group-card {
  margin-bottom: 20px;
}

.tab-content {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.filter-section {
  margin-bottom: 20px;
}

.group-name {
  display: flex;
  align-items: center;
}

.group-icon {
  margin-right: 8px;
  color: #409eff;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-actions {
  display: flex;
  gap: 8px;
}

.node-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.content-setting {
  padding: 20px 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #303133;
}

.tag-selection {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.available-tags,
.selected-tags {
  flex: 1;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: bold;
  color: #303133;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
