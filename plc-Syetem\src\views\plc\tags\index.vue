<template>
  <div class="tags-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h1>測點管理系統</h1>
      <p>管理 PLC 系統的測點、裝置、通道和群組，提供完整的測點組織架構</p>
    </div>

    <!-- 系統統計概覽 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon channel-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.channelCount }}</div>
              <div class="stats-label">通道數量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon device-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.deviceCount }}</div>
              <div class="stats-label">裝置數量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon tag-icon">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.tagCount }}</div>
              <div class="stats-label">測點數量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon group-icon">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ systemStats.groupCount }}</div>
              <div class="stats-label">群組數量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能模組導航 -->
    <div class="modules-section">
      <h2>功能模組</h2>
      <el-row :gutter="20">
        <el-col :span="12" :lg="8">
          <el-card
            class="module-card"
            shadow="hover"
            @click="navigateToModule('channel')"
          >
            <div class="module-content">
              <div class="module-icon channel-bg">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="module-info">
                <h3>通道管理</h3>
                <p>管理通訊通道，支援 TCP、OBIX、Desigo CC 等多種驅動程式</p>
                <div class="module-stats">
                  <span>{{ systemStats.channelCount }} 個通道</span>
                  <span>{{ systemStats.activeChannelCount }} 個啟用</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12" :lg="8">
          <el-card
            class="module-card"
            shadow="hover"
            @click="navigateToModule('device')"
          >
            <div class="module-content">
              <div class="module-icon device-bg">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="module-info">
                <h3>裝置管理</h3>
                <p>管理 PLC 裝置、感測器、執行器等設備，支援裝置分類</p>
                <div class="module-stats">
                  <span>{{ systemStats.deviceCount }} 個裝置</span>
                  <span>{{ systemStats.activeDeviceCount }} 個線上</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12" :lg="8">
          <el-card
            class="module-card"
            shadow="hover"
            @click="navigateToModule('tag')"
          >
            <div class="module-content">
              <div class="module-icon tag-bg">
                <el-icon><Cpu /></el-icon>
              </div>
              <div class="module-info">
                <h3>測點管理</h3>
                <p>管理測點資料，包含類比、數位測點，支援警報設定</p>
                <div class="module-stats">
                  <span>{{ systemStats.tagCount }} 個測點</span>
                  <span>{{ systemStats.alarmTagCount }} 個警報</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12" :lg="8">
          <el-card
            class="module-card"
            shadow="hover"
            @click="navigateToModule('group')"
          >
            <div class="module-content">
              <div class="module-icon group-bg">
                <el-icon><Collection /></el-icon>
              </div>
              <div class="module-info">
                <h3>群組管理</h3>
                <p>管理測點群組，支援群組分類和地區組織</p>
                <div class="module-stats">
                  <span>{{ systemStats.groupCount }} 個群組</span>
                  <span>{{ systemStats.groupTagCount }} 個測點</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12" :lg="8">
          <el-card
            class="module-card"
            shadow="hover"
            @click="navigateToModule('region')"
          >
            <div class="module-content">
              <div class="module-icon region-bg">
                <el-icon><Location /></el-icon>
              </div>
              <div class="module-info">
                <h3>地區管理</h3>
                <p>管理系統地區結構，支援階層式地區組織</p>
                <div class="module-stats">
                  <span>{{ systemStats.regionCount }} 個地區</span>
                  <span>階層式管理</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2>快速操作</h2>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="action-card">
            <div class="action-content">
              <el-icon class="action-icon"><Plus /></el-icon>
              <h4>新增測點</h4>
              <p>快速新增新的測點</p>
              <el-button type="primary" @click="quickAddTag">立即新增</el-button>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="action-card">
            <div class="action-content">
              <el-icon class="action-icon"><View /></el-icon>
              <h4>即時監控</h4>
              <p>查看測點即時數據</p>
              <el-button type="success" @click="viewRealtime">開始監控</el-button>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="action-card">
            <div class="action-content">
              <el-icon class="action-icon"><Download /></el-icon>
              <h4>匯出資料</h4>
              <p>匯出測點配置資料</p>
              <el-button type="warning" @click="exportData">開始匯出</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 系統狀態 -->
    <div class="system-status">
      <h2>系統狀態</h2>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>通訊狀態</span>
            </template>
            <div class="status-list">
              <div class="status-item">
                <span class="status-label">TCP 通道</span>
                <el-tag type="success">正常</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">OBIX 通道</span>
                <el-tag type="success">正常</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">Desigo CC</span>
                <el-tag type="warning">警告</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>警報狀態</span>
            </template>
            <div class="status-list">
              <div class="status-item">
                <span class="status-label">高溫警報</span>
                <el-tag type="danger">{{ systemStats.highTempAlarms }} 個</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">低溫警報</span>
                <el-tag type="warning">{{ systemStats.lowTempAlarms }} 個</el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">設備離線</span>
                <el-tag type="info">{{ systemStats.offlineDevices }} 個</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>



    <!-- 標籤編輯對話框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="editMode === 'create' ? '新增標籤' : '編輯標籤'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="標籤名稱" prop="TagName">
              <el-input v-model="editForm.TagName" placeholder="請輸入標籤名稱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="標籤類型" prop="TagType">
              <el-select v-model="editForm.TagType" placeholder="請選擇標籤類型" style="width: 100%">
                <el-option
                  v-for="type in tagTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="資料類型" prop="DataType">
              <el-select v-model="editForm.DataType" placeholder="請選擇資料類型" style="width: 100%">
                <el-option
                  v-for="type in dataTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="位址" prop="Address">
              <el-input v-model="editForm.Address" placeholder="請輸入位址" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="設備">
              <el-select v-model="editForm.DeviceId" placeholder="請選擇設備" clearable style="width: 100%">
                <el-option
                  v-for="device in devices"
                  :key="device.DeviceId"
                  :label="device.DeviceName"
                  :value="device.DeviceId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="群組">
              <el-select v-model="editForm.GroupId" placeholder="請選擇群組" clearable style="width: 100%">
                <el-option
                  v-for="group in groups"
                  :key="group.GroupId"
                  :label="group.GroupName"
                  :value="group.GroupId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input
            v-model="editForm.TagDescription"
            type="textarea"
            :rows="3"
            placeholder="請輸入標籤描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="單位">
              <el-input v-model="editForm.Unit" placeholder="請輸入單位" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="掃描頻率">
              <el-input-number
                v-model="editForm.ScanRate"
                :min="100"
                :max="60000"
                :step="100"
                placeholder="毫秒"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="預設值">
              <el-input-number
                v-model="editForm.DefaultValue"
                placeholder="預設值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最小值">
              <el-input-number
                v-model="editForm.MinValue"
                placeholder="最小值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大值">
              <el-input-number
                v-model="editForm.MaxValue"
                placeholder="最大值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="狀態">
              <el-switch
                v-model="editForm.IsActive"
                active-text="啟用"
                inactive-text="停用"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="唯讀">
              <el-switch
                v-model="editForm.IsReadOnly"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="啟用警報">
              <el-switch
                v-model="editForm.AlarmEnabled"
                active-text="啟用"
                inactive-text="停用"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <div v-if="editForm.AlarmEnabled">
          <el-divider content-position="left">警報設定</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="高警報限值">
                <el-input-number
                  v-model="editForm.HighAlarmLimit"
                  placeholder="高警報限值"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="低警報限值">
                <el-input-number
                  v-model="editForm.LowAlarmLimit"
                  placeholder="低警報限值"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="高警告限值">
                <el-input-number
                  v-model="editForm.HighWarningLimit"
                  placeholder="高警告限值"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="低警告限值">
                <el-input-number
                  v-model="editForm.LowWarningLimit"
                  placeholder="低警告限值"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveTag" :loading="saving">
            {{ editMode === 'create' ? '新增' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 標籤詳情對話框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="標籤詳情"
      width="600px"
    >
      <el-descriptions v-if="selectedTag" :column="2" border>
        <el-descriptions-item label="標籤名稱">{{ selectedTag.TagName }}</el-descriptions-item>
        <el-descriptions-item label="標籤類型">{{ selectedTag.TagType }}</el-descriptions-item>
        <el-descriptions-item label="資料類型">{{ selectedTag.DataType }}</el-descriptions-item>
        <el-descriptions-item label="位址">{{ selectedTag.Address }}</el-descriptions-item>
        <el-descriptions-item label="設備">{{ selectedTag.DeviceName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="群組">{{ selectedTag.GroupName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="單位">{{ selectedTag.Unit || '-' }}</el-descriptions-item>
        <el-descriptions-item label="掃描頻率">{{ selectedTag.ScanRate || '-' }} ms</el-descriptions-item>
        <el-descriptions-item label="狀態">
          <el-tag :type="selectedTag.IsActive ? 'success' : 'danger'">
            {{ selectedTag.IsActive ? '啟用' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="唯讀">
          <el-tag :type="selectedTag.IsReadOnly ? 'warning' : 'info'">
            {{ selectedTag.IsReadOnly ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="警報啟用">
          <el-tag :type="selectedTag.AlarmEnabled ? 'success' : 'info'">
            {{ selectedTag.AlarmEnabled ? '啟用' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="創建時間" :span="2">
          {{ formatDateTime(selectedTag.CreatedTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ selectedTag.TagDescription || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 即時值對話框 -->
    <el-dialog
      v-model="realTimeValueDialogVisible"
      title="標籤即時值"
      width="500px"
    >
      <div v-if="realTimeValue" class="real-time-value">
        <el-card>
          <div class="value-display">
            <div class="tag-name">{{ realTimeValue.TagName }}</div>
            <div class="current-value">
              <span class="value">{{ realTimeValue.Value }}</span>
              <span class="unit">{{ realTimeValue.Unit || '' }}</span>
            </div>
            <div class="quality">
              <el-tag :type="getQualityTagType(realTimeValue.Quality)">
                {{ realTimeValue.Quality }}
              </el-tag>
            </div>
            <div class="timestamp">
              更新時間: {{ formatDateTime(realTimeValue.Timestamp) }}
            </div>
          </div>
        </el-card>

        <div v-if="!selectedTag?.IsReadOnly" class="write-value">
          <el-divider content-position="left">寫入值</el-divider>
          <el-form :inline="true">
            <el-form-item>
              <el-input-number
                v-model="writeValue"
                placeholder="請輸入要寫入的值"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleWriteValue" :loading="writing">
                寫入
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div v-else class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
    </el-dialog>

    <!-- 匯入對話框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="匯入標籤"
      width="500px"
    >
      <el-form label-width="120px">
        <el-form-item label="選擇檔案">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            accept=".xlsx,.xls,.csv"
            :on-change="handleFileChange"
            :file-list="fileList"
          >
            <el-button :icon="Upload">選擇檔案</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支援 Excel (.xlsx, .xls) 和 CSV (.csv) 格式
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="覆蓋現有">
          <el-switch
            v-model="importOptions.overwriteExisting"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmImport" :loading="importing">
            匯入
          </el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Connection,
  Monitor,
  Cpu,
  Collection,
  Location,
  Plus,
  View,
  Download
} from '@element-plus/icons-vue'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Router
const router = useRouter()

// Store
const authStore = usePLCAuthStore()

// 系統統計數據
const systemStats = reactive({
  channelCount: 0,
  activeChannelCount: 0,
  deviceCount: 0,
  activeDeviceCount: 0,
  tagCount: 0,
  alarmTagCount: 0,
  groupCount: 0,
  groupTagCount: 0,
  regionCount: 0,
  highTempAlarms: 0,
  lowTempAlarms: 0,
  offlineDevices: 0
})

/**
 * 導航到指定模組
 */
const navigateToModule = (module: string) => {
  router.push(`/plc/tags/${module}`)
}

/**
 * 快速新增測點
 */
const quickAddTag = () => {
  router.push('/plc/tags/tag')
  // 可以在路由中傳遞參數來直接打開新增對話框
}

/**
 * 查看即時監控
 */
const viewRealtime = () => {
  router.push('/plc/database/realtime')
}

/**
 * 匯出資料
 */
const exportData = () => {
  // TODO: 實現資料匯出功能
  ElMessage.info('匯出功能開發中...')
}

/**
 * 載入系統統計
 */
const loadSystemStats = async () => {
  try {
    // TODO: 調用API載入系統統計
    // 模擬數據
    Object.assign(systemStats, {
      channelCount: 8,
      activeChannelCount: 6,
      deviceCount: 25,
      activeDeviceCount: 22,
      tagCount: 156,
      alarmTagCount: 12,
      groupCount: 18,
      groupTagCount: 145,
      regionCount: 12,
      highTempAlarms: 2,
      lowTempAlarms: 1,
      offlineDevices: 3
    })

  } catch (error: any) {
    console.error('載入系統統計失敗:', error)
    ElMessage.error(error.message || '載入系統統計失敗')
  }
}

// 生命週期
onMounted(async () => {
  await loadSystemStats()
})

</script>

<style scoped>
.tags-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 10px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

.stats-row {
  margin-bottom: 30px;
}

.stats-card {
  border-radius: 8px;
  overflow: hidden;
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.channel-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.device-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.tag-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.group-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.modules-section {
  margin-bottom: 30px;
}

.modules-section h2 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 20px;
}

.module-card {
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.module-content {
  display: flex;
  align-items: flex-start;
  padding: 25px;
}

.module-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.channel-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.device-bg {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.tag-bg {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.group-bg {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.region-bg {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.module-info {
  flex: 1;
}

.module-info h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 10px;
}

.module-info p {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
}

.module-stats {
  display: flex;
  gap: 15px;
}

.module-stats span {
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 20px;
}

.action-card {
  border-radius: 8px;
  text-align: center;
}

.action-content {
  padding: 30px 20px;
}

.action-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 15px;
}

.action-content h4 {
  font-size: 16px;
  color: #303133;
  margin-bottom: 10px;
}

.action-content p {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;
}

.system-status h2 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 20px;
}

.status-list {
  padding: 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: #606266;
  font-size: 14px;
}

@media (max-width: 768px) {
  .stats-content {
    flex-direction: column;
    text-align: center;
  }

  .stats-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .module-content {
    flex-direction: column;
    text-align: center;
  }

  .module-icon {
    margin-right: 0;
    margin-bottom: 15px;
    align-self: center;
  }

  .module-stats {
    justify-content: center;
  }
}
</style>

