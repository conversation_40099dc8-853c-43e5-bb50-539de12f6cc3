<template>
  <div class="region-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>地區管理</h2>
      <p>管理系統地區結構，支援階層式地區組織</p>
    </div>

    <!-- 地區管理 -->
    <el-card class="region-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span>地區列表</span>
            <!-- 麵包屑導航 -->
            <el-breadcrumb v-if="breadcrumbs.length > 0" separator="/" class="breadcrumb">
              <el-breadcrumb-item
                v-for="(item, index) in breadcrumbs"
                :key="item.id"
                @click="navigateToRegion(item, index)"
                class="breadcrumb-item"
              >
                {{ item.name }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-button
              v-if="breadcrumbs.length > 0"
              type="info"
              @click="goBack"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回上級
            </el-button>
            <el-button type="primary" @click="showRegionDialog = true">
              新增地區
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜尋和篩選 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜尋地區名稱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="statusFilter"
              placeholder="狀態篩選"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="啟用" value="active" />
              <el-option label="停用" value="inactive" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="info" @click="refreshRegionList">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 地區表格 -->
      <el-table
        v-loading="loading"
        :data="filteredRegionList"
        stripe
        border
      >
        <el-table-column prop="name" label="地區名稱" width="250">
          <template #default="{ row }">
            <div class="region-name" @click="enterRegion(row)">
              <el-icon class="region-icon">
                <Location />
              </el-icon>
              <span class="clickable-name">{{ row.name }}</span>
              <el-icon v-if="row.hasChildren" class="arrow-icon">
                <ArrowRight />
              </el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="code" label="地區代碼" width="150" />

        <el-table-column prop="description" label="描述" min-width="200" />

        <el-table-column prop="childCount" label="子地區數量" width="120" align="right">
          <template #default="{ row }">
            <el-tag v-if="row.childCount > 0" type="info">{{ row.childCount }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '啟用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="建立時間" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editRegion(row)"
            >
              編輯
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="toggleRegionStatus(row)"
            >
              {{ row.status === 'active' ? '停用' : '啟用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteRegion(row)"
            >
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯地區對話框 -->
    <el-dialog
      v-model="showRegionDialog"
      :title="regionForm.id ? '編輯地區' : '新增地區'"
      width="600px"
    >
      <el-form
        ref="regionFormRef"
        :model="regionForm"
        :rules="regionRules"
        label-width="120px"
      >
        <el-form-item label="地區名稱" prop="name">
          <el-input v-model="regionForm.name" placeholder="請輸入地區名稱" />
        </el-form-item>

        <el-form-item label="地區代碼" prop="code">
          <el-input v-model="regionForm.code" placeholder="請輸入地區代碼" />
        </el-form-item>

        <el-form-item label="上級地區" prop="parentId">
          <el-tree-select
            v-model="regionForm.parentId"
            :data="regionTreeOptions"
            :props="treeProps"
            placeholder="請選擇上級地區（可選）"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="regionForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入地區描述"
          />
        </el-form-item>

        <el-form-item label="狀態">
          <el-radio-group v-model="regionForm.status">
            <el-radio value="active">啟用</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="排序">
          <el-input-number
            v-model="regionForm.sortOrder"
            :min="0"
            :max="9999"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showRegionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRegion">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Location,
  ArrowLeft,
  ArrowRight
} from '@element-plus/icons-vue'
import { regionAPI, type RegionItem } from '@/api/plc/tags'
import { usePLCAuthStore } from '@/store/modules/plc-auth'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const regionFormRef = ref<InstanceType<typeof ElForm>>()

// 響應式數據
const loading = ref(false)
const showRegionDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 導航相關
const breadcrumbs = ref<RegionItem[]>([])
const currentParentId = ref('')

// 數據列表
const regionList = ref<RegionItem[]>([])
const regionTreeOptions = ref<RegionItem[]>([])

// 樹狀結構屬性
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 地區表單
const regionForm = reactive({
  id: '',
  name: '',
  code: '',
  parentId: '',
  description: '',
  status: 'active',
  sortOrder: 0
})

// 表單驗證規則
const regionRules = {
  name: [
    { required: true, message: '請輸入地區名稱', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '請輸入地區代碼', trigger: 'blur' }
  ]
}

// 計算屬性
const filteredRegionList = computed(() => {
  let filtered = regionList.value

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.code?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }

  return filtered
})

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 搜尋處理
 */
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中處理
}

/**
 * 篩選處理
 */
const handleFilter = () => {
  // 篩選邏輯已在計算屬性中處理
}

/**
 * 分頁大小變更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadRegionList()
}

/**
 * 當前頁變更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadRegionList()
}

/**
 * 刷新地區列表
 */
const refreshRegionList = () => {
  loadRegionList()
}

/**
 * 進入子地區
 */
const enterRegion = (region: RegionItem) => {
  if (region.hasChildren) {
    breadcrumbs.value.push(region)
    currentParentId.value = region.id
    loadRegionList()
  }
}

/**
 * 導航到指定地區
 */
const navigateToRegion = (region: RegionItem, index: number) => {
  // 移除當前位置之後的麵包屑
  breadcrumbs.value = breadcrumbs.value.slice(0, index + 1)
  currentParentId.value = region.id
  loadRegionList()
}

/**
 * 返回上級
 */
const goBack = () => {
  if (breadcrumbs.value.length > 0) {
    breadcrumbs.value.pop()
    currentParentId.value = breadcrumbs.value.length > 0
      ? breadcrumbs.value[breadcrumbs.value.length - 1].id
      : ''
    loadRegionList()
  }
}

/**
 * 編輯地區
 */
const editRegion = (row: RegionItem) => {
  Object.assign(regionForm, {
    ...row,
    parentId: currentParentId.value
  })
  showRegionDialog.value = true
}

/**
 * 切換地區狀態
 */
const toggleRegionStatus = async (row: RegionItem) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    const statusText = newStatus === 'active' ? '啟用' : '停用'

    await ElMessageBox.confirm(
      `確定要${statusText}地區 "${row.name}" 嗎？`,
      '狀態變更確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 注意：後端資料庫 Region 表沒有 Status 欄位，這是前端 UI 狀態模擬
    // 如果未來後端支持地區狀態，可以調用相應的 API
    console.log('地區狀態切換 (前端模擬):', {
      regionId: row.id,
      regionName: row.name,
      oldStatus: row.status,
      newStatus: newStatus,
      note: '後端 Region 表無 Status 欄位，此為前端 UI 狀態模擬'
    })

    row.status = newStatus
    ElMessage.success(`地區${statusText}成功 (前端狀態更新)`, {
      duration: 5000,
      showClose: true
    })

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('切換地區狀態失敗:', error)

      // 提取錯誤訊息
      let errorMessage = '切換地區狀態失敗'
      if (error?.response?.data?.Message) {
        errorMessage += `: ${error.response.data.Message}`
      } else if (error?.response?.data?.message) {
        errorMessage += `: ${error.response.data.message}`
      } else if (error?.message) {
        errorMessage += `: ${error.message}`
      } else if (typeof error === 'string') {
        errorMessage += `: ${error}`
      }

      ElMessage.error(errorMessage, {
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 刪除地區
 */
const deleteRegion = async (row: RegionItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除地區 "${row.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除地區
    const deleteData = {
      RegionId: row.id
    }

    console.log('刪除地區 API 請求:', deleteData)
    const response = await tagsAPI.deleteRegion(deleteData)
    console.log('刪除地區 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('地區刪除成功')
      await loadRegionList()
    } else {
      const errorMessage = response?.Message || response?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除地區失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除地區失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存地區
 */
const saveRegion = async () => {
  if (!regionFormRef.value) return

  try {
    await regionFormRef.value.validate()

    // 設置父級ID
    if (!regionForm.parentId && currentParentId.value) {
      regionForm.parentId = currentParentId.value
    }

    // 調用API保存地區
    const saveData = {
      Name: regionForm.name,
      ParentId: regionForm.parentId || undefined,
      Description: regionForm.description || undefined
    }

    let response
    if (regionForm.id) {
      // 更新地區
      const updateData = {
        Id: regionForm.id,
        ...saveData
      }
      console.log('更新地區 API 請求:', updateData)
      response = await tagsAPI.updateRegion(updateData)
      console.log('更新地區 API 響應:', response)
    } else {
      // 新增地區
      console.log('新增地區 API 請求:', saveData)
      response = await tagsAPI.createRegion(saveData)
      console.log('新增地區 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(regionForm.id ? '地區更新成功' : '地區新增成功')
      showRegionDialog.value = false

      // 重置表單
      Object.assign(regionForm, {
        id: '',
        name: '',
        code: '',
        parentId: '',
        description: '',
        status: 'active',
        sortOrder: 0
      })

      await loadRegionList()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存地區失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存地區失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

/**
 * 載入地區列表
 */
const loadRegionList = async () => {
  try {
    loading.value = true

    // 調用真實API載入地區列表
    const response = await plcDataService.get('/Tag/GetRegionHierarchyListAsync', {
      parentId: currentParentId.value || null,
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    if (response && response.data) {
      // 轉換後端數據格式為前端格式
      regionList.value = response.data.map((region: any) => ({
        id: region.RegionId || region.Id,
        name: region.RegionName || region.Name,
        code: region.RegionCode || region.Code,
        description: region.RegionDescription || region.Description,
        status: region.IsActive ? 'active' : 'inactive',
        hasChildren: region.HasChildren || false,
        childCount: region.ChildCount || 0,
        sortOrder: region.SortOrder || 0,
        createTime: region.CreatedTime || new Date().toISOString()
      }))

      pagination.total = response.total || regionList.value.length
      ElMessage.success(`成功載入 ${regionList.value.length} 個地區`)
    } else {
      regionList.value = []
      pagination.total = 0
      ElMessage.warning('未找到地區數據')
    }
    } else if (currentParentId.value === '1') {
      // 台北總部的子地區
      regionList.value = [
        {
          id: '11',
          name: '一樓',
          code: 'TPE-HQ-1F',
          description: '一樓大廳及接待區',
          status: 'active',
          hasChildren: true,
          childCount: 2,
          sortOrder: 1,
          createTime: new Date().toISOString()
        },
        {
          id: '12',
          name: '二樓',
          code: 'TPE-HQ-2F',
          description: '二樓辦公區',
          status: 'active',
          hasChildren: false,
          childCount: 0,
          sortOrder: 2,
          createTime: new Date().toISOString()
        }
      ]
    } else {
      regionList.value = []
    }

    totalCount.value = regionList.value.length

  } catch (error: any) {
    console.error('載入地區列表失敗:', error)
    ElMessage.error(error.message || '載入地區列表失敗')
  } finally {
    loading.value = false
  }
}

/**
 * 載入地區樹選項
 */
const loadRegionTreeOptions = async () => {
  try {
    // TODO: 調用API載入完整地區樹
    // 模擬數據
    regionTreeOptions.value = [
      {
        id: '1',
        name: '台北總部',
        children: [
          {
            id: '11',
            name: '一樓',
            children: [
              { id: '111', name: '大廳', children: [] },
              { id: '112', name: '接待區', children: [] }
            ]
          },
          {
            id: '12',
            name: '二樓',
            children: []
          }
        ]
      },
      {
        id: '2',
        name: '台中分部',
        children: [
          { id: '21', name: '辦公區A', children: [] },
          { id: '22', name: '辦公區B', children: [] }
        ]
      }
    ]

  } catch (error: any) {
    console.error('載入地區樹選項失敗:', error)
    ElMessage.error(error.message || '載入地區樹選項失敗')
  }
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadRegionList(),
    loadRegionTreeOptions()
  ])
})
</script>

<style scoped>
.region-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.region-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-left > span {
  font-size: 16px;
  font-weight: bold;
}

.breadcrumb {
  margin-left: 20px;
}

.breadcrumb-item {
  cursor: pointer;
  color: #409eff;
}

.breadcrumb-item:hover {
  text-decoration: underline;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 20px;
}

.region-name {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.region-name:hover .clickable-name {
  color: #409eff;
  text-decoration: underline;
}

.region-icon {
  margin-right: 8px;
  color: #409eff;
}

.clickable-name {
  flex: 1;
  transition: color 0.3s;
}

.arrow-icon {
  margin-left: 8px;
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #303133;
  cursor: default;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner):hover {
  text-decoration: none;
}
</style>
