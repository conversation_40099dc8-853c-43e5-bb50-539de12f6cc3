<template>
  <div class="tag-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>測點管理</h2>
      <p>管理 PLC 系統的測點，包含測點列表和測點分類</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="tag-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 測點列表 -->
        <el-tab-pane label="測點列表" name="list">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>測點列表</span>
                  <div>
                    <el-button type="success" @click="testDialog" style="margin-right: 10px;">
                      測試對話框
                    </el-button>
                    <el-button type="primary" @click="showTagDialog = true">
                      新增測點
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 搜尋和篩選 -->
              <div class="filter-section">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜尋測點名稱..."
                      clearable
                      @input="handleSearch"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="statusFilter"
                      placeholder="狀態篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="啟用" value="active" />
                      <el-option label="停用" value="inactive" />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="typeFilter"
                      placeholder="類型篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="類比" value="Analog" />
                      <el-option label="數位" value="Digital" />
                      <el-option label="計算" value="Calculated" />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="deviceFilter"
                      placeholder="裝置篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option
                        v-for="device in deviceOptions"
                        :key="device.id"
                        :label="device.name"
                        :value="device.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-button type="info" @click="refreshTagList">
                      <el-icon><Refresh /></el-icon>
                      刷新
                    </el-button>
                    <el-button type="success" @click="exportTags">
                      <el-icon><Download /></el-icon>
                      匯出
                    </el-button>
                  </el-col>
                </el-row>
              </div>

              <!-- 測點表格 -->
              <el-table
                v-loading="tagLoading"
                :data="filteredTagList"
                stripe
                border
                height="500"
              >
                <el-table-column prop="name" label="測點名稱" width="150" fixed="left">
                  <template #default="{ row }">
                    <div class="tag-name">
                      <el-icon class="tag-icon">
                        <Cpu />
                      </el-icon>
                      {{ row.name }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="description" label="描述" width="200" />

                <el-table-column prop="tagType" label="測點類型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getTagTypeColor(row.tagType)">
                      {{ getTagTypeText(row.tagType) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="dataType" label="數據類型" width="100">
                  <template #default="{ row }">
                    <el-tag type="info">{{ row.dataType }}</el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="address" label="地址" width="120" />

                <el-table-column prop="deviceName" label="裝置" width="150" />

                <el-table-column prop="unit" label="單位" width="80" />

                <el-table-column prop="currentValue" label="當前值" width="100" align="right">
                  <template #default="{ row }">
                    <span :class="getValueClass(row)">
                      {{ formatValue(row.currentValue, row.dataType) }}
                    </span>
                  </template>
                </el-table-column>

                <el-table-column prop="alarmEnabled" label="警報" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="row.alarmEnabled ? 'warning' : 'info'" size="small">
                      {{ row.alarmEnabled ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="狀態" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="updateTime" label="更新時間" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.updateTime) }}
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editTag(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleTagStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteTag(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分頁 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :total="totalCount"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 測點分類 -->
        <el-tab-pane label="測點分類" name="class">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>測點分類</span>
                  <el-button type="primary" @click="showClassDialog = true">
                    新增分類
                  </el-button>
                </div>
              </template>

              <!-- 分類樹狀結構 -->
              <el-tree
                ref="classTreeRef"
                v-loading="classLoading"
                :data="tagClassTree"
                :props="treeProps"
                node-key="id"
                default-expand-all
                :expand-on-click-node="false"
              >
                <template #default="{ node, data }">
                  <div class="tree-node">
                    <span class="node-label">{{ node.label }}</span>
                    <div class="node-actions">
                      <el-button
                        type="primary"
                        size="small"
                        @click="editClass(data)"
                      >
                        編輯
                      </el-button>
                      <el-button
                        type="success"
                        size="small"
                        @click="addSubClass(data)"
                      >
                        新增子分類
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="deleteClass(data)"
                      >
                        刪除
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-tree>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 新增/編輯測點對話框 -->
    <el-dialog
      v-model="showTagDialog"
      :title="tagForm.id ? '編輯測點' : '新增測點'"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-tabs v-model="tagDialogTab" type="border-card">
        <!-- 基礎設定 -->
        <el-tab-pane label="基礎設定" name="basic">
          <el-form
            ref="tagFormRef"
            :model="tagForm"
            :rules="tagRules"
            label-width="120px"
            class="tag-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="測點名稱" prop="name">
                  <el-input v-model="tagForm.name" placeholder="請輸入測點名稱" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="測點類型" prop="tagType">
                  <el-select v-model="tagForm.tagType" style="width: 100%">
                    <el-option label="類比" value="Analog" />
                    <el-option label="數位" value="Digital" />
                    <el-option label="計算" value="Calculated" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="描述">
              <el-input
                v-model="tagForm.description"
                type="textarea"
                :rows="2"
                placeholder="請輸入測點描述"
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="數據類型" prop="dataType">
                  <el-select v-model="tagForm.dataType" style="width: 100%">
                    <el-option label="Boolean" value="Boolean" />
                    <el-option label="Int16" value="Int16" />
                    <el-option label="Int32" value="Int32" />
                    <el-option label="Float" value="Float" />
                    <el-option label="Double" value="Double" />
                    <el-option label="String" value="String" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="地址" prop="address">
                  <el-input v-model="tagForm.address" placeholder="例如: 40001" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="裝置" prop="deviceId">
                  <el-select v-model="tagForm.deviceId" style="width: 100%">
                    <el-option
                      v-for="device in deviceOptions"
                      :key="device.id"
                      :label="device.name"
                      :value="device.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="單位">
                  <el-input v-model="tagForm.unit" placeholder="例如: °C, %, V" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="最小值">
                  <el-input-number
                    v-model="tagForm.minValue"
                    style="width: 100%"
                    :precision="2"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大值">
                  <el-input-number
                    v-model="tagForm.maxValue"
                    style="width: 100%"
                    :precision="2"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="預設值">
                  <el-input-number
                    v-model="tagForm.defaultValue"
                    style="width: 100%"
                    :precision="2"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="掃描頻率">
                  <el-input-number
                    v-model="tagForm.scanRate"
                    :min="100"
                    :max="60000"
                    style="width: 100%"
                  />
                  <span style="margin-left: 8px">毫秒</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="狀態">
                  <el-radio-group v-model="tagForm.status">
                    <el-radio value="active">啟用</el-radio>
                    <el-radio value="inactive">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="唯讀">
              <el-switch v-model="tagForm.isReadOnly" />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 警報設定 -->
        <el-tab-pane label="警報設定" name="alarm">
          <el-form
            :model="tagForm"
            label-width="100px"
            class="alarm-form compact-form"
          >
            <el-form-item label="警報狀態">
              <el-select v-model="tagForm.alarmStatus" style="width: 200px">
                <el-option label="停用" :value="0" />
                <el-option label="一般警報" :value="1" />
                <el-option label="重要警報" :value="2" />
              </el-select>
            </el-form-item>

            <!-- 當警報狀態不是停用時，顯示警報設定欄位 -->
            <template v-if="tagForm.alarmStatus !== 0">
              <!-- 通用警報設定 -->
              <div class="alarm-section">
                <el-row :gutter="16" class="compact-row">
                  <el-col :span="8">
                    <el-form-item label="播放語音" class="compact-item">
                      <el-switch v-model="tagForm.alarmAudio" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="通知群組" class="compact-item">
                      <el-select
                        v-model="tagForm.notifyGroups"
                        multiple
                        style="width: 100%"
                        placeholder="請選擇通知群組"
                        size="default"
                      >
                        <el-option
                          v-for="group in notifyGroupOptions"
                          :key="group.id"
                          :label="group.name"
                          :value="group.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="SOP 說明" class="compact-item">
                  <el-input
                    v-model="tagForm.alarmSOP"
                    type="textarea"
                    :rows="2"
                    placeholder="請輸入標準作業程序說明"
                    resize="none"
                  />
                </el-form-item>
              </div>

              <!-- 類比警報設定 -->
              <template v-if="tagForm.tagType === 'Analog'">
                <el-divider content-position="left">類比警報設定</el-divider>

                <!-- HH 警報 -->
                <el-card class="alarm-card compact-card" shadow="never">
                  <template #header>
                    <div class="alarm-header compact-header">
                      <span class="alarm-title">HH 警報 (極高)</span>
                      <el-switch v-model="tagForm.hhAlarmEnabled" size="default" />
                    </div>
                  </template>
                  <template v-if="tagForm.hhAlarmEnabled">
                    <el-row :gutter="12" class="compact-row">
                      <el-col :span="12">
                        <el-form-item label="警報值" class="compact-item">
                          <el-input-number
                            v-model="tagForm.hhAlarmValue"
                            style="width: 100%"
                            :precision="2"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="說明" class="compact-item">
                          <el-input
                            v-model="tagForm.hhAlarmDescription"
                            placeholder="請輸入警報說明"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-card>

                <!-- HI 警報 -->
                <el-card class="alarm-card compact-card" shadow="never">
                  <template #header>
                    <div class="alarm-header compact-header">
                      <span class="alarm-title">HI 警報 (高)</span>
                      <el-switch v-model="tagForm.hiAlarmEnabled" size="default" />
                    </div>
                  </template>
                  <template v-if="tagForm.hiAlarmEnabled">
                    <el-row :gutter="12" class="compact-row">
                      <el-col :span="12">
                        <el-form-item label="警報值" class="compact-item">
                          <el-input-number
                            v-model="tagForm.hiAlarmValue"
                            style="width: 100%"
                            :precision="2"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="說明" class="compact-item">
                          <el-input
                            v-model="tagForm.hiAlarmDescription"
                            placeholder="請輸入警報說明"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-card>

                <!-- LO 警報 -->
                <el-card class="alarm-card compact-card" shadow="never">
                  <template #header>
                    <div class="alarm-header compact-header">
                      <span class="alarm-title">LO 警報 (低)</span>
                      <el-switch v-model="tagForm.loAlarmEnabled" size="default" />
                    </div>
                  </template>
                  <template v-if="tagForm.loAlarmEnabled">
                    <el-row :gutter="12" class="compact-row">
                      <el-col :span="12">
                        <el-form-item label="警報值" class="compact-item">
                          <el-input-number
                            v-model="tagForm.loAlarmValue"
                            style="width: 100%"
                            :precision="2"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="說明" class="compact-item">
                          <el-input
                            v-model="tagForm.loAlarmDescription"
                            placeholder="請輸入警報說明"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-card>

                <!-- LL 警報 -->
                <el-card class="alarm-card compact-card" shadow="never">
                  <template #header>
                    <div class="alarm-header compact-header">
                      <span class="alarm-title">LL 警報 (極低)</span>
                      <el-switch v-model="tagForm.llAlarmEnabled" size="default" />
                    </div>
                  </template>
                  <template v-if="tagForm.llAlarmEnabled">
                    <el-row :gutter="12" class="compact-row">
                      <el-col :span="12">
                        <el-form-item label="警報值" class="compact-item">
                          <el-input-number
                            v-model="tagForm.llAlarmValue"
                            style="width: 100%"
                            :precision="2"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="說明" class="compact-item">
                          <el-input
                            v-model="tagForm.llAlarmDescription"
                            placeholder="請輸入警報說明"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-card>
              </template>

              <!-- 數位警報設定 -->
              <template v-if="tagForm.tagType === 'Digital'">
                <el-divider content-position="left" class="compact-divider">數位警報設定</el-divider>

                <!-- 警報狀態 -->
                <el-card class="alarm-card compact-card" shadow="never">
                  <template #header>
                    <div class="alarm-header compact-header">
                      <span class="alarm-title">警報狀態</span>
                      <el-switch v-model="tagForm.digitalAlarmEnabled" size="default" />
                    </div>
                  </template>
                  <template v-if="tagForm.digitalAlarmEnabled">
                    <el-row :gutter="12" class="compact-row">
                      <el-col :span="12">
                        <el-form-item label="警報值" class="compact-item">
                          <el-select v-model="tagForm.digitalAlarmValue" style="width: 100%" size="default">
                            <el-option label="0" :value="0" />
                            <el-option label="1" :value="1" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="說明" class="compact-item">
                          <el-input
                            v-model="tagForm.digitalAlarmDescription"
                            placeholder="請輸入警報說明"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-card>

                <!-- 復歸狀態 -->
                <el-card class="alarm-card compact-card" shadow="never">
                  <template #header>
                    <div class="alarm-header compact-header">
                      <span class="alarm-title">復歸狀態</span>
                      <el-switch v-model="tagForm.digitalResetEnabled" size="default" />
                    </div>
                  </template>
                  <template v-if="tagForm.digitalResetEnabled">
                    <el-row :gutter="12" class="compact-row">
                      <el-col :span="12">
                        <el-form-item label="復歸值" class="compact-item">
                          <el-select v-model="tagForm.digitalResetValue" style="width: 100%" size="default">
                            <el-option label="0" :value="0" />
                            <el-option label="1" :value="1" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="說明" class="compact-item">
                          <el-input
                            v-model="tagForm.digitalResetDescription"
                            placeholder="請輸入復歸說明"
                            size="default"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-card>
              </template>

              <!-- 例外設定 -->
              <el-divider content-position="left" class="compact-divider">例外設定</el-divider>
              <el-card class="alarm-card compact-card" shadow="never">
                <template #header>
                  <div class="alarm-header compact-header">
                    <span class="alarm-title">例外設定</span>
                    <el-switch v-model="tagForm.exceptionEnabled" size="default" />
                  </div>
                </template>
                <template v-if="tagForm.exceptionEnabled">
                  <el-row :gutter="12" class="compact-row">
                    <el-col :span="12">
                      <el-form-item label="開始時間" class="compact-item">
                        <el-time-picker
                          v-model="tagForm.exceptionStartTime"
                          format="HH:mm:ss"
                          placeholder="選擇開始時間"
                          style="width: 100%"
                          size="default"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="結束時間" class="compact-item">
                        <el-time-picker
                          v-model="tagForm.exceptionEndTime"
                          format="HH:mm:ss"
                          placeholder="選擇結束時間"
                          style="width: 100%"
                          size="default"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="12" class="compact-row">
                    <el-col :span="12">
                      <el-form-item label="例外動作" class="compact-item">
                        <el-select
                          v-model="tagForm.exceptionAction"
                          style="width: 100%"
                          placeholder="請選擇例外動作"
                          size="default"
                        >
                          <el-option label="停止警報" :value="0" />
                          <el-option label="延遲警報" :value="1" />
                          <el-option label="降級警報" :value="2" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="例外說明" class="compact-item">
                        <el-input
                          v-model="tagForm.exceptionDescription"
                          placeholder="請輸入例外設定說明"
                          size="default"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </el-card>
            </template>
          </el-form>
        </el-tab-pane>

        <!-- 運算式設定 -->
        <el-tab-pane label="運算式設定" name="expression">
          <el-form
            :model="tagForm"
            label-width="120px"
            class="expression-form"
          >
            <el-form-item label="啟用運算式">
              <el-switch v-model="tagForm.expressionEnabled" />
            </el-form-item>

            <template v-if="tagForm.expressionEnabled">
              <el-form-item label="轉換方式">
                <el-select v-model="tagForm.expressionType" style="width: 300px">
                  <el-option
                    v-for="type in expressionTypeOptions"
                    :key="type.id"
                    :label="type.name"
                    :value="type.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="運算式內容">
                <el-input
                  v-model="tagForm.expressionContent"
                  type="textarea"
                  :rows="4"
                  placeholder="請輸入運算式內容，例如: (A + B) * 2"
                />
                <div class="expression-help">
                  <el-text type="info" size="small">
                    支援基本數學運算符號：+、-、*、/、()，以及函數：sin、cos、tan、sqrt、abs 等
                  </el-text>
                </div>
              </el-form-item>

              <!-- 相關測點管理 -->
              <el-divider content-position="left">相關測點</el-divider>

              <el-form-item label="選擇測點">
                <el-row :gutter="10">
                  <el-col :span="16">
                    <el-select
                      v-model="selectedRelatedTag"
                      filterable
                      placeholder="請選擇要加入的測點"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="tag in availableTagsForExpression"
                        :key="tag.id"
                        :label="`${tag.name} (${tag.description})`"
                        :value="tag.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-button
                      type="primary"
                      :disabled="!selectedRelatedTag"
                      @click="addRelatedTag"
                    >
                      加入測點
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>

              <!-- 已選擇的相關測點列表 -->
              <el-form-item label="已選測點">
                <div v-if="tagForm.relatedTags && tagForm.relatedTags.length > 0">
                  <el-tag
                    v-for="(relatedTag, index) in tagForm.relatedTags"
                    :key="relatedTag.id"
                    closable
                    @close="removeRelatedTag(index)"
                    style="margin-right: 8px; margin-bottom: 8px"
                  >
                    {{ relatedTag.name }} ({{ relatedTag.description }})
                  </el-tag>
                </div>
                <el-text v-else type="info">尚未選擇相關測點</el-text>
              </el-form-item>

              <!-- 運算式預覽 -->
              <el-form-item label="運算式預覽">
                <el-input
                  :value="expressionPreview"
                  readonly
                  type="textarea"
                  :rows="2"
                  placeholder="運算式預覽將在此顯示"
                />
              </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <el-button @click="showTagDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTag">確認</el-button>
      </template>
    </el-dialog>

    <!-- 新增/編輯分類對話框 -->
    <el-dialog
      v-model="showClassDialog"
      :title="classForm.id ? '編輯分類' : '新增分類'"
      width="500px"
    >
      <el-form
        ref="classFormRef"
        :model="classForm"
        :rules="classRules"
        label-width="120px"
      >
        <el-form-item label="分類名稱" prop="name">
          <el-input v-model="classForm.name" placeholder="請輸入分類名稱" />
        </el-form-item>

        <el-form-item label="上級分類" prop="parentId">
          <el-tree-select
            v-model="classForm.parentId"
            :data="tagClassTree"
            :props="treeProps"
            placeholder="請選擇上級分類（可選）"
            clearable
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="classForm.description"
            type="textarea"
            :rows="3"
            placeholder="請輸入分類描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showClassDialog = false">取消</el-button>
        <el-button type="primary" @click="saveClass">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import { 
  Search, 
  Refresh, 
  Download,
  Cpu 
} from '@element-plus/icons-vue'
import { tagAPI, type TagItem, type TagClassItem, type DeviceItem } from '@/api/plc/tags'
import { usePLCAuthStore } from '@/store/modules/plc-auth'
import { plcDataService } from '@/utils/plc/dataService'

// Store
const authStore = usePLCAuthStore()

// 表單引用
const tagFormRef = ref<InstanceType<typeof ElForm>>()
const classFormRef = ref<InstanceType<typeof ElForm>>()
const classTreeRef = ref()

// 響應式數據
const activeTab = ref('list')
const tagDialogTab = ref('basic')
const tagLoading = ref(false)
const classLoading = ref(false)
const showTagDialog = ref(false)
const showClassDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const deviceFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 數據列表
const tagList = ref<TagItem[]>([])
const tagClassTree = ref<TagClassItem[]>([])
const deviceOptions = ref<DeviceItem[]>([])
const notifyGroupOptions = ref([])
const expressionTypeOptions = ref([
  { id: 0, name: '線性轉換' },
  { id: 1, name: '數學運算' },
  { id: 2, name: '邏輯運算' },
  { id: 3, name: '條件運算' }
])

// 運算式相關
const selectedRelatedTag = ref('')
const availableTagsForExpression = computed(() => {
  return tagList.value.filter(tag =>
    tag.id !== tagForm.id &&
    !tagForm.relatedTags.some(related => related.id === tag.id)
  )
})

// 運算式預覽
const expressionPreview = computed(() => {
  if (!tagForm.expressionContent) return ''

  let preview = tagForm.expressionContent
  tagForm.relatedTags.forEach(tag => {
    const regex = new RegExp(`\\b${tag.name}\\b`, 'g')
    preview = preview.replace(regex, `[${tag.name}]`)
  })

  return preview
})

// 樹狀結構屬性
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 測點表單
const tagForm = reactive({
  id: '',
  name: '',
  description: '',
  tagType: 'Analog',
  dataType: 'Float',
  address: '',
  deviceId: '',
  unit: '',
  minValue: 0,
  maxValue: 100,
  defaultValue: 0,
  scanRate: 1000,
  status: 'active',
  isReadOnly: false,

  // 警報設定
  alarmStatus: 1, // 1: 停用, 2: 啟用
  alarmAudio: false,
  alarmSOP: '',
  notifyGroups: [],

  // 類比警報
  hhAlarmEnabled: false,
  hhAlarmValue: 0,
  hhAlarmDescription: '',
  hiAlarmEnabled: false,
  hiAlarmValue: 0,
  hiAlarmDescription: '',
  loAlarmEnabled: false,
  loAlarmValue: 0,
  loAlarmDescription: '',
  llAlarmEnabled: false,
  llAlarmValue: 0,
  llAlarmDescription: '',

  // 數位警報
  digitalAlarmEnabled: false,
  digitalAlarmValue: 0,
  digitalAlarmDescription: '',
  digitalResetEnabled: false,
  digitalResetValue: 0,
  digitalResetDescription: '',

  // 例外設定
  exceptionEnabled: false,
  exceptionStartTime: null,
  exceptionEndTime: null,
  exceptionAction: 0, // 0: 停止警報, 1: 延遲警報, 2: 降級警報
  exceptionDescription: '',

  // 運算式設定
  expressionEnabled: false,
  expressionType: 0,
  expressionContent: '',
  relatedTags: []
})

// 分類表單
const classForm = reactive({
  id: '',
  name: '',
  parentId: '',
  description: ''
})

// 表單驗證規則
const tagRules = {
  name: [
    { required: true, message: '請輸入測點名稱', trigger: 'blur' }
  ],
  tagType: [
    { required: true, message: '請選擇測點類型', trigger: 'change' }
  ],
  dataType: [
    { required: true, message: '請選擇數據類型', trigger: 'change' }
  ],
  address: [
    { required: true, message: '請輸入地址', trigger: 'blur' }
  ],
  deviceId: [
    { required: true, message: '請選擇裝置', trigger: 'change' }
  ]
}

const classRules = {
  name: [
    { required: true, message: '請輸入分類名稱', trigger: 'blur' }
  ]
}

// 計算屬性
const filteredTagList = computed(() => {
  let filtered = tagList.value

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
  }

  // 類型篩選
  if (typeFilter.value) {
    filtered = filtered.filter(item => item.tagType === typeFilter.value)
  }

  // 裝置篩選
  if (deviceFilter.value) {
    filtered = filtered.filter(item => item.deviceId === deviceFilter.value)
  }

  return filtered
})

/**
 * 獲取測點類型顏色
 */
const getTagTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    Analog: 'primary',
    Digital: 'success',
    Calculated: 'warning'
  }
  return colorMap[type] || 'default'
}

/**
 * 獲取測點類型文字
 */
const getTagTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    Analog: '類比',
    Digital: '數位',
    Calculated: '計算'
  }
  return textMap[type] || type
}

/**
 * 獲取數值樣式類別
 */
const getValueClass = (row: TagItem): string => {
  if (row.alarmEnabled && row.currentValue !== null && row.currentValue !== undefined) {
    const value = Number(row.currentValue)
    if (row.highAlarmLimit && value > row.highAlarmLimit) return 'alarm-high'
    if (row.lowAlarmLimit && value < row.lowAlarmLimit) return 'alarm-low'
    if (row.highWarningLimit && value > row.highWarningLimit) return 'warning-high'
    if (row.lowWarningLimit && value < row.lowWarningLimit) return 'warning-low'
  }
  return 'normal-value'
}

/**
 * 格式化數值
 */
const formatValue = (value: any, dataType: string): string => {
  if (value === null || value === undefined) return '-'

  switch (dataType) {
    case 'Boolean':
      return value ? '真' : '假'
    case 'Float':
    case 'Double':
      return Number(value).toFixed(2)
    case 'Int16':
    case 'Int32':
      return Math.round(Number(value)).toString()
    default:
      return String(value)
  }
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 搜尋處理
 */
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中處理
}

/**
 * 篩選處理
 */
const handleFilter = () => {
  // 篩選邏輯已在計算屬性中處理
}

/**
 * 分頁大小變更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTagList()
}

/**
 * 當前頁變更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadTagList()
}

/**
 * 刷新測點列表
 */
const refreshTagList = () => {
  loadTagList()
}

/**
 * 匯出測點
 */
const exportTags = () => {
  // TODO: 實現測點匯出功能
  ElMessage.info('匯出功能開發中...')
}

/**
 * 測試對話框顯示
 */
const testDialog = () => {
  console.log('=== 測試對話框功能 ===')
  console.log('showTagDialog 當前值:', showTagDialog.value)

  // 重置表單
  resetTagForm()

  // 設置對話框顯示
  showTagDialog.value = true

  console.log('showTagDialog 設置後值:', showTagDialog.value)

  // 延遲檢查對話框狀態
  setTimeout(() => {
    const dialogs = document.querySelectorAll('.el-dialog')
    const overlays = document.querySelectorAll('.el-overlay')
    const visibleDialogs = Array.from(dialogs).filter(d =>
      window.getComputedStyle(d).display !== 'none'
    )

    console.log('=== 對話框檢查結果 ===')
    console.log('找到對話框數量:', dialogs.length)
    console.log('找到遮罩數量:', overlays.length)
    console.log('可見對話框數量:', visibleDialogs.length)

    dialogs.forEach((dialog, index) => {
      const style = window.getComputedStyle(dialog)
      console.log(`對話框 ${index} 狀態:`, {
        display: style.display,
        visibility: style.visibility,
        opacity: style.opacity,
        zIndex: style.zIndex
      })
    })

    if (visibleDialogs.length === 0) {
      console.log('⚠️ 對話框未顯示，嘗試強制顯示...')
      dialogs.forEach(dialog => {
        dialog.style.display = 'block'
        dialog.style.visibility = 'visible'
        dialog.style.opacity = '1'
        dialog.style.zIndex = '9999'
      })
      overlays.forEach(overlay => {
        overlay.style.display = 'block'
        overlay.style.visibility = 'visible'
        overlay.style.opacity = '1'
      })
    }
  }, 200)
}

/**
 * 編輯測點
 */
const editTag = (row: TagItem) => {
  console.log('editTag 被調用:', row)
  console.log('showTagDialog 當前值:', showTagDialog.value)

  Object.assign(tagForm, row)
  showTagDialog.value = true

  console.log('showTagDialog 設置後值:', showTagDialog.value)

  // 延遲檢查對話框是否顯示
  setTimeout(() => {
    const dialogs = document.querySelectorAll('.el-dialog')
    const visibleDialogs = Array.from(dialogs).filter(d =>
      window.getComputedStyle(d).display !== 'none'
    )
    console.log('找到對話框數量:', dialogs.length)
    console.log('可見對話框數量:', visibleDialogs.length)
  }, 100)
}

/**
 * 切換測點狀態
 */
const toggleTagStatus = async (row: TagItem) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    const statusText = newStatus === 'active' ? '啟用' : '停用'

    await ElMessageBox.confirm(
      `確定要${statusText}測點 "${row.name}" 嗎？`,
      '狀態變更確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 調用API更新狀態
    row.status = newStatus
    ElMessage.success(`測點${statusText}成功`)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('切換測點狀態失敗:', error)
      ElMessage.error('切換測點狀態失敗')
    }
  }
}

/**
 * 刪除測點
 */
const deleteTag = async (row: TagItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除測點 "${row.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除測點
    const deleteData = {
      TagId: row.id
    }

    console.log('刪除測點 API 請求:', deleteData)
    const response = await tagsAPI.deleteTag(deleteData)
    console.log('刪除測點 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('測點刪除成功')
      await loadTagList()
    } else {
      const errorMessage = response?.Message || response?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除測點失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除測點失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存測點
 */
const saveTag = async () => {
  if (!tagFormRef.value) return

  try {
    await tagFormRef.value.validate()

    // 準備保存數據
    const saveData = {
      // 基礎設定
      Id: tagForm.id,
      Name: tagForm.name,
      Description: tagForm.description,
      TagType: tagForm.tagType,
      DataType: tagForm.dataType,
      Address: tagForm.address,
      DeviceId: tagForm.deviceId,
      Unit: tagForm.unit,
      MinValue: tagForm.minValue,
      MaxValue: tagForm.maxValue,
      DefaultValue: tagForm.defaultValue,
      ScanRate: tagForm.scanRate,
      Status: tagForm.status === 'active',
      IsReadOnly: tagForm.isReadOnly,

      // 警報設定
      AlarmSetting: {
        Status: tagForm.alarmStatus,
        Audio: tagForm.alarmAudio,
        SOP: tagForm.alarmSOP,
        NotifyGroups: tagForm.notifyGroups,

        // 類比警報
        HHAlarm: tagForm.hhAlarmEnabled ? {
          Enabled: true,
          Value: tagForm.hhAlarmValue,
          Description: tagForm.hhAlarmDescription
        } : { Enabled: false },

        HIAlarm: tagForm.hiAlarmEnabled ? {
          Enabled: true,
          Value: tagForm.hiAlarmValue,
          Description: tagForm.hiAlarmDescription
        } : { Enabled: false },

        LOAlarm: tagForm.loAlarmEnabled ? {
          Enabled: true,
          Value: tagForm.loAlarmValue,
          Description: tagForm.loAlarmDescription
        } : { Enabled: false },

        LLAlarm: tagForm.llAlarmEnabled ? {
          Enabled: true,
          Value: tagForm.llAlarmValue,
          Description: tagForm.llAlarmDescription
        } : { Enabled: false },

        // 數位警報
        DigitalAlarm: tagForm.digitalAlarmEnabled ? {
          Enabled: true,
          Value: tagForm.digitalAlarmValue,
          Description: tagForm.digitalAlarmDescription
        } : { Enabled: false },

        DigitalReset: tagForm.digitalResetEnabled ? {
          Enabled: true,
          Value: tagForm.digitalResetValue,
          Description: tagForm.digitalResetDescription
        } : { Enabled: false },

        // 例外設定
        Exception: tagForm.exceptionEnabled ? {
          Enabled: true,
          StartTime: tagForm.exceptionStartTime ? tagForm.exceptionStartTime.toTimeString().split(' ')[0] : null,
          EndTime: tagForm.exceptionEndTime ? tagForm.exceptionEndTime.toTimeString().split(' ')[0] : null,
          Action: tagForm.exceptionAction,
          Description: tagForm.exceptionDescription
        } : { Enabled: false }
      },

      // 運算式設定
      ExpressionSetting: {
        Enabled: tagForm.expressionEnabled,
        Type: tagForm.expressionType,
        Content: tagForm.expressionContent,
        RelatedTags: tagForm.relatedTags
      }
    }

    // 調用API保存測點
    const apiEndpoint = tagForm.id ? '/Tag/UpdateTag' : '/Tag/CreateTag'
    const response = await plcDataService.post(apiEndpoint, saveData)

    if (response && response.ReturnCode === 1) {
      ElMessage.success(tagForm.id ? '測點更新成功' : '測點創建成功')
      showTagDialog.value = false
      resetTagForm()
      await loadTagList()
    } else {
      throw new Error(response?.Message || '保存失敗')
    }

  } catch (error: any) {
    console.error('保存測點失敗:', error)
    ElMessage.error(error.message || '保存測點失敗')
  }
}

/**
 * 重置測點表單
 */
const resetTagForm = () => {
  Object.assign(tagForm, {
    id: '',
    name: '',
    description: '',
    tagType: 'Analog',
    dataType: 'Float',
    address: '',
    deviceId: '',
    unit: '',
    minValue: 0,
    maxValue: 100,
    defaultValue: 0,
    scanRate: 1000,
    status: 'active',
    isReadOnly: false,

    // 警報設定
    alarmStatus: 1,
    alarmAudio: false,
    alarmSOP: '',
    notifyGroups: [],

    // 類比警報
    hhAlarmEnabled: false,
    hhAlarmValue: 0,
    hhAlarmDescription: '',
    hiAlarmEnabled: false,
    hiAlarmValue: 0,
    hiAlarmDescription: '',
    loAlarmEnabled: false,
    loAlarmValue: 0,
    loAlarmDescription: '',
    llAlarmEnabled: false,
    llAlarmValue: 0,
    llAlarmDescription: '',

    // 數位警報
    digitalAlarmEnabled: false,
    digitalAlarmValue: 0,
    digitalAlarmDescription: '',
    digitalResetEnabled: false,
    digitalResetValue: 0,
    digitalResetDescription: '',

    // 例外設定
    exceptionEnabled: false,
    exceptionStartTime: null,
    exceptionEndTime: null,
    exceptionAction: 0,
    exceptionDescription: '',

    // 運算式設定
    expressionEnabled: false,
    expressionType: 0,
    expressionContent: '',
    relatedTags: []
  })
}

/**
 * 編輯分類
 */
const editClass = (data: TagClassItem) => {
  Object.assign(classForm, data)
  showClassDialog.value = true
}

/**
 * 新增子分類
 */
const addSubClass = (data: TagClassItem) => {
  Object.assign(classForm, {
    id: '',
    name: '',
    parentId: data.id,
    description: ''
  })
  showClassDialog.value = true
}

/**
 * 刪除分類
 */
const deleteClass = async (data: TagClassItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除分類 "${data.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除分類
    const deleteData = {
      Id: data.id
    }

    console.log('刪除測點分類 API 請求:', deleteData)
    const response = await tagsAPI.deleteTagCategory(deleteData)
    console.log('刪除測點分類 API 響應:', response)

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success('分類刪除成功')
      await loadTagClassTree()
    } else {
      const errorMessage = response?.Message || response?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除分類失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除分類失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存分類
 */
const saveClass = async () => {
  if (!classFormRef.value) return

  try {
    await classFormRef.value.validate()

    // 調用API保存分類
    const saveData = {
      Name: classForm.name,
      ParentId: classForm.parentId || undefined,
      Description: classForm.description || undefined
    }

    let response
    if (classForm.id) {
      // 更新分類
      const updateData = {
        Id: classForm.id,
        ...saveData
      }
      console.log('更新測點分類 API 請求:', updateData)
      response = await tagsAPI.updateTagCategory(updateData)
      console.log('更新測點分類 API 響應:', response)
    } else {
      // 新增分類
      console.log('新增測點分類 API 請求:', saveData)
      response = await tagsAPI.createTagCategory(saveData)
      console.log('新增測點分類 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(classForm.id ? '分類更新成功' : '分類新增成功')
      showClassDialog.value = false

      // 重置表單
      Object.assign(classForm, {
        id: '',
        name: '',
        parentId: '',
        description: ''
      })

      await loadTagClassTree()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存分類失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存分類失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

/**
 * 載入測點列表
 */
const loadTagList = async () => {
  try {
    tagLoading.value = true

    // 調用真實API載入測點列表
    const response = await plcDataService.get('/Tag/GetTagList', {
      pageIndex: currentPage.value,
      pageSize: pageSize.value
    })

    if (response && response.Detail && response.Detail.TagList) {
      // 轉換後端數據格式為前端格式
      tagList.value = response.Detail.TagList.map((tag: any) => ({
        id: tag.TagId || tag.Id,
        name: tag.TagName || tag.Name,
        description: tag.TagDescription || tag.Description,
        tagType: tag.TagType || 'Analog',
        dataType: typeof tag.DataType === 'object' ? tag.DataType?.Name || 'Float' : tag.DataType || 'Float',
        address: tag.Address || '',
        deviceId: tag.DeviceId || '',
        deviceName: tag.DeviceName || '',
        unit: typeof tag.Unit === 'object' ? tag.Unit?.Name || tag.Unit?.Symbol || '' : tag.Unit || '',
        minValue: tag.MinValue,
        maxValue: tag.MaxValue,
        defaultValue: tag.DefaultValue,
        scanRate: tag.ScanRate || 1000,
        status: tag.IsActive ? 'active' : 'inactive',
        isReadOnly: tag.IsReadOnly || false,
        alarmEnabled: tag.AlarmEnabled || false,
        highAlarmLimit: tag.HighAlarmLimit,
        lowAlarmLimit: tag.LowAlarmLimit,
        highWarningLimit: tag.HighWarningLimit,
        lowWarningLimit: tag.LowWarningLimit,
        currentValue: tag.CurrentValue,
        updateTime: tag.UpdatedTime || tag.CreatedTime
      }))

      totalCount.value = response.Detail.TotalCount || tagList.value.length
      ElMessage.success(`成功載入 ${tagList.value.length} 個測點`)
    } else {
      tagList.value = []
      totalCount.value = 0
      ElMessage.warning('未找到測點數據')
    }

  } catch (error: any) {
    console.error('載入測點列表失敗:', error)
    ElMessage.error(error.message || '載入測點列表失敗')
  } finally {
    tagLoading.value = false
  }
}

/**
 * 載入測點分類樹
 */
const loadTagClassTree = async () => {
  try {
    classLoading.value = true

    // 調用真實API載入測點分類樹 - 使用群組階層列表
    const response = await plcDataService.get('/Tag/GetTagCategoryHierarchyList', {})

    if (response && response.Detail && response.Detail.TagCategoryHierarchyList) {
      // 轉換後端數據格式為前端格式
      tagClassTree.value = response.Detail.TagCategoryHierarchyList.map((category: any) => ({
        id: category.CategoryId || category.Id,
        name: category.CategoryName || category.Name,
        description: category.CategoryDescription || category.Description,
        children: category.Children ? category.Children.map((child: any) => ({
          id: child.CategoryId || child.Id,
          name: child.CategoryName || child.Name,
          description: child.CategoryDescription || child.Description,
          children: []
        })) : []
      }))

      if (tagClassTree.value.length > 0) {
        ElMessage.success(`成功載入 ${tagClassTree.value.length} 個測點分類`)
      } else {
        ElMessage.info('分類列表為空，可以新增分類')
      }
    } else {
      tagClassTree.value = []
      ElMessage.info('分類列表為空，可以新增分類')
    }

  } catch (error: any) {
    console.error('載入分類樹失敗:', error)
    ElMessage.error(error.message || '載入分類樹失敗')
  } finally {
    classLoading.value = false
  }
}

/**
 * 載入裝置選項
 */
const loadDeviceOptions = async () => {
  try {
    // 調用真實API載入裝置列表 - 修正API端點路徑
    const response = await plcDataService.get('/Tag/GetDeviceList', {})

    if (response && response.Detail && response.Detail.DeviceList) {
      // 轉換後端數據格式為前端格式
      deviceOptions.value = response.Detail.DeviceList.map((device: any) => ({
        id: device.DeviceId || device.Id,
        name: device.DeviceName || device.Name,
        deviceType: device.DeviceType || 'PLC',
        description: device.DeviceDescription || device.Description,
        ipAddress: device.IpAddress,
        port: device.Port,
        protocol: device.Protocol,
        status: device.IsActive ? 'active' : 'inactive',
        createTime: device.CreatedTime || new Date().toISOString()
      }))

      ElMessage.success(`成功載入 ${deviceOptions.value.length} 個裝置`)
    } else {
      deviceOptions.value = []
      ElMessage.warning('未找到裝置數據')
    }

  } catch (error: any) {
    console.error('載入裝置選項失敗:', error)
    ElMessage.error(error.message || '載入裝置選項失敗')
  }
}

/**
 * 載入通知群組選項
 */
const loadNotifyGroupOptions = async () => {
  try {
    // 調用真實API載入通知群組列表 - 使用正確的MessageController端點
    const response = await plcDataService.get('/Message/GetMessageGroupDetailList', {})

    if (response && response.Detail && response.Detail.MessageGroupDetailList) {
      notifyGroupOptions.value = response.Detail.MessageGroupDetailList.map((group: any) => ({
        id: group.Id,
        name: group.Name,
        description: group.Description || ''
      }))
    } else {
      notifyGroupOptions.value = []
    }
  } catch (error: any) {
    console.error('載入通知群組選項失敗:', error)
    notifyGroupOptions.value = []
  }
}

/**
 * 添加相關測點
 */
const addRelatedTag = () => {
  if (!selectedRelatedTag.value) return

  const tag = tagList.value.find(t => t.id === selectedRelatedTag.value)
  if (tag && !tagForm.relatedTags.some(rt => rt.id === tag.id)) {
    tagForm.relatedTags.push({
      id: tag.id,
      name: tag.name,
      description: tag.description
    })
    selectedRelatedTag.value = ''
  }
}

/**
 * 移除相關測點
 */
const removeRelatedTag = (index: number) => {
  tagForm.relatedTags.splice(index, 1)
}

/**
 * 載入測點詳細資料
 */
const loadTagDetails = async (tagId: string) => {
  try {
    // 調用真實API載入測點詳細資料
    const response = await plcDataService.get(`/Tag/GetTagDetail/${tagId}`, {})

    if (response && response.Detail) {
      const detail = response.Detail

      // 填充警報設定數據
      if (detail.Alarm) {
        tagForm.alarmStatus = detail.Alarm.Status || 1
        tagForm.alarmAudio = detail.Alarm.Audio || false
        tagForm.alarmSOP = detail.Alarm.SOP || ''
        tagForm.notifyGroups = detail.Alarm.NotifyGroups || []

        // 類比警報
        if (detail.Alarm.HH) {
          tagForm.hhAlarmEnabled = detail.Alarm.HH.Enabled || false
          tagForm.hhAlarmValue = detail.Alarm.HH.Value || 0
          tagForm.hhAlarmDescription = detail.Alarm.HH.Description || ''
        }
        // ... 其他警報設定

        // 填充例外設定數據
        if (detail.Alarm.Exception) {
          tagForm.exceptionEnabled = detail.Alarm.Exception.Enabled || false
          if (detail.Alarm.Exception.StartTime) {
            const startTime = new Date()
            const [hours, minutes, seconds] = detail.Alarm.Exception.StartTime.split(':')
            startTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
            tagForm.exceptionStartTime = startTime
          }
          if (detail.Alarm.Exception.EndTime) {
            const endTime = new Date()
            const [hours, minutes, seconds] = detail.Alarm.Exception.EndTime.split(':')
            endTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
            tagForm.exceptionEndTime = endTime
          }
          tagForm.exceptionAction = detail.Alarm.Exception.Action || 0
          tagForm.exceptionDescription = detail.Alarm.Exception.Description || ''
        }
      }

      // 填充運算式設定數據
      if (detail.Expression) {
        tagForm.expressionEnabled = detail.Expression.Enabled || false
        tagForm.expressionType = detail.Expression.Type || 0
        tagForm.expressionContent = detail.Expression.Content || ''
        tagForm.relatedTags = detail.Expression.RelatedTags || []
      }
    }
  } catch (error: any) {
    console.error('載入測點詳細資料失敗:', error)
  }
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadTagList(),
    loadTagClassTree(),
    loadDeviceOptions(),
    loadNotifyGroupOptions()
  ])
})
</script>

<style scoped>
.tag-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.tag-card {
  margin-bottom: 20px;
}

.tab-content {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.filter-section {
  margin-bottom: 20px;
}

.tag-name {
  display: flex;
  align-items: center;
}

.tag-icon {
  margin-right: 8px;
  color: #409eff;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

/* 警報設定樣式 */
.alarm-card {
  margin-bottom: 12px;
  border: 1px solid #e4e7ed;
}

.alarm-card.compact-card {
  margin-bottom: 8px;
}

.alarm-card.compact-card .el-card__body {
  padding: 12px 16px;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.alarm-header.compact-header {
  padding: 8px 0;
}

.alarm-title {
  font-size: 14px;
  color: #606266;
}

.alarm-form .el-form-item {
  margin-bottom: 16px;
}

.compact-form .el-form-item.compact-item {
  margin-bottom: 8px;
}

.compact-row {
  margin-bottom: 8px;
}

.compact-divider {
  margin: 12px 0 8px 0;
}

.alarm-section {
  margin-bottom: 12px;
}

/* 運算式設定樣式 */
.expression-form .el-form-item {
  margin-bottom: 20px;
}

.expression-help {
  margin-top: 8px;
}

.expression-help .el-text {
  font-size: 12px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-actions {
  display: flex;
  gap: 8px;
}

.node-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.tag-form,
.alarm-form {
  padding: 20px 0;
}

/* 數值狀態樣式 */
.normal-value {
  color: #303133;
}

.warning-high,
.warning-low {
  color: #e6a23c;
  font-weight: bold;
}

.alarm-high,
.alarm-low {
  color: #f56c6c;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>
