<template>
  <div class="user-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h1>用戶管理</h1>
      <p>管理系統用戶和權限設定</p>
    </div>

    <!-- 系統統計 -->
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon user-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.userCount }}</div>
            <div class="stat-label">總用戶數</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon role-icon">
            <el-icon><Key /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.roleCount }}</div>
            <div class="stat-label">權限角色</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon active-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.activeUserCount }}</div>
            <div class="stat-label">活躍用戶</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon permission-icon">
            <el-icon><Lock /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ systemStats.permissionCount }}</div>
            <div class="stat-label">權限功能</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 模組導航 -->
    <div class="module-grid">
      <el-card
        class="module-card"
        shadow="hover"
        @click="navigateToModule('list')"
      >
        <div class="module-content">
          <div class="module-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="module-info">
            <h3>用戶列表</h3>
            <p>管理系統用戶帳號、狀態和基本資訊</p>
          </div>
          <div class="module-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card
        class="module-card"
        shadow="hover"
        @click="navigateToModule('role')"
      >
        <div class="module-content">
          <div class="module-icon">
            <el-icon><Key /></el-icon>
          </div>
          <div class="module-info">
            <h3>權限管理</h3>
            <p>設定用戶角色和功能權限</p>
          </div>
          <div class="module-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快速操作</span>
        </template>
        <div class="action-buttons">
          <el-button type="primary" @click="quickAddUser">
            <el-icon><Plus /></el-icon>
            新增用戶
          </el-button>
          <el-button type="success" @click="quickAddRole">
            <el-icon><Key /></el-icon>
            新增角色
          </el-button>
          <el-button @click="viewUserActivity">
            <el-icon><View /></el-icon>
            查看活動記錄
          </el-button>
          <el-button @click="exportUserData">
            <el-icon><Download /></el-icon>
            匯出用戶資料
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 系統狀態 -->
    <div class="system-status">
      <el-card>
        <template #header>
          <span>系統狀態</span>
        </template>
        <div class="status-grid">
          <div class="status-item">
            <span class="status-label">認證服務</span>
            <el-tag :type="systemStatus.authService ? 'success' : 'danger'">
              {{ systemStatus.authService ? '正常' : '異常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">權限檢查</span>
            <el-tag :type="systemStatus.permissionCheck ? 'success' : 'danger'">
              {{ systemStatus.permissionCheck ? '正常' : '異常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">用戶同步</span>
            <el-tag :type="systemStatus.userSync ? 'success' : 'danger'">
              {{ systemStatus.userSync ? '正常' : '異常' }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">最後更新</span>
            <span class="status-time">{{ systemStatus.lastUpdate }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  UserFilled,
  Key,
  Lock,
  CircleCheck,
  ArrowRight,
  Plus,
  View,
  Download
} from '@element-plus/icons-vue'

const router = useRouter()

// 系統統計數據
const systemStats = reactive({
  userCount: 0,
  roleCount: 0,
  activeUserCount: 0,
  permissionCount: 0
})

// 系統狀態
const systemStatus = reactive({
  authService: true,
  permissionCheck: true,
  userSync: true,
  lastUpdate: '2024-01-20 10:30:00'
})

// 導航到子模組
const navigateToModule = (module: string) => {
  router.push(`/plc-user/${module}`)
}

// 快速操作
const quickAddUser = () => {
  router.push('/plc-user/list?action=add')
}

const quickAddRole = () => {
  router.push('/plc-user/role?action=add')
}

const viewUserActivity = () => {
  ElMessage.info('用戶活動記錄功能開發中...')
}

const exportUserData = () => {
  ElMessage.info('用戶資料匯出功能開發中...')
}

// 載入系統統計
const loadSystemStats = async () => {
  try {
    // 模擬載入統計數據
    systemStats.userCount = 25
    systemStats.roleCount = 8
    systemStats.activeUserCount = 22
    systemStats.permissionCount = 45
  } catch (error) {
    console.error('載入系統統計失敗:', error)
  }
}

// 生命週期
onMounted(async () => {
  await loadSystemStats()
})
</script>

<style scoped>
.user-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.role-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.active-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.permission-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.module-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.module-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.module-info {
  flex: 1;
}

.module-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.module-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.module-arrow {
  color: #9ca3af;
  font-size: 16px;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.system-status .status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  font-weight: 500;
  color: #374151;
}

.status-time {
  color: #6b7280;
  font-size: 14px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .module-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    justify-content: center;
  }

  .module-stats {
    justify-content: center;
  }
}
</style>
