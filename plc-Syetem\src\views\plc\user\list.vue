<template>
  <div class="user-list-container">
    <!-- 搜尋區域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用戶名稱">
          <el-input
            v-model="searchForm.name"
            placeholder="請輸入用戶名稱"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="權限角色">
          <el-select
            v-model="searchForm.roleId"
            placeholder="請選擇權限角色"
            clearable
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="狀態">
          <el-select
            v-model="searchForm.status"
            placeholder="請選擇狀態"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="啟用" value="1" />
            <el-option label="停用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜尋
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工具列 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增用戶
          </el-button>
          <el-button 
            type="success" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchActivate"
          >
            <el-icon><Check /></el-icon>
            批量啟用
          </el-button>
          <el-button 
            type="warning" 
            :disabled="selectedRows.length === 0"
            @click="handleBatchDeactivate"
          >
            <el-icon><Close /></el-icon>
            批量停用
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            匯出
          </el-button>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            匯入
          </el-button>
          <el-button @click="loadUsers">
            <el-icon><Refresh /></el-icon>
            重新載入
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 用戶列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="用戶名稱" min-width="120" />
        <el-table-column prop="account" label="帳號" min-width="120" />
        <el-table-column prop="email" label="電子郵件" min-width="180" />
        <el-table-column prop="permission.name" label="權限角色" min-width="120" />
        <el-table-column label="狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="row.enableState === 1 ? 'success' : 'danger'">
              {{ row.enableState === 1 ? '啟用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              編輯
            </el-button>
            <el-button
              :type="row.enableState === 1 ? 'warning' : 'success'"
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.enableState === 1 ? '停用' : '啟用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              刪除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/編輯對話框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用戶名稱" prop="name">
          <el-input v-model="form.name" placeholder="請輸入用戶名稱" />
        </el-form-item>
        <el-form-item label="帳號" prop="account">
          <el-input v-model="form.account" placeholder="請輸入帳號" />
        </el-form-item>
        <el-form-item label="電子郵件" prop="email">
          <el-input v-model="form.email" placeholder="請輸入電子郵件" />
        </el-form-item>
        <el-form-item label="密碼" prop="password" v-if="!form.id">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="請輸入密碼"
            show-password
          />
        </el-form-item>
        <el-form-item label="權限角色" prop="roleId">
          <el-select v-model="form.roleId" placeholder="請選擇權限角色">
            <el-option
              v-for="role in roleOptions"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="狀態" prop="enableState">
          <el-radio-group v-model="form.enableState">
            <el-radio :label="1">啟用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            確定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Check,
  Close,
  Download,
  Upload
} from '@element-plus/icons-vue'
import { userAPI, roleAPI } from '@/api/plc/user'

// 響應式數據
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const selectedRows = ref([])
const userList = ref([])
const roleOptions = ref([])

// 搜尋表單
const searchForm = reactive({
  name: '',
  roleId: '',
  status: ''
})

// 分頁
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 表單
const form = reactive({
  id: null,
  name: '',
  account: '',
  email: '',
  password: '',
  roleId: '',
  enableState: 1
})

const formRef = ref()

// 表單驗證規則
const formRules = {
  name: [
    { required: true, message: '請輸入用戶名稱', trigger: 'blur' }
  ],
  account: [
    { required: true, message: '請輸入帳號', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '請輸入電子郵件', trigger: 'blur' },
    { type: 'email', message: '請輸入正確的電子郵件格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' },
    { min: 6, message: '密碼長度不能少於6位', trigger: 'blur' }
  ],
  roleId: [
    { required: true, message: '請選擇權限角色', trigger: 'change' }
  ]
}

// 計算屬性
const dialogTitle = computed(() => {
  return form.id ? '編輯用戶' : '新增用戶'
})

// 載入用戶列表
const loadUsers = async () => {
  try {
    loading.value = true
    // 調用真實API載入用戶列表
    const response = await userAPI.getUserList({
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    if (response && response.Detail && response.Detail.StaffMembers) {
      const staffMembers = response.Detail.StaffMembers
      userList.value = staffMembers.map((user: any) => ({
        id: user.StaffId,
        name: user.StaffName,
        account: user.Account,
        email: user.Email,
        enableState: user.EnableState,
        permission: { id: user.RoleId, name: user.RoleName }
      }))

      pagination.total = staffMembers.length
      ElMessage.success(`成功載入 ${userList.value.length} 個用戶`)
    } else {
      userList.value = []
      pagination.total = 0
      ElMessage.warning('未找到用戶數據')
    }
  } catch (error) {
    console.error('載入用戶列表失敗:', error)
    ElMessage.error('載入用戶列表失敗')
  } finally {
    loading.value = false
  }
}

// 載入角色選項
const loadRoleOptions = async () => {
  try {
    // 調用真實API載入角色選項
    const response = await roleAPI.getRoleList()

    if (response && response.Detail && response.Detail.Roles) {
      roleOptions.value = response.Detail.Roles.map((role: any) => ({
        id: role.RoleId,
        name: role.RoleName
      }))

      ElMessage.success(`成功載入 ${roleOptions.value.length} 個角色`)
    } else {
      roleOptions.value = []
      ElMessage.warning('未找到角色數據')
    }
  } catch (error: any) {
    console.error('載入角色選項失敗:', error)
    ElMessage.error(`載入角色選項失敗: ${error.message || '未知錯誤'}`)
    roleOptions.value = []
  }
}

// 搜尋
const handleSearch = () => {
  pagination.currentPage = 1
  loadUsers()
}

// 重置搜尋
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    roleId: '',
    status: ''
  })
  handleSearch()
}

// 新增用戶
const handleAdd = () => {
  Object.assign(form, {
    id: null,
    name: '',
    account: '',
    email: '',
    password: '',
    roleId: '',
    enableState: 1
  })
  dialogVisible.value = true
}

// 編輯用戶
const handleEdit = (row: any) => {
  Object.assign(form, {
    id: row.id,
    name: row.name,
    account: row.account,
    email: row.email,
    password: '',
    roleId: row.permission.id,
    enableState: row.enableState
  })
  dialogVisible.value = true
}

// 提交表單
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    // 調用真實API創建或更新用戶
    if (form.id) {
      // 更新用戶
      await userAPI.updateUser({
        id: form.id,
        name: form.name,
        account: form.account,
        email: form.email,
        roleId: form.roleId,
        enableState: form.enableState
      })
      ElMessage.success('用戶更新成功')
    } else {
      // 創建用戶
      await userAPI.createUser({
        name: form.name,
        account: form.account,
        email: form.email,
        password: form.password,
        roleId: form.roleId,
        enableState: form.enableState
      })
      ElMessage.success('用戶創建成功')
    }

    dialogVisible.value = false
    loadUsers()
  } catch (error) {
    console.error('提交失敗:', error)
  } finally {
    submitting.value = false
  }
}

// 對話框關閉
const handleDialogClose = () => {
  formRef.value?.resetFields()
}

// 切換用戶狀態
const handleToggleStatus = async (row: any) => {
  try {
    const action = row.enableState === 1 ? '停用' : '啟用'
    await ElMessageBox.confirm(
      `確定要${action}用戶 "${row.name}" 嗎？`,
      '確認操作',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success(`用戶${action}成功`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切換狀態失敗:', error)
      ElMessage.error('操作失敗')
    }
  }
}

// 刪除用戶
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除用戶 "${row.name}" 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('用戶刪除成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('刪除失敗:', error)
      ElMessage.error('刪除失敗')
    }
  }
}

// 選擇變更
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量啟用
const handleBatchActivate = async () => {
  try {
    await ElMessageBox.confirm(
      `確定要啟用選中的 ${selectedRows.value.length} 個用戶嗎？`,
      '確認批量啟用',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`成功啟用 ${selectedRows.value.length} 個用戶`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量啟用失敗:', error)
      ElMessage.error('批量啟用失敗')
    }
  }
}

// 批量停用
const handleBatchDeactivate = async () => {
  try {
    await ElMessageBox.confirm(
      `確定要停用選中的 ${selectedRows.value.length} 個用戶嗎？`,
      '確認批量停用',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`成功停用 ${selectedRows.value.length} 個用戶`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量停用失敗:', error)
      ElMessage.error('批量停用失敗')
    }
  }
}

// 匯出
const handleExport = () => {
  ElMessage.info('匯出功能開發中...')
}

// 匯入
const handleImport = () => {
  ElMessage.info('匯入功能開發中...')
}

// 分頁變更
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadUsers()
}

// 生命週期
onMounted(() => {
  loadUsers()
  loadRoleOptions()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.search-card,
.toolbar-card,
.table-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}
</style>
