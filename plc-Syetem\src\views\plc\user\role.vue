<template>
  <div class="role-container">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 角色列表 -->
      <el-tab-pane label="角色列表" name="list">
        <!-- 工具列 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增角色
            </el-button>
            <el-button 
              type="danger" 
              :disabled="selectedRows.length === 0"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量刪除
            </el-button>
          </div>
          <div class="toolbar-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜尋角色名稱"
              clearable
              @keyup.enter="handleSearch"
              style="width: 200px"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button @click="loadRoles">
              <el-icon><Refresh /></el-icon>
              重新載入
            </el-button>
          </div>
        </div>

        <!-- 角色列表 -->
        <el-table
          v-loading="loading"
          :data="filteredRoleList"
          @selection-change="handleSelectionChange"
          stripe
          border
          style="margin-top: 20px"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="角色名稱" min-width="150" />
          <el-table-column label="是否為根權限" width="120">
            <template #default="{ row }">
              <el-tag :type="row.isRoot ? 'danger' : 'primary'">
                {{ row.isRoot ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="權限數量" width="100">
            <template #default="{ row }">
              <el-tag type="info">{{ getPermissionCount(row) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="使用人數" width="100">
            <template #default="{ row }">
              <el-tag type="success">{{ getUserCount(row) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                編輯
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleViewPermissions(row)"
              >
                查看權限
              </el-button>
              <el-button
                type="danger"
                size="small"
                :disabled="row.isRoot"
                @click="handleDelete(row)"
              >
                刪除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 權限設定 -->
      <el-tab-pane label="權限設定" name="permissions">
        <div class="permission-settings">
          <el-alert
            title="權限說明"
            type="info"
            :closable="false"
            style="margin-bottom: 20px"
          >
            <template #default>
              <p>權限分為四種類型：</p>
              <ul>
                <li><strong>讀取(R)</strong>：可以查看數據</li>
                <li><strong>創建(C)</strong>：可以新增數據</li>
                <li><strong>更新(U)</strong>：可以修改數據</li>
                <li><strong>刪除(D)</strong>：可以刪除數據</li>
              </ul>
            </template>
          </el-alert>

          <el-card>
            <template #header>
              <span>系統功能權限</span>
            </template>
            
            <div class="permission-grid">
              <div v-for="feature in featureList" :key="feature.key" class="permission-item">
                <div class="feature-name">{{ feature.name }}</div>
                <div class="permission-checkboxes">
                  <el-checkbox-group v-model="feature.permissions">
                    <el-checkbox label="r">讀取</el-checkbox>
                    <el-checkbox label="c">創建</el-checkbox>
                    <el-checkbox label="u">更新</el-checkbox>
                    <el-checkbox label="d">刪除</el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/編輯角色對話框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="角色名稱" prop="name">
          <el-input v-model="form.name" placeholder="請輸入角色名稱" />
        </el-form-item>
        <el-form-item label="是否為根權限">
          <el-switch
            v-model="form.isRoot"
            :disabled="form.id && form.isRoot"
          />
          <div class="form-tip">根權限擁有所有功能的完整權限</div>
        </el-form-item>
        
        <el-form-item label="功能權限" v-if="!form.isRoot">
          <div class="permission-form">
            <div v-for="feature in permissionFeatures" :key="feature.key" class="permission-row">
              <div class="feature-label">{{ feature.name }}</div>
              <el-checkbox-group v-model="form.permissions[feature.key]">
                <el-checkbox label="r">讀取</el-checkbox>
                <el-checkbox label="c">創建</el-checkbox>
                <el-checkbox label="u">更新</el-checkbox>
                <el-checkbox label="d">刪除</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            確定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 權限詳情對話框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="權限詳情"
      width="600px"
    >
      <div v-if="selectedRole">
        <h4>{{ selectedRole.name }}</h4>
        <el-divider />
        
        <div v-if="selectedRole.isRoot" class="root-permission">
          <el-alert
            title="根權限角色"
            type="warning"
            :closable="false"
          >
            此角色擁有系統所有功能的完整權限
          </el-alert>
        </div>
        
        <div v-else class="permission-details">
          <div v-for="feature in permissionFeatures" :key="feature.key" class="permission-detail-item">
            <div class="feature-name">{{ feature.name }}</div>
            <div class="permission-tags">
              <el-tag
                v-for="permission in getFeaturePermissions(feature.key)"
                :key="permission"
                :type="getPermissionTagType(permission)"
                size="small"
                style="margin-right: 8px"
              >
                {{ getPermissionLabel(permission) }}
              </el-tag>
              <span v-if="getFeaturePermissions(feature.key).length === 0" class="no-permission">
                無權限
              </span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Delete,
  Search,
  Refresh
} from '@element-plus/icons-vue'

// 響應式數據
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const activeTab = ref('list')
const searchKeyword = ref('')
const selectedRows = ref([])
const roleList = ref([])
const selectedRole = ref(null)

// 表單
const form = reactive({
  id: null,
  name: '',
  isRoot: false,
  permissions: {}
})

const formRef = ref()

// 表單驗證規則
const formRules = {
  name: [
    { required: true, message: '請輸入角色名稱', trigger: 'blur' }
  ]
}

// 權限功能列表
const permissionFeatures = [
  { key: 'alarm', name: '警報管理' },
  { key: 'database', name: '數據中心' },
  { key: 'tags', name: '標籤管理' },
  { key: 'user', name: '用戶管理' },
  { key: 'system', name: '系統管理' },
  { key: 'notify', name: '通知系統' },
  { key: 'schedule', name: '排程系統' },
  { key: 'gui', name: 'GUI監控' }
]

// 功能列表（用於權限設定頁面）
const featureList = ref(
  permissionFeatures.map(feature => ({
    ...feature,
    permissions: []
  }))
)

// 計算屬性
const dialogTitle = computed(() => {
  return form.id ? '編輯角色' : '新增角色'
})

const filteredRoleList = computed(() => {
  if (!searchKeyword.value) {
    return roleList.value
  }
  return roleList.value.filter(role => 
    role.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 載入角色列表
const loadRoles = async () => {
  try {
    loading.value = true
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模擬數據
    roleList.value = [
      {
        id: '1',
        name: '超級管理員',
        isRoot: true,
        permissions: {}
      },
      {
        id: '2',
        name: '操作員',
        isRoot: false,
        permissions: {
          alarm: ['r', 'u'],
          database: ['r'],
          tags: ['r', 'c', 'u']
        }
      },
      {
        id: '3',
        name: '觀察員',
        isRoot: false,
        permissions: {
          alarm: ['r'],
          database: ['r'],
          tags: ['r']
        }
      }
    ]
  } catch (error) {
    console.error('載入角色列表失敗:', error)
    ElMessage.error('載入角色列表失敗')
  } finally {
    loading.value = false
  }
}

// 搜尋
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中實現
}

// 新增角色
const handleAdd = () => {
  Object.assign(form, {
    id: null,
    name: '',
    isRoot: false,
    permissions: {}
  })
  
  // 初始化權限
  permissionFeatures.forEach(feature => {
    form.permissions[feature.key] = []
  })
  
  dialogVisible.value = true
}

// 編輯角色
const handleEdit = (row: any) => {
  Object.assign(form, {
    id: row.id,
    name: row.name,
    isRoot: row.isRoot,
    permissions: { ...row.permissions }
  })
  
  // 確保所有功能都有權限數組
  permissionFeatures.forEach(feature => {
    if (!form.permissions[feature.key]) {
      form.permissions[feature.key] = []
    }
  })
  
  dialogVisible.value = true
}

// 提交表單
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(form.id ? '角色更新成功' : '角色創建成功')
    dialogVisible.value = false
    loadRoles()
  } catch (error) {
    console.error('提交失敗:', error)
  } finally {
    submitting.value = false
  }
}

// 對話框關閉
const handleDialogClose = () => {
  formRef.value?.resetFields()
}

// 查看權限
const handleViewPermissions = (row: any) => {
  selectedRole.value = row
  permissionDialogVisible.value = true
}

// 刪除角色
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除角色 "${row.name}" 嗎？此操作不可恢復。`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('角色刪除成功')
    loadRoles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('刪除失敗:', error)
      ElMessage.error('刪除失敗')
    }
  }
}

// 選擇變更
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量刪除
const handleBatchDelete = async () => {
  try {
    const nonRootRoles = selectedRows.value.filter(role => !role.isRoot)
    if (nonRootRoles.length === 0) {
      ElMessage.warning('無法刪除根權限角色')
      return
    }
    
    await ElMessageBox.confirm(
      `確定要刪除選中的 ${nonRootRoles.length} 個角色嗎？此操作不可恢復。`,
      '確認批量刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模擬 API 調用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`成功刪除 ${nonRootRoles.length} 個角色`)
    loadRoles()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量刪除失敗:', error)
      ElMessage.error('批量刪除失敗')
    }
  }
}

// 獲取權限數量
const getPermissionCount = (role: any) => {
  if (role.isRoot) return '全部'
  
  let count = 0
  Object.values(role.permissions || {}).forEach((perms: any) => {
    count += perms.length
  })
  return count
}

// 獲取使用人數（模擬）
const getUserCount = (role: any) => {
  // 模擬數據
  const userCounts = {
    '1': 1, // 超級管理員
    '2': 5, // 操作員
    '3': 3  // 觀察員
  }
  return userCounts[role.id] || 0
}

// 獲取功能權限
const getFeaturePermissions = (featureKey: string) => {
  if (!selectedRole.value || selectedRole.value.isRoot) return []
  return selectedRole.value.permissions[featureKey] || []
}

// 獲取權限標籤類型
const getPermissionTagType = (permission: string) => {
  const types = {
    'r': 'info',
    'c': 'success',
    'u': 'warning',
    'd': 'danger'
  }
  return types[permission] || 'info'
}

// 獲取權限標籤
const getPermissionLabel = (permission: string) => {
  const labels = {
    'r': '讀取',
    'c': '創建',
    'u': '更新',
    'd': '刪除'
  }
  return labels[permission] || permission
}

// 生命週期
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.role-container {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.permission-settings {
  padding: 20px;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.permission-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.feature-name {
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
}

.permission-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-form {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.permission-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.permission-row:last-child {
  border-bottom: none;
}

.feature-label {
  width: 120px;
  font-weight: 500;
  color: #606266;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

.root-permission {
  text-align: center;
  padding: 20px;
}

.permission-details {
  max-height: 400px;
  overflow-y: auto;
}

.permission-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.permission-detail-item:last-child {
  border-bottom: none;
}

.permission-detail-item .feature-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 0;
}

.permission-tags {
  display: flex;
  align-items: center;
}

.no-permission {
  color: #c0c4cc;
  font-size: 12px;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .permission-grid {
    grid-template-columns: 1fr;
  }

  .permission-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .feature-label {
    width: auto;
  }
}
</style>
