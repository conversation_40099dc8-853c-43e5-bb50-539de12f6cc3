<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLC 標籤編輯對話框測試</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #409eff;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #409eff;
            margin-top: 0;
        }
        .button-group {
            margin: 15px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #409eff;
            color: white;
        }
        .btn-success {
            background-color: #67c23a;
            color: white;
        }
        .btn-warning {
            background-color: #e6a23c;
            color: white;
        }
        .btn-danger {
            background-color: #f56c6c;
            color: white;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 5px;
            display: inline-block;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #409eff;
            border-radius: 8px;
            margin-top: 20px;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 PLC 標籤編輯對話框測試工具</h1>
            <p>用於測試和調試標籤管理頁面的編輯對話框功能</p>
        </div>

        <div class="test-section">
            <h3>📋 測試步驟</h3>
            <ol>
                <li>點擊下方按鈕打開 PLC 系統</li>
                <li>使用帳號 <strong><EMAIL></strong> 密碼 <strong>111111</strong> 登入</li>
                <li>導航到 <strong>測點管理</strong> 頁面</li>
                <li>點擊任一標籤的 <strong>編輯</strong> 按鈕</li>
                <li>檢查編輯對話框是否正常顯示</li>
                <li>驗證警報狀態是否有三個選項：停用、一般警報、重要警報</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🚀 快速操作</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="openPLCSystem()">
                    🌐 打開 PLC 系統
                </button>
                <button class="btn btn-success" onclick="openTagManagement()">
                    📊 直接打開標籤管理
                </button>
                <button class="btn btn-warning" onclick="runDebugScript()">
                    🔍 運行調試腳本
                </button>
                <button class="btn btn-danger" onclick="clearResults()">
                    🗑️ 清除結果
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 測試結果</h3>
            <div id="testResults" class="result">等待測試結果...</div>
        </div>

        <div class="test-section">
            <h3>🖥️ PLC 系統預覽</h3>
            <div class="iframe-container">
                <iframe id="plcFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        let resultDiv = document.getElementById('testResults');
        let plcFrame = document.getElementById('plcFrame');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'warning';
            
            resultDiv.innerHTML += `[${timestamp}] <span class="status ${statusClass}">${type.toUpperCase()}</span> ${message}\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function openPLCSystem() {
            log('正在打開 PLC 系統...', 'info');
            plcFrame.src = 'http://localhost:8849/';
            
            plcFrame.onload = function() {
                log('✅ PLC 系統已載入', 'success');
                log('請使用帳號: <EMAIL> 密碼: 111111 登入', 'info');
            };
            
            plcFrame.onerror = function() {
                log('❌ 無法載入 PLC 系統，請確認開發服務器是否運行', 'error');
            };
        }

        function openTagManagement() {
            log('正在打開標籤管理頁面...', 'info');
            plcFrame.src = 'http://localhost:8849/#/plc-tags/tag';
            
            plcFrame.onload = function() {
                log('✅ 標籤管理頁面已載入', 'success');
                log('請點擊任一標籤的編輯按鈕測試對話框', 'info');
            };
        }

        function runDebugScript() {
            log('正在運行調試腳本...', 'info');
            
            try {
                // 嘗試在 iframe 中執行調試腳本
                const frameWindow = plcFrame.contentWindow;
                if (frameWindow && frameWindow.document) {
                    // 注入調試腳本
                    const script = frameWindow.document.createElement('script');
                    script.textContent = `
                        // 檢查對話框狀態
                        function checkDialogs() {
                            const dialogs = document.querySelectorAll('.el-dialog');
                            const overlays = document.querySelectorAll('.el-overlay');
                            
                            console.log('找到對話框:', dialogs.length);
                            console.log('找到遮罩:', overlays.length);
                            
                            dialogs.forEach((dialog, i) => {
                                const style = window.getComputedStyle(dialog);
                                console.log('對話框', i, '狀態:', {
                                    display: style.display,
                                    visibility: style.visibility,
                                    opacity: style.opacity
                                });
                            });
                            
                            return { dialogs: dialogs.length, overlays: overlays.length };
                        }
                        
                        // 強制顯示對話框
                        function forceShowDialog() {
                            const dialogs = document.querySelectorAll('.el-dialog');
                            const overlays = document.querySelectorAll('.el-overlay');
                            
                            dialogs.forEach(dialog => {
                                dialog.style.display = 'block';
                                dialog.style.visibility = 'visible';
                                dialog.style.opacity = '1';
                                dialog.style.zIndex = '9999';
                            });
                            
                            overlays.forEach(overlay => {
                                overlay.style.display = 'block';
                                overlay.style.visibility = 'visible';
                                overlay.style.opacity = '1';
                            });
                            
                            console.log('已嘗試強制顯示所有對話框');
                        }
                        
                        // 執行檢查
                        const result = checkDialogs();
                        window.parent.postMessage({
                            type: 'debug-result',
                            data: result
                        }, '*');
                    `;
                    
                    frameWindow.document.head.appendChild(script);
                    log('✅ 調試腳本已注入', 'success');
                } else {
                    log('❌ 無法訪問 iframe 內容，可能是跨域問題', 'error');
                }
            } catch (error) {
                log('❌ 調試腳本執行失敗: ' + error.message, 'error');
            }
        }

        function clearResults() {
            resultDiv.innerHTML = '結果已清除...\n';
        }

        // 監聽來自 iframe 的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'debug-result') {
                const data = event.data.data;
                log(`🔍 調試結果: 找到 ${data.dialogs} 個對話框, ${data.overlays} 個遮罩`, 'info');
                
                if (data.dialogs === 0) {
                    log('⚠️ 未找到對話框元素，可能頁面尚未載入完成', 'warning');
                } else {
                    log('✅ 對話框元素存在，請檢查顯示狀態', 'success');
                }
            }
        });

        // 初始化
        log('🚀 PLC 標籤編輯對話框測試工具已準備就緒', 'success');
        log('開發服務器地址: http://localhost:8849/', 'info');
    </script>
</body>
</html>
