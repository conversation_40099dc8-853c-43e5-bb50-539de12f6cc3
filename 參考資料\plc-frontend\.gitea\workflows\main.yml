name: Deploy

on:
  push:
    branches:
      - develop

jobs:
  build:
    runs-on: ubuntu-latest
    # container:
    #   image: docker:latest
    steps:
      - uses: actions/checkout@v2

      - name: stop_containers
        run: |
          CONTAINERS=$(docker ps -q --filter ancestor=frontend:latest)
          if [ -z "$CONTAINERS" ]; then
            echo "No running containers using the specified image found."
          else
            for CONT<PERSON><PERSON><PERSON> in $CONTAINERS; do
              echo "Stopping container $CONTAINER..."
              docker rm $CONTAINER
            done
          fi

      - name: remove_image
        run: |
          if docker image inspect frontend:latest &> /dev/null; then
            echo "Removing Docker image frontend:latest..."
            docker rmi frontend:latest
          else
            echo "Docker image frontend:latest not found."
          fi

      - name: Build
        run: |
          docker build -t frontend:latest .

  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: check_network
        run: |
          if docker network inspect app-network &> /dev/null; then
            echo "Network app-network exists."
            docker network rm app-network
          else
           echo "Network app-network does not exist. No action required."
          fi

      - name: Create app-network
        run: docker network create app-network

      - name: Run frontend container
        run: |
          docker run -d \
            --name frontend \
            -p 8080:80 \
            --network app-network \
            frontend:latest 
          docker cp nginx.conf frontend:/etc/nginx/nginx.conf
          docker exec frontend sh -c "rm -f /etc/nginx/conf.d/default.conf && nginx -s reload"
