# ⚠️ 專案啟動環境說明

> **重要：每一次啟動、開發、部署本專案前，所有人員都必須優先閱讀本說明，並嚴格遵守下列 Node.js 與 npm 版本條件，否則請勿進行任何操作。**

本專案必須使用下列 Node.js 與 npm 版本，否則可能無法正常啟動：

- Node.js：**18.16.1**
- npm：**9.5.1**

## 啟動步驟

```bash
nvm install 18.16.1
nvm alias default 18.16.1
nvm use 18.16.1
node -v   # ✅ v18.16.1
npm -v    # ✅ v9.5.1
npm install
npm run serve
```

> 請務必確認 `node -v` 與 `npm -v` 版本正確，否則請勿進行開發或部署。


GITHUB
https://github.com/ococomtw/plc-frontend



# UIUX 不可異動規則
- 任何 UI 或 UX 相關的元件、樣式、版面、互動設計，均不可異動、不可調整。
- 包含但不限於：色彩、字型、間距、按鈕樣式、版面配置、動畫、響應式設計、使用者流程等。
- 若有任何 UIUX 相關需求，需經專案負責人書面同意。 