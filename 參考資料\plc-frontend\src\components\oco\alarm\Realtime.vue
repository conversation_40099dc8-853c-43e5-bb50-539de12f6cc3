<template>
  <RealTime>
    <sdModal
      v-if="CCTVModal"
      :visible="CCTVModal"
      :onCancel="closeCCTVModal"
      :width="1000"
    >
      <cctvStream :cctv="currCCTV"></cctvStream>
    </sdModal>
    <a-collapse
      :activeKey="collapsed"
      type="card"
      accordion
      @change="changePanel"
    >
      <a-collapse-panel key="1" class="custom-tab" :show-arrow="false">
        <template #header>
          <div class="custom-header">警報</div>
        </template>
        <a-button
          v-permission="{ permission: 'update', module: 'alarm-realtime' }"
          class="check-button"
          type="primary"
          ghost
          @click="checkAlarm"
        >
          確認全部
        </a-button>
        <a-checkbox :checked="pause" @change="onPauseChanged"
          >暫停更新</a-checkbox
        >
        <a-table
          :data-source="tableData"
          :columns="columns"
          :pagination="false"
          :scroll="{ x: 'max-content', y: windowWidth < 768 ? 100 : 300 }"
          :row-class-name="getRowClassName"
        />
      </a-collapse-panel>
    </a-collapse>
  </RealTime>
</template>
<script src="./index.js"></script>
