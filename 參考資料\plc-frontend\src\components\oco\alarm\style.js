import Styled from "vue3-styled-components";
const RealTime = Styled.div`
   
    max-width: 100%;
    position:fixed;
    bottom:0px;
    z-index:10;
    .text-alert{
        color:${({ theme }) => theme["error-color"]};
    }

    .text-checked{
        color:${({ theme }) => theme["success-color"]};
    }

    .text-normal{
        color:${({ theme }) => theme["info-color"]};
    }

    table{
        white-space: nowrap;
        border-radius:0px;
    }

    .ant-table-container table > thead > tr:first-child th:first-child {
        border-top-left-radius: 0px;
    }
    
    .ant-table-thead > tr > th {
        background-color: ${({ theme }) => theme["primary-color"]};
        color:white;
    }
    

    .ant-collapse{
        border-color:${({ theme }) => theme["primary-color"]};
        border-top:0px;
        background:transparent;
    }

    .ant-collapse-header{
        border:0px;
        padding:0px !important;
    }
    .ant-collapse-content{
        border-bottom:0px;
        border-color:${({ theme }) => theme["primary-color"]};
    }

    .ant-collapse-content-box{
        padding:0;
        color:${({ theme }) => theme["primary-color"]};
    }

    .custom-header{
        display:flex;
        padding:0.5rem 1rem;
        border:1px solid ${({ theme }) => theme["primary-color"]};
        border-bottom:0;
        border-left:0;
        border-radius:5px 5px 0 0;
        background:${({ theme }) => theme["primary-color"]};
        color:${({ theme }) => theme["white-color"]};
    }

    .check-button{
        margin:1rem 1rem 1rem 1rem;
        height:40px;
    }

    
`;

const ActionSpan = Styled.div`
    display:flex;
    button{
        margin:0 0.5rem;
        padding:0;
        height:30px;
        width:40px;
        .unicon{
            margin-top:2px;
            svg{
                color:${({ theme }) => theme["primary-color"]};
                fill:${({ theme }) => theme["primary-color"]};
            }
        }
    }
    
`;
export { RealTime, ActionSpan };
