import * as signalR from "@microsoft/signalr";
import { getItem } from "@/utility/localStorageControl";
import { notification } from "ant-design-vue";
export function useAlarmConnection(callback) {
  // 測試使用 /api/AlarmSummary 路徑，看看是否能通過代理正確轉發
  const signalRUrl = "/api/AlarmSummary";

  const connection = new signalR.HubConnectionBuilder()
    .withUrl(signalRUrl, {
      skipNegotiation: true,
      transport: signalR.HttpTransportType.WebSockets,
    })
    .withAutomaticReconnect({
      nextRetryDelayInMilliseconds: () => 5000,
    })
    .build();
  connection.start().catch((err) => console.log(err));

  connection.on("GetConnectedId", async function (clientId) {
    const customerId = getItem("customer_id");
    if (!customerId) {
      console.error("客戶 ID 未找到，請重新登入");
      notification.error({
        message: "連接失敗",
        description: "客戶 ID 未找到，請重新登入系統",
        duration: 5,
      });
      return;
    }

    const targetObj = {
      CustomerId: customerId,
      ClientId: clientId,
    };
    const json = JSON.stringify(targetObj);
    try {
      await connection.invoke(
        "FromClientSendConnectedIdAndCustomerIdAsync",
        json
      );
    } catch (err) {
      console.error("警報連接錯誤:", err);
      notification.error({
        message: "警報連接失敗",
        description: "無法建立警報連接，請檢查網路或重新登入",
        duration: 5,
      });
    }
  });

  connection.on("ToClientSendAlarmSummaryJson", callback);

  connection.on("CustomerIdError", function (errorText) {
    console.error("客戶代碼錯誤:", errorText);
  });

  connection.onclose(async () => {
    speechSynthesis.cancel();
  });

  return { connection };
}
