import * as signalR from "@microsoft/signalr";
import { getItem } from "@/utility/localStorageControl";
import { notification } from "ant-design-vue";

// 測試使用 /api/Cctv 路徑，看看是否能通過 nginx 代理正確轉發
const signalRUrl = "/api/Cctv";

const connection = new signalR.HubConnectionBuilder()
  .withUrl(signalRUrl, {
    skipNegotiation: true,
    transport: signalR.HttpTransportType.WebSockets,
  })
  .withAutomaticReconnect({
    nextRetryDelayInMilliseconds: () => 5000,
  })
  .build();

export async function useCCTVConnection() {
  if (connection) {
    try {
      // 先嘗試啟動連接
      await connection.start();
      console.log("CCTV WebSocket 連接成功");

      const customerId = getItem("customer_id");
      if (customerId) {
        try {
          // 確保 customerId 是正確的 Guid 格式
          console.log("嘗試註冊 CCTV 客戶:", customerId);
          await connection.invoke("registerClientAsync", customerId, 999);
          console.log("CCTV 連接註冊成功");
        } catch (err) {
          console.error("CCTV 連接註冊失敗:", err);
          notification.error({
            message: "CCTV 連接失敗",
            description: "無法註冊 CCTV 連接，可能是客戶 ID 格式不正確",
            duration: 5,
          });
        }
      } else {
        console.error("客戶 ID 未找到，請重新登入");
        notification.error({
          message: "連接失敗",
          description: "客戶 ID 未找到，請重新登入系統",
          duration: 5,
        });
      }
    } catch (err) {
      console.error("CCTV WebSocket 連接失敗:", err);
      notification.error({
        message: "CCTV WebSocket 連接失敗",
        description: "無法連接到 CCTV 服務器，請檢查網路連接",
        duration: 5,
      });
    }

    connection.onclose(async () => {
      speechSynthesis.cancel();
    });
  }

  return { connection };
}

export function getCCTVImage(callback) {
  connection.on("receiveCctvImage", callback);
}
