//import { nextTick } from 'vue'
import { ref, nextTick } from 'vue'
import { useStore } from 'vuex'
export function useUpdate () {
  const { dispatch, state } = useStore()

  const updateAllTag = (sources, allTags) => {
    allTags.forEach(el => {
      if (el._objects) {
        updateAllTag(sources, el.getObjects())
      } else {
        const tar = sources.find(source => source.TagId === el.tagId)
        el.set({ stroke: el.normalStroke, fill: el.normalFill })

        if (tar && tar.TagRealvalue && !el.showProperty) {
          if (!el.showProperty) {
            el.set({ text: tar.TagRealvalue })
            // 強制更新 canvas
            el.canvas.renderAll()
          }

          //確認此物件是否有規則,如果沒有規則就返回
          const rules = el.rule
          if (!rules) return

          //組成判斷式
          //•	例如：
          //•	eval("5>=3") 返回 true 給 isStop
          //•	eval("5<3") 返回 false 給 isStop
          const isStop = rules.stopValue
            ? eval(`${tar.TagRealvalue}${rules.stopValue}`)
            : false
          console.log('tar.TagRealvalue', tar.TagRealvalue)
          console.log('rules.stopValue', rules.stopValue)
          console.log('isStop', isStop)

          // tar.TagRealvalue == rules.stopValue == 0 則 isStop = True
          if (isStop) {
            // stop
            if (rules.stopAction === '1' && rules.stopValue) {
              //el.opacity = 0; //隱藏物件
              el.visible = false
            } else if (rules?.stopAction === '2' && !rules.stopValue) {
              el.set({
                stroke: rules.stopStroke,
                fill: rules.stopFill
              })
            }
          } else if (rules.stopAction === '1') {
            //el.opacity = 1; //顯示物件
            el.visible = true
          } else if (
            tar.AlarmState &&
            tar.AlarmState !== 3 &&
            tar.AlarmState !== 4
          ) {
            // alarm
            const alarmRes = checkAlarmStyle({
              rules,
              type: tar.TagType,
              state: tar.AlarmState,
              priority: tar.AlarmPriority
            })
            if (alarmRes) {
              const { stroke, fill } = alarmRes
              el.set({ stroke, fill })
            }
          }
        }
        // 在每個物件更新後強制重新渲染
        nextTick(() => {
          if (el.canvas) {
            el.canvas.renderAll()
          }
        })
      }
      // 确保触发 Canvas 更新
      //nextTick(() => {
      //  el.canvas.requestRenderAll()
      //})     
    })
    // 最後再次確保整個 canvas 被更新
    nextTick(() => {
      if (allTags[0] && allTags[0].canvas) {
        allTags[0].canvas.renderAll()
      }
    })
    //canvas.requestRenderAll();
  }

  function checkAlarmStyle ({ rules, type, state, priority }) {
    if (type === 1) {
      // analogy
      if (state === 1) {
        // alarm
        if (rules.HHAlarmStatus && priority === 3) {
          return {
            stroke: rules.HHAlarmStroke,
            fill: rules.HHAlarmFill
          }
        } else if (rules.HIAlarmStatus && priority === 2) {
          return {
            stroke: rules.HIAlarmStroke,
            fill: rules.HIAlarmFill
          }
        } else if (rules.LLAlarmStatus && priority === 1) {
          return {
            stroke: rules.LLAlarmStroke,
            fill: rules.LLAlarmFill
          }
        } else if (rules.LOAlarmStatus && priority === 0) {
          return {
            stroke: rules.LOAlarmStroke,
            fill: rules.LOAlarmFill
          }
        }
      } else if (state === 2) {
        // check
        if (rules.HHCheckStatus && priority === 3) {
          return {
            stroke: rules.HHCheckStroke,
            fill: rules.HHCheckFill
          }
        } else if (rules.HICheckStatus && priority === 2) {
          return {
            stroke: rules.HICheckStroke,
            fill: rules.HICheckFill
          }
        } else if (rules.LLCheckStatus && priority === 1) {
          return {
            stroke: rules.LLCheckStroke,
            fill: rules.LLCheckFill
          }
        } else if (rules.LOCheckStatus && priority === 0) {
          return {
            stroke: rules.LOCheckStroke,
            fill: rules.LOCheckFill
          }
        }
      }
    } else if (type === 0) {
      // digital
      if (state === 1) {
        // alarm
        if (rules.digAlarmStatus && priority === 1) {
          return {
            stroke: rules.digAlarmStroke,
            fill: rules.digAlarmFill
          }
        }
      } else if (state === 2) {
        // check
        if (rules.digCheckStatus && priority === 1) {
          return {
            stroke: rules.digCheckStroke,
            fill: rules.digCheckFill
          }
        }
      }
    }
    return
  }

  const setNormalStyle = object => {
    if (object._objects) {
      object._objects.forEach(el => {
        setNormalStyle(el)
      })
    } else {
      object.set('normalStroke', object.stroke)
      object.set('normalFill', object.fill)
    }
  }

  const showPropsData = ref()
  const getAllPropTags = async canvasJSON => {
    const allTags = []
    for (let i = 0; i < canvasJSON.objects.length; i++) {
      if (canvasJSON.objects[i].showProperty) {
        allTags.push(canvasJSON.objects[i].tagId)
      }
    }
    if (allTags.length > 0) {
      const res = await dispatch('tags/fetchAdditionProps', allTags)
      showPropsData.value = res
    }
  }

  const setCanvasString = async symbolList => {
    if (state.gui.guiDetail.DataContentJson) {
      const content = JSON.parse(
        JSON.parse(state.gui.guiDetail.DataContentJson)
      )

      await getAllPropTags(content)
      const callback = (obj, symbolVars) => {
        if (obj.objects) {
          obj.objects.forEach(el => {
            callback(el, symbolVars)
          })
        } else {
          if (obj.symbolVar) {
            obj.tagId = symbolVars.find(el => el.name === obj.symbolVar).value
          }
        }
      }
      for (let i = 0; i < content.objects.length; i++) {
        if (content.objects[i].symbolId) {
          const symbolData = symbolList.find(
            symbol => symbol.id === content.objects[i].symbolId
          )
          if (symbolData) {
            const symbolContent = JSON.parse(symbolData.content)
            const props = {
              ...content.objects[i],
              objects: symbolContent.objects,
              width: symbolContent.width,
              height: symbolContent.height,
              symbolVars: symbolData.allSymbolVars.map(el => {
                const hasValue = content.objects[i].symbolVars.find(
                  symbolVar => symbolVar.name === el
                )
                if (hasValue) {
                  return { name: el, value: hasValue.value }
                } else {
                  return { name: el, value: null }
                }
              })
            }
            callback(props, props.symbolVars)
            content.objects[i] = props
          }
        }
      }  
      return content
    } else {
      return {}
    }
  }

  const setAdditionProperty = object => {
    if (object._objects) {
      object._objects.forEach(el => {
        setAdditionProperty(el)
      })
    } else {
      if (object.showProperty) {
        const tar = showPropsData.value[object.tagId]
        if (tar) {
          object.set({ text: tar[object.showProperty] || '屬性未設定' })
        }
      }
    }
  }

  return {
    updateAllTag,
    setNormalStyle,
    setCanvasString,
    getAllPropTags,
    setAdditionProperty
  }
}
