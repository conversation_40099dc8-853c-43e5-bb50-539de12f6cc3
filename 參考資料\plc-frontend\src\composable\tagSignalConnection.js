import * as signalR from "@microsoft/signalr";
export function useTagSignalConnection(id, callback) {
  // 在開發環境中直接連接到後端，在生產環境中使用相對路徑
  const signalRUrl = process.env.NODE_ENV === 'development'
    ? `${process.env.VUE_APP_API_ENDPOINT}PageTag`
    : "/api/PageTag";

  const connection = new signalR.HubConnectionBuilder()
    .withUrl(signalRUrl, {
      skipNegotiation: true,
      transport: signalR.HttpTransportType.WebSockets,
    })
    .withAutomaticReconnect({
      nextRetryDelayInMilliseconds: () => 5000,
    })
    .build();
  connection.start().catch((err) => console.log(err));

  connection.on("GetConnectedId", async function (clientId) {
    const targetObj = {
      PageId: id,
      ClientId: clientId,
    };
    const json = JSON.stringify(targetObj);
    try {
      await connection.invoke("FromClientSendConnectedIdAndPageIdAsync", json);
    } catch (err) {
      console.error(err);
    }
  });

  connection.on("ToClientPageTagJson", callback);

  connection.on("PageIdError", function (errorText) {
    console.log(errorText);
  });

  connection.onclose(async () => {
    speechSynthesis.cancel();
  });

  return { connection };
}
