﻿using System.Security.Authentication;
using Oco.Core.ErrException;
using Oco.Core.Extend;
using Oco.Product_002.Model.DataBase.DbFactoreyMethod;
using TagImportAndExport;
using TagImportAndExport.ElementPocos;
using TagImportAndExport.Message;

namespace WinFormImport
{
    public partial class MyForm : Form
    {
        readonly Config? _config = null;
        readonly ImportFile? _importFile = null;
        /// <summary>
        /// 操入介面, CustomerId 與 CustormerName 的分隔符號
        /// </summary>
        const string CustomerIdNameSeparator = ",";
        public MyForm(Config config, ImportFile importFile)
        {
            InitializeComponent();
            _config = config;
            _importFile = importFile;

        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            CheckForIllegalCrossThreadCalls = false;
            await SetCustomerIdListAsync();
        }

        /// <summary>
        /// 開啟要選擇檔案的對話框
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            opfDlgImport.FileName = "";
            opfDlgImport.ShowDialog();
        }

        /// <summary>
        /// 選定了檔案
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void opfDlgImport_FileOk(object sender, System.ComponentModel.CancelEventArgs e)
        {
            var f = opfDlgImport.FileNames == null ? default(string) : opfDlgImport.FileNames[0];
            txtbxFileName.Text = f;
        }

        private void txtbxFileName_TextChanged(object sender, EventArgs e)
        {
            if (txtbxFileName.Text.Trim() == string.Empty)
            {
                btnImport.Enabled = false;
            }
            else
            {
                btnImport.Enabled = true;
            }
        }

        private async void btnRefreshCustomerIdList_Click(object sender, EventArgs e)
        {
            await SetCustomerIdListAsync();

        }

        /// <summary>
        /// 設定 Customer id 清單
        /// </summary>
        /// <returns></returns>
        private async Task SetCustomerIdListAsync()
        {
            try
            {
                grpBx1.Enabled = false;
                using var db = DBReader.CreateDB();
                var customerList = await Customer.GetCustomerIdArrayAsync(db)!;
                CmbxCustomerId.Items.Clear();
                CmbxCustomerId.Items.AddRange(customerList.Select(m => (m.CustomerId) + CustomerIdNameSeparator + "(" + m.CustomerName + ")").ToArray());
                if (CmbxCustomerId.Items.Count == 0)
                {
                    _ = MessageBox.Show("請設定 Customer Id");
                    _ = CmbxCustomerId.Focus();
                }
                else
                {
                    CmbxCustomerId.SelectedIndex = 0;
                    grpBx1.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                _ = MessageBox.Show($"無法取得Customer Id({ex.Message})");
            }
        }

        public bool Doing = false;
        void DoImportOrExport()
        {
            var StartTime = DateTime.Now;
            txtBxNote.Text = "";
            while (true)
            {
                if (!Doing) { break; }
                var thisTime = DateTime.Now;
                var diff = thisTime.Subtract(StartTime).Seconds;
                lblCounter.Text = diff.ToString("#,000");
                Thread.Sleep(900);
                Application.DoEvents();
            }
        }


        /// <summary>
        /// 匯入檔案. 開始
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void btnImport_Click(object sender, EventArgs e)
        {
            var dr = MessageBox.Show($"本項作業結果不可逆,確定要匯入測點資料至 {CmbxCustomerId.Text} 用戶?", "", MessageBoxButtons.OKCancel);
            if (dr != DialogResult.OK)
            {
                return;
            }
            Doing = true;
            lblCounter.Visible = true;
            var th = new Thread(DoImportOrExport);
            th.Start();
            await Task.Run(async () =>
            {
                grpBx1.Enabled = false;
                ErrMessage.ErrMessageDic = [];
                var import = _importFile!;

                #region 整理原始資料
                try
                {
                    using (var db = DBReader.CreateDB())
                    {
                        // 檢查檔案是否存在
                        try
                        {
                            import.CheckIfFileExist(txtbxFileName.Text);
                        }
                        catch (ImportTagFileNotExistException ex)
                        {
                            throw new ImportTagFileNotExistException(ex.Message + $" (檔案名稱：{txtbxFileName.Text})");
                        }

                        // 讀入原始資料
                        try
                        {
                            import.OriginalFileFieldListList = await import.ReadOriginalFileToFieldListListAsync();
                        }
                        catch (EmptyImportTagFileException )
                        {
                            throw;
                        }
                        catch (Exception) { throw; }
                        var customerId = CmbxCustomerId.Text.Split(new string[] { CustomerIdNameSeparator }, StringSplitOptions.RemoveEmptyEntries)[0];
                        // 設定Customer 資料
                        Customer? customerInfo=null;
                        try
                        {
                            customerInfo = await import.SetCustomerInfoAsync(customerId, db);
                        }
                        catch (Exception ex) 
                        {
                            throw;
                        }
                         import.SetCustomerInfo(customerInfo!);
                    }
                }
                catch (Exception ex)
                {
                    Doing = false;
                    txtBxNote.Text = ex.Message;
                    txtBxNote.ForeColor = Color.Red;
                    _ = MessageBox.Show(ex.Message);
                    grpBx1.Enabled = true;
                    lblCounter.Visible = false;
                    return;
                }
                try
                {
                    // 設定各元件的始資料 POCO
                    import.SetOriginalPocos();
                }
                catch (Exception ex)
                {
                    Doing = false;
                    txtBxNote.Text = ex.Message;
                    txtBxNote.ForeColor = Color.Red;
                    _ = MessageBox.Show(ex.Message);
                    grpBx1.Enabled = true;
                    lblCounter.Visible = false;
                    return;
                }

                #endregion


                if (ErrMessage.ErrMessageDic.Count > 0)
                {
                    var path = _config!.LogDir;
                    var fullName = _config.FullLogFileName;
                    // 資料讀入階段..

                    var importLog = new ImportLog(path!, fullName);
                    await importLog.WriteStageLogAsync("資料讀入階段", ErrMessage.ErrMessageDic);
                    Doing = false;
                    txtBxNote.Text = fullName;
                    txtBxNote.ForeColor = Color.Red;
                    _ = MessageBox.Show("於資料讀入階段發現問題無法匯入，請參考: " + fullName);
                    grpBx1.Enabled = true;
                    lblCounter.Visible = false;
                    return;
                }
                #region  將資料設定為符合 資料庫模型的 資料
                try
                {
                    _ = await import.SetFinalPocosAsync();
                }
                catch (Exception ex)
                {
                    Doing = false;
                    txtBxNote.Text = ex.Message;
                    txtBxNote.ForeColor = Color.Red;
                    _ = MessageBox.Show(ex.Message);
                    grpBx1.Enabled = true;
                    lblCounter.Visible = false;
                    return;
                }
                // 資料匯入階段如果有一個(含)以下的錯誤
                if (ErrMessage.ErrMessageDic.Count > 0)
                {
                    var path = _config!.LogDir;
                    var fullName = _config.FullLogFileName;
                    // 資料讀入階段..

                    var importLog = new ImportLog(path!, fullName);
                    await importLog.WriteStageLogAsync("資料剖析階段", ErrMessage.ErrMessageDic);
                    Doing = false;
                    txtBxNote.Text = fullName;
                    txtBxNote.ForeColor = Color.Red;
                    _ = MessageBox.Show("於資料剖析階段發現問題無法匯入，請參考: " + fullName);
                    grpBx1.Enabled = true;
                    lblCounter.Visible = false;
                    return;

                }
                #endregion

                // 寫入資料庫
                try
                {
                    await import.UpdateDbAsync();
                    Doing = false;
                    txtBxNote.Text = "已經匯入";
                    txtBxNote.ForeColor = Color.Blue;
                    _ = MessageBox.Show("己經匯入");
                }
                catch (Exception ex)
                {
                    var path = _config!.LogDir;
                    var fullName = _config.FullLogFileName;
                    // 資料讀入階段..

                    var importLog = new ImportLog(path!, fullName);
                    await importLog.WriteFinalLogAsync("資料匯入階段", ex);
                    Doing = false;
                    txtBxNote.Text = fullName;
                    txtBxNote.ForeColor = Color.Red;
                    _ = MessageBox.Show("資料匯入階段發生問題，請參考: " + fullName);
                }

                grpBx1.Enabled = true;
                lblCounter.Visible = false;
            }
             );

        }


        private async void btnExport_Click(object sender, EventArgs e)
        {
            Doing = true;
            lblCounter.Visible = true;
            var th = new Thread(DoImportOrExport);
            th.Start();
            await Task.Run(async () =>
             {
                 grpBx1.Enabled = false;
                 try
                 {

                     var customerId = (CmbxCustomerId.Text.Split(new string[] { CustomerIdNameSeparator }, StringSplitOptions.RemoveEmptyEntries)[0]).ToGuid();
                     var exportDir = _config!.ExportDir;
                     var exportFileName = _config.GetExportFileName(customerId);
                     var expoprt = new ExportFile(customerId, exportFileName);
                     if (!Directory.Exists(exportDir))
                     {
                         _ = Directory.CreateDirectory(exportDir!);
                     }

                     using (var db = DBReader.CreateDB())
                     {
                         await expoprt.SetFinalPocosFromDbAsync(db);
                     }
                     expoprt.SetOriginalPocoFromFinalPoco();
                     await expoprt.SetExportFileFromOriginalPoco();
                     Doing = false;
                     txtBxNote.Text = exportFileName;
                     txtBxNote.ForeColor = Color.Blue;
                     MessageBox.Show($"已經匯出，檔案存放於：{exportFileName}");

                 }
                 catch (Exception ex)
                 {
                     Doing = false;
                     txtBxNote.Text = ex.Message;
                     txtBxNote.ForeColor = Color.Red;
                     MessageBox.Show($"無法滙出檔案：{ex.Message}");
                 }
                 grpBx1.Enabled = true;
                 lblCounter.Visible = false;

             });

        }

        private void lblCounter_Click(object sender, EventArgs e)
        {

        }

        private void CmbxCustomerId_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void label2_Click(object sender, EventArgs e)
        {

        }
    }
}
