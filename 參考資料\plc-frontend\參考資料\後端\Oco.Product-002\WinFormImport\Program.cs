using System;
using System.Windows.Forms;
using System.Windows.Forms.Design;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Oco.Product_002.Model.DataBase.DbFactoreyMethod;
using TagImportAndExport;

namespace WinFormImport
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            AppContext. SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
            var services = Host.CreateApplicationBuilder(args).Services;
            

            _=services.AddSingleton<Config>();
            _ = services.AddTransient<ImportFile>();
            _ = services.AddTransient<MyForm>();


            var serviceProvider = services.BuildServiceProvider();
            var config = serviceProvider.GetService<Config>();
            
            DBReader.SetConntext(config.DbReaderText!);
            DBWriter.SetConntext(config.DbWriterText!);

            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.

            ApplicationConfiguration.Initialize();
            
            Application.Run(serviceProvider.GetRequiredService<MyForm>());

        }
    }
}