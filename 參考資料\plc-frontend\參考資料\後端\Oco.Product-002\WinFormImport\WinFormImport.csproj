﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Configurations>Debug;Release;Main</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="IGN_DatabaseText.Main.json" />
    <None Remove="IGN_DatabaseText.Release.json" />
    <None Remove="IGN_DatabaseText.Task.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="IGN_DatabaseText.Main.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="IGN_DatabaseText.Release.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="IGN_DatabaseText.Task.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Oco.Core\Oco.Core.csproj" />
    <ProjectReference Include="..\Oco.Product-002.Api\Oco.Product-002.Api.csproj" />
    <ProjectReference Include="..\Oco.Product-002.Model\Oco.Product-002.Model.csproj" />
    <ProjectReference Include="..\TagImportAndExport\TagImportAndExport.csproj" />
  </ItemGroup>

</Project>