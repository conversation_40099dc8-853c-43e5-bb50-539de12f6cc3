{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "D:/Product02/logs/WebApi/Api-.log", "rollingInterval": "Day", "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "LicenseChecker": {"IsTrialLicense": false, "ServiceUrl": "http://127.0.0.1:5115"}}