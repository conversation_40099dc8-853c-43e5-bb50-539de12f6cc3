{
  "AllowedHosts": "*",
  "Kestrel": {
    "Limits": {
      "//MaxRequestBodySize": "about 3G",
      "MaxRequestBodySize": 10737418240
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Default": "Information",
        "Microsoft.AspNetCore": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
      },
      {
        "Name": "File",
        "Args": {
          "path": "D:/Product02/logs/WebApi/Api-.log",
          "rollingInterval": "Day",
          "fileSizeLimitBytes": 52428800, // 50 MB
          "rollOnFileSizeLimit": true,
          "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
        }
      }
    ]
  },
  "LicenseChecker": {
    "IsTrialLicense": false,
    "ServiceUrl": "http://*************:5115"
  },
  "Tax": {
    "BusinessTaxRate": 0.05
  },
  "ServiceClients": {
    "ScheduleWorkerServiceUrl": "http://localhost:5127"
  }
}