{"runtimeOptions": {"tfm": "net8.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "8.0.0"}, {"name": "Microsoft.AspNetCore.App", "version": "8.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configProperties": {"System.Reflection.Metadata.MetadataUpdater.IsSupported": false, "System.Reflection.NullabilityInfoContext.IsSupported": true, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}