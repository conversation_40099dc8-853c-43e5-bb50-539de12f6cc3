{"format": 1, "restore": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj": {}}, "projects": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj", "projectName": "Oco.Core.Services", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.102.2, )"}, "FastEnum": {"target": "Package", "version": "[1.8.0, )"}, "MQTTnet": {"target": "Package", "version": "[4.3.7.1207, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Oco.FeeCalculator.Electricity": {"target": "Package", "version": "[0.0.5-alpha, )"}, "Oco.FeeCalculator.Water": {"target": "Package", "version": "[0.0.1-gamma, )"}, "Oco.ServiceUtility": {"target": "Package", "version": "[0.0.1, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}, "System.Linq.Async": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj", "projectName": "Oco.Core.Vars", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FastEnum": {"target": "Package", "version": "[1.8.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj", "projectName": "Oco.Core", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Oco.Core.Modbus": {"target": "Package", "version": "[0.9.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj", "projectName": "Oco.Identity", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"jose-jwt": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj", "projectName": "Oco.LicencesChecker", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}, "System.Linq.Async": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj", "projectName": "Oco.Message", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[112.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj", "projectName": "Oco.Product-002.Api.Poco", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FastEnum": {"target": "Package", "version": "[1.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj", "projectName": "Oco.Product-002.Api", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Identity\\Oco.Identity.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.LicencesChecker\\Oco.LicencesChecker.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api.Poco\\Oco.Product-002.Api.Poco.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Cronos": {"target": "Package", "version": "[0.8.4, )"}, "DateOnlyTimeOnly.AspNet.Swashbuckle": {"target": "Package", "version": "[2.1.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.20.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Oco.Core.Modbus": {"target": "Package", "version": "[0.9.2, )"}, "Oco.Desigocc.Share": {"target": "Package", "version": "[1.0.4, )"}, "RestSharp": {"target": "Package", "version": "[112.0.0, )"}, "Scrutor": {"target": "Package", "version": "[4.2.2, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj", "projectName": "Oco.Product-002.Model", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.5, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj", "projectName": "Oco.Repositories", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FastEnum": {"target": "Package", "version": "[1.8.0, )"}, "System.Linq.Async": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj", "projectName": "TagImportAndExport", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj", "projectName": "WinFormImport", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Api\\Oco.Product-002.Api.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}, "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj": {"projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}