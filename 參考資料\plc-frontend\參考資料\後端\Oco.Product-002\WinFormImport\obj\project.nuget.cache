{"version": 2, "dgSpecHash": "qrL8IwTsPpQ=", "success": false, "projectFilePath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1302", "level": "Error", "message": "您正在使用 'HTTP' 來源執行 'restore' 作業: http://192.168.1.154:14235/v3/index.json。NuGet 需要 HTTPS 來源。若要使用 HTTP 來源，您必須在 NuGet.Config 檔案中將 'allowInsecureConnections' 明確設定為 true。參閱 https://aka.ms/nuget-https-everywhere 以取得更多資訊。", "projectPath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj", "filePath": "D:\\plc-Syetem\\參考資料\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\WinFormImport\\WinFormImport.csproj", "targetGraphs": []}]}