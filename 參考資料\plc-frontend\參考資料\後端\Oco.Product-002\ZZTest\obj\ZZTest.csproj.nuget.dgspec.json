{"format": 1, "restore": {"D:\\_Prj產品Oco.Product-002\\SourceCode\\BackEnd\\Oco.Product-002\\ZZTest\\ZZTest.csproj": {}}, "projects": {"D:\\_Prj產品Oco.Product-002\\SourceCode\\BackEnd\\Oco.Product-002\\ZZTest\\ZZTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\_Prj產品Oco.Product-002\\SourceCode\\BackEnd\\Oco.Product-002\\ZZTest\\ZZTest.csproj", "projectName": "ZZTest", "projectPath": "D:\\_Prj產品Oco.Product-002\\SourceCode\\BackEnd\\Oco.Product-002\\ZZTest\\ZZTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\_Prj產品Oco.Product-002\\SourceCode\\BackEnd\\Oco.Product-002\\ZZTest\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\temp": {}, "http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.101\\RuntimeIdentifierGraph.json"}}}}}